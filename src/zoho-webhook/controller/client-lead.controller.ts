import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { RolesGuard } from 'src/auth/roles.guard';
import { AuthGuard } from '@nestjs/passport';
import { ClientLeadListDto } from '../dto/client-list.dto';
import { ClientLeadService } from '../services/client-lead.service';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';

@ApiTags("client lead")
@ApiBearerAuth()
@Controller('client-lead')
export class clientLeadController {
    constructor(private readonly clientLeadService: ClientLeadService) { }
    @Post('/fetch-list')
    @ApiOperation({ summary: "Fetch the client lead" })
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION)
    async fetchClientLead(@Body() clientLeadListDto: ClientLeadListDto) {
        const response = await this.clientLeadService.clientLeadList(clientLeadListDto);
        return response

    }
    @Get('/fetch-client-detail/:id')
    @ApiOperation({ summary: "Fetch the client lead detail" })
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION)
    async fetchClientDetail(@Param('id') clientLeadId: string) {
        const response = await this.clientLeadService.clientLeadDetail(clientLeadId);
        return response;
    }
    @Post('/convert-lead-to-client')
    @ApiOperation({ summary: "conver the lead to the client" })
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION)
    async convertLeadToClient() {
        const response = await this.clientLeadService.convertLeadToClient();
        return response;
    }
    @Get('/fetch-client-minor/:id')
    @ApiOperation({ summary: "Fetch the client unAdded Minor" })
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION)
    async fetchClientMinorList(@Param('id') clientLeadId: string) {
        console.log(clientLeadId)
        const response = await this.clientLeadService.clientMinorList(clientLeadId);
        return response;
    }
}

import { Injectable, BadRequestException, NotFoundException, forwardRef, Inject } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, PipelineStage, Types } from "mongoose";
import { CLIENT_FIELD_ENUM } from "src/zoho-webhook/enum/client.enum";
import { ClientLead } from "src/zoho-webhook/schema/zoho-webhook.schema";
import { parsePhoneNumberFromString } from 'libphonenumber-js';
import { UploadService } from "src/utils/services/upload.service";

interface MobileDetails {
    countryCode: string;
    number: string;
}
@Injectable()
export class ZohowebhookService {
    constructor(
        @InjectModel(ClientLead.name) private readonly leadModel: Model<ClientLead>,
        private UploadService: UploadService,

    ) { }

    async createWebhook(webhookData: any, file: any, organizationId: string, facilityId: string): Promise<any> {
        try {
            const data = this.removeEmptyFields(webhookData);
            const clientData = this.mapClientFields(data);
            const { countryCode, number } = this.extractMobileDetails(clientData.mobile);
            const subClientDataArray = this.extractMinorData(data);
            const leadPayload = {
                organizationId,
                facilityId,
                firstName: clientData.firstName,
                lastName: clientData.lastName,
                email: clientData.email,
                phone: clientData.mobile,
                dob:clientData.dob,
                address: {
                    street: data.client_address_street,
                    addressLine1: data.client_address_addressLine1,
                    city: data.client_address_city,
                    state: data.client_address_state,
                    country: data.client_address_country,
                },
                businessAddress: {
                    street: clientData.business_address_street,
                    addressLine1: clientData.business_address_addressLine1,
                    city: clientData.business_address_city,
                    state: clientData.business_address_state,
                    country: clientData.business_address_country,
                },
                mobileDetails: { countryCode, number },
                minors: subClientDataArray,
                rawZohoData: data,
            };
            const savedLead = await this.leadModel.create(leadPayload);
            if (file) {
                const uploadResponse = await this.uploadSignature(file, { leadId: savedLead._id })
                await this.leadModel.findByIdAndUpdate(savedLead._id, { signature: uploadResponse.signatureUrl }, { new: true });
            }
            return {
                message: 'Lead created successfully',
                data: savedLead,
            };

        } catch (error) {
            console.error('Zoho Webhook Error:', error);
            throw new BadRequestException('Error processing Zoho webhook');
        }
    }


    removeEmptyFields(obj: Record<string, any>): Record<string, any> {
        return Object.entries(obj).reduce((acc, [key, value]) => {
            if (
                value !== null &&
                value !== undefined &&
                value !== '' &&
                !(typeof value === 'object' && Object.keys(this.removeEmptyFields(value)).length === 0)
            ) {
                acc[key] = typeof value === 'object' && !Array.isArray(value)
                    ? this.removeEmptyFields(value)
                    : value;
            }
            return acc;
        }, {} as Record<string, any>);
    }
    mapClientFields(cleanedPayload: Record<string, any>) {
        const client: Record<string, any> = {};

        Object.entries(CLIENT_FIELD_ENUM).forEach(([key, fieldKey]) => {
            if (cleanedPayload[fieldKey]) {
                client[key] = cleanedPayload[fieldKey];
            }
        });

        return client;
    }


    extractMobileDetails(mobile: string): MobileDetails {
        const phoneNumber = parsePhoneNumberFromString(mobile);

        if (!phoneNumber || !phoneNumber.isValid()) {
            throw new Error('Invalid phone number');
        }

        return {
            countryCode: `+${phoneNumber.countryCallingCode}`,
            number: phoneNumber.nationalNumber,
        };
    }
    extractMinorData(cleanedPayload: Record<string, any>): any[] {
        const minorMap: Record<number, any> = {};

        for (const [key, value] of Object.entries(cleanedPayload)) {
            const match = key.match(/^sub_client_(\d+)_([A-Za-z]+)$/i) || key.match(/^sub_client_(\d+)_([A-Za-z]+)$/i);
            if (match) {
                const index = parseInt(match[1], 10) - 1;
                const field = match[2];

                if (!minorMap[index]) minorMap[index] = {};
                minorMap[index][field.charAt(0).toLowerCase() + field.slice(1)] = value;
            }
        }

        return Object.values(minorMap);
    }
    async uploadSignature(file: Express.Multer.File, data: { leadId: string }) {
        try {
            const s3Path = "signature/";
            const fileExtension = file.originalname.split('.').pop();
            const fileName = `signature-${data.leadId}.${fileExtension}`;

            if (!file || !file.buffer || !file.mimetype.startsWith('image/')) {
                throw new Error("Invalid or missing image file");
            }

            const imageBuffer = file.buffer;

            const s3UploadResponse = await this.UploadService.uploadPdf(
                imageBuffer,
                s3Path,
                fileName,
                file.mimetype
            );
            return {
                message: "Signature uploaded successfully",
                signatureUrl: s3UploadResponse.Location,
            };
        } catch (error) {
            console.error("❌ Error uploading image signature:", error.message);
            throw new Error("Image signature upload failed");
        }
    }


}
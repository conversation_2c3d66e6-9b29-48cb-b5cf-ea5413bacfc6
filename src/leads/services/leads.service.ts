import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Lead } from '../schemas/lead.schema';
import { CreateLeadDto } from '../dtos/create-lead.dto';
import { MasterLeadService } from '../../masterLead/services/master-lead.service';

@Injectable()
export class LeadsService {
  constructor(
    @InjectModel(Lead.name) private leadModel: Model<Lead>,
    private readonly masterLeadService: MasterLeadService,
  ) {}

  async create(dto: CreateLeadDto): Promise<{ lead: Lead; masterLead: any }> {
    const lead = await this.leadModel.create({ data: dto.data });

    const mappedMasterLead = this.mapToMasterLead(dto.data);
    const masterLead = await this.masterLeadService.create(mappedMasterLead);

    return { lead, masterLead };
  }

  async findAll(): Promise<Lead[]> {
    return this.leadModel.find().sort({ createdAt: -1 }).exec();
  }

  private mapToMasterLead(data: Record<string, any>) {
    const fieldPatterns = {
      firstName: [/^first.?name$/i, /^f.?name$/i, /^your.?name$/i, /^name$/i, /^given.?name$/i],
      lastName: [/^last.?name$/i, /^l.?name$/i, /^surname$/i, /^family.?name$/i, /^lname$/i],
      fullName: [/^full.?name$/i, /^fullname$/i],
      mobile: [/mobile/i, /phone/i, /telephone/i, /cell/i, /contact.?number/i, /mobile.?number/i],
      email: [/email/i, /e-?mail/i, /mail.?id/i, /contact.?email/i, /email.?address/i],
    };
  
    const masterLeadData: any = {};
  
    // First, handle all fields except fullname
    Object.entries(fieldPatterns).forEach(([masterKey, regexPatterns]) => {
      if (masterKey === 'fullName') return; // skip fullname initially
      for (const [key, value] of Object.entries(data)) {
        if (regexPatterns.some((pattern) => pattern.test(key))) {
          masterLeadData[masterKey] = value;
          break;
        }
      }
    });
  
    // Handle fullname explicitly if firstName or lastName is missing
    if (!masterLeadData.firstName || !masterLeadData.lastName) {
      for (const [key, value] of Object.entries(data)) {
        if (fieldPatterns.fullName.some((pattern) => pattern.test(key))) {
          const names = value.split(' ').filter(n => n.trim());
          if (names.length >= 2) {
            if (!masterLeadData.firstName) masterLeadData.firstName = names[0];
            if (!masterLeadData.lastName) masterLeadData.lastName = names.slice(1).join(' ');
          } else if (names.length === 1 && !masterLeadData.firstName) {
            masterLeadData.firstName = names[0];
          }
          break;
        }
      }
    }
  
    // Explicitly set source as 'website'
    masterLeadData['source'] = 'website';
  
    // console.log('Explicitly mapped data:', masterLeadData);
  
    return masterLeadData;
  }
  
  
  
}

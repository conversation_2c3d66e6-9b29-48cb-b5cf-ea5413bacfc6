import { BadRequestException, Body, Controller, Delete, Get, HttpCode, HttpException, HttpStatus, Param, Patch, Post, Query, UseGuards } from "@nestjs/common";
import { OrganizationService } from "../services/organization.service";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { Roles } from "src/auth/decorators/roles.decorator";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { OrganizationDto } from "../dto/organization.dto";
import { OrganizationListDto } from "../dto/organization-list.dto";
import { OrganizationDetailsDto } from "../dto/organization-details.dto";
import { OrganizationSettingsDto } from "../dto/organization-settings.dto";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { AddServiceCategoryDto } from "../dto/add-service-category.dto";
import { UpdateServiceCategoryDto } from "../dto/update-service-category.dto";
import { AppointmentTypesDto } from "../dto/appointment-types.dto";
import { UpdateAppointmentTypesDto } from "../dto/update-appointment-types.dto";
import { AssignPricingDto } from "../dto/assign-pricing.dto";
import { ServicesListDto } from "../dto/services-list.dto";
import { UpdateOrganizationDto } from "../dto/update-organization.dto";
import { UpdateAppointmentTypeStatusDto } from "src/organization/dto/update-appointment-type-status.dto";
import { DeleteAppointmentTypesDto } from "src/organization/dto/delete-appointment-types.dto";
import { SendMailDto } from "../dto/sendMail.dto";
import { ChangeStatusDto } from "../dto/change-status.dto";
import { RemovePricingDto } from "../dto/remove-pricing.dto";
import { GroupServiceSubtypeDTO } from "../dto/groupSubtype-service.dto";
import { ServicesFromPackageDto } from "src/organization/dto/services-from-package.dto";
import { AddRevenueCategoryDto } from "../dto/add-revenue-category.dto";
import { UpdateRevenueCategoryDto } from "../dto/update-revenue-category.dto";
import { RevenueCategoryListDto } from "../dto/revenue-category-list.dto";
import { RevenueCategoryStatusDto } from "../dto/revenue-category-status.dto";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { PolicyAbilityProtected, PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { GetOrganizationId } from "../decorators/organization.decorator";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { MongoIdPipe } from "src/common/database/pipes/mongo-id.pipe";
import { Response, ResponsePaging } from "src/common/response/decorators/response.decorator";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";

@ApiTags("Organizations")
@ApiBearerAuth()
@Controller("organization")
export class OrganizationController {
    constructor(
        private organizationService: OrganizationService,
        private paginationService: PaginationService,
    ) { }

    @Post("/register")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Register a new Organization" })
    async registerOrganization(@Body() createOrganizationDto: OrganizationDto): Promise<any> {
        return await this.organizationService.registerOrganization(createOrganizationDto);
    }

    @Post("/list")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Organizations listing" })
    async organizationList(@Body() organizationListDto: OrganizationListDto): Promise<any> {
        return await this.organizationService.organizationList(organizationListDto);
    }

    @Post("/details")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Organizations details" })
    async organizationDetails(@Body() organizationDetailsDto: OrganizationDetailsDto): Promise<any> {
        return await this.organizationService.organizationDetails(organizationDetailsDto);
    }

    @Patch("/update/:organizationId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Organizations details update" })
    async organizationUpdate(@Param("organizationId") organizationId: string, @Body() organizationUpdateDto: UpdateOrganizationDto): Promise<any> {
        return await this.organizationService.organizationUpdate(organizationUpdateDto, organizationId);
    }

    @Patch("/status/:organizationId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Organizations status update" })
    async organizationStatusUpdate(@Param("organizationId") organizationId: string, @Body() changeStatusDto: ChangeStatusDto): Promise<any> {
        let data = await this.organizationService.organizationStatusUpdate(organizationId, changeStatusDto);
        return {
            message: "Organization status updated",
            data: data,
        };
    }

    @Patch("/updateSettings")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Organizations details update" })
    async organizationSettingsUpdate(@GetUser() user, @Body() organizationSettingsDto: OrganizationSettingsDto): Promise<any> {
        let organizationId = user._id;
        return await this.organizationService.organizationSettingsUpdate(organizationSettingsDto, organizationId);
    }

    @Get("/settings/:organizationId")
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Organization settings" })
    async organizationSettings(@Param("organizationId") organizationId: string): Promise<any> {
        return await this.organizationService.organizationSettings(organizationId);
    }

    @Post("/add/services")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.ORGANIZATION_SERVICE_WRITE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Add organization service category" })
    async addOrganizationServices(
        @GetUser() user,
        @Body() addServiceCategoryDto: AddServiceCategoryDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        // let role = user.role;
        // let data = "";
        // switch (role.type) {
        //     case ENUM_ROLE_TYPE.ORGANIZATION:
        const data = await this.organizationService.addServicesByOrg(addServiceCategoryDto, organizationId);
        //     break;
        // case ENUM_ROLE_TYPE.WEB_MASTER:

        // data = await this.organizationService.addServicesByWebMaster(addServiceCategoryDto, user._id);
        //     break;
        // default:
        //     throw new BadRequestException("Invalid role");
        // }
        return {
            message: "Service category added successfully",
            data: data,
        };
    }

    @Post("/services/list")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.ORGANIZATION_SERVICE_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Organization service category list" })
    async organizationServicesList(
        @GetUser() user,
        @Body() servicesListDto: ServicesListDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        // let data = "";
        // const { role } = user;
        // if (role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
        const data = await this.organizationService.servicesListByOrg(servicesListDto, organizationId.toString());
        // }
        // else if(role.type === ENUM_ROLE_TYPE.USER){
        //     data = await this.organizationService.servicesListByUser(servicesListDto, user._id);
        // }
        // else {
        //     data = await this.organizationService.servicesListByStaff(servicesListDto, user._id);
        // }
        return {
            message: "Service category list",
            data: data,
        };
    }

    @Post("/active-services/list")
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Organization service category list" })
    async organizationActiveServicesList(
        @GetUser() user, @Body()
        servicesListDto: ServicesListDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        // let data = "";
        // const { role: { type: roleType } } = user;
        // if (roleType === ENUM_ROLE_TYPE.ORGANIZATION) {
        const data = await this.organizationService.activeServicesListByOrg(servicesListDto, organizationId.toString());
        // }
        // else if(roleType === ENUM_ROLE_TYPE.USER){
        //     data = await this.organizationService.activeServicesListByUser(servicesListDto, user._id);
        // }
        // else {
        //     data = await this.organizationService.activeServicesListByStaff(servicesListDto, user._id);
        // }
        return {
            message: "Service category list",
            data: data,
        };
    }

    @Get("/services/:serviceId")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.ORGANIZATION_SERVICE_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get details of organization service category" })
    async getOrganizationServices(@Param("serviceId") serviceId: string): Promise<any> {
        let data = await this.organizationService.serviceDetails(serviceId);
        if (!data) throw new BadRequestException("Details not found");
        return {
            message: "Service category details",
            data: data,
        };
    }

    @Patch("/edit/services")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.ORGANIZATION_SERVICE_UPDATE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Edit organization service category" })
    async editOrganizationServices(
        @GetUser() user,
        @Body() updateServiceCategoryDto: UpdateServiceCategoryDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        // let role = user.role;
        // let data = "";
        // switch (role.type) {
        //     case ENUM_ROLE_TYPE.ORGANIZATION:
        const data = await this.organizationService.updateServicesByOrg(updateServiceCategoryDto, organizationId.toString());
        //         break;
        //     case ENUM_ROLE_TYPE.WEB_MASTER:
        //         data = await this.organizationService.updateServicesByWebMaster(updateServiceCategoryDto, user._id);
        //         break;
        //     default:
        //         throw new BadRequestException("Invalid role");
        // }
        return {
            message: "Service category updated successfully",
            data: data,
        };
    }

    @Post("/add/services/appointmentType")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.ORGANIZATION_SERVICE_WRITE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Add organization service category appointment type" })
    async addServicesAppointment(
        @GetUser() user,
        @Body() addAppointmentTypeDto: AppointmentTypesDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        // let role = user.role;
        // let data = "";
        // switch (role.type) {
        //     case ENUM_ROLE_TYPE.ORGANIZATION:
        const data = await this.organizationService.addAppointmentTypeByOrg(addAppointmentTypeDto, organizationId.toString());
        //         break;
        //     case ENUM_ROLE_TYPE.WEB_MASTER:
        //         data = await this.organizationService.addAppointmentTypeByWebMaster(addAppointmentTypeDto, user._id);
        //         break;
        //     default:
        //         throw new BadRequestException("Invalid role");
        // }
        return {
            message: "Appointment type added successfully",
            data: data,
        };
    }

    @Get("/services/:serviceId/appointmentType/:appointmentId")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.ORGANIZATION_SERVICE_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get details of appointment type of organization service category" })
    async getAppointmentType(@Param("serviceId") serviceId: string, @Param("appointmentId") appointmentId: string): Promise<any> {
        let data = await this.organizationService.appointmentTypeDetails(serviceId, appointmentId);
        if (!data) throw new BadRequestException("Details not found");
        return {
            message: "Appointment type details",
            data: data,
        };
    }

    @Patch("/edit/services/appointmentType")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.ORGANIZATION_SERVICE_UPDATE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update organization service category appointment type" })
    async updateServicesAppointment(
        @GetUser() user,
        @Body() updateAppointmentTypeDto: UpdateAppointmentTypesDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        // let role = user.role;
        // let data = "";
        // switch (role.type) {
        // case ENUM_ROLE_TYPE.ORGANIZATION:
        const data = await this.organizationService.updateAppointmentTypeByOrg(updateAppointmentTypeDto, organizationId.toString());
        //         break;
        //     case ENUM_ROLE_TYPE.WEB_MASTER:
        //         data = await this.organizationService.updateAppointmentTypeByWebMaster(updateAppointmentTypeDto, user._id);
        //         break;
        //     default:
        //         throw new BadRequestException("Invalid role");
        // }
        return {
            message: "Appointment type updated successfully",
            data: data,
        };
    }

    @Patch("/updateStatus/services/appointmentType")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.ORGANIZATION_SERVICE_UPDATE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update status organization service category appointment type" })
    async updateServicesAppointmentStatus(
        @GetUser() user,
        @Body() updateAppointmentTypeStatusDto: UpdateAppointmentTypeStatusDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        // let role = user.role;
        // let data = "";
        // switch (role.type) {
        //     case ENUM_ROLE_TYPE.ORGANIZATION:
        const data = await this.organizationService.updateAppointmentTypeStatusByOrg(updateAppointmentTypeStatusDto, organizationId.toString());
        //         break;
        //     case ENUM_ROLE_TYPE.WEB_MASTER:
        //         data = await this.organizationService.updateAppointmentTypeStatusByWebMaster(updateAppointmentTypeStatusDto, user._id);
        //         break;
        //     default:
        //         throw new BadRequestException("Invalid role");
        // }
        return {
            message: "Appointment type status updated successfully",
            data: data,
        };
    }

    @Patch("/delete/services/appointmentType")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.ORGANIZATION_SERVICE_DELETE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Delete appointment type" })
    async deleteAppointmentType(
        @GetUser() user,
        @Body() deleteAppointmentTypeStatusDto: DeleteAppointmentTypesDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        // let role = user.role;
        // let data = "";
        // switch (role.type) {
        //     case ENUM_ROLE_TYPE.ORGANIZATION:
        const data = await this.organizationService.deleteAppointmentTypeByOrg(deleteAppointmentTypeStatusDto, organizationId.toString());
        //     break;
        // case ENUM_ROLE_TYPE.WEB_MASTER:
        //     data = await this.organizationService.deleteAppointmentTypeByWebMaster(deleteAppointmentTypeStatusDto, user._id);
        //     break;
        // default:
        //     throw new BadRequestException("Invalid role");
        // }
        return {
            message: "Appointment type deleted successfully",
            data: data,
        };
    }

    @Post("/sendMail")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Resend mail to an Organization" })
    async sendMail(@Body() sendMailDto: SendMailDto): Promise<any> {
        return await this.organizationService.sendMail(sendMailDto);
    }

    @Post("/services/assign-pricing")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.ORGANIZATION_SERVICE_UPDATE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Assign pricing to service category of organization" })
    async addPricingServiceCategory(
        @GetUser() user,
        @Body() assignPricingDto: AssignPricingDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        // let role = user.role;
        // let data = "";
        // switch (role.type) {
        //     case ENUM_ROLE_TYPE.ORGANIZATION:
        const data = await this.organizationService.assignPricingByOrg(assignPricingDto, organizationId.toString());
        //         break;
        //     case ENUM_ROLE_TYPE.WEB_MASTER:
        //         data = await this.organizationService.assignPricingByStaff(assignPricingDto, user._id);
        //         break;
        //     default:
        //         throw new BadRequestException("Invalid role");
        // }
        return {
            message: "Pricing assigned to service category",
            data: data,
        };
    }

    @Post("/services/remove-assigned-pricing")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.ORGANIZATION_SERVICE_UPDATE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Remove assigned pricing" })
    async removeAssignedPricing(
        @GetUser() user,
        @Body() removePricingDto: RemovePricingDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        // let role = user.role;
        // let data = "";
        // switch (role.type) {
        //     case ENUM_ROLE_TYPE.ORGANIZATION:
        const data = await this.organizationService.removeAssignedPricingByOrg(removePricingDto, organizationId.toString());
        //         break;
        //     case ENUM_ROLE_TYPE.WEB_MASTER:
        //         data = await this.organizationService.removeAssignedPricingByStaff(removePricingDto, user._id);
        //         break;
        //     default:
        //         throw new BadRequestException("Invalid role");
        // }
        return {
            message: "Pricing removed from Service category",
            data: data,
        };
    }

    @Post("/v1/active-services/list")
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Organization service category list" })
    async activeServicesList(@GetUser() user, @Body() serviceType): Promise<any> {
        let serviceTypePayload = serviceType.reqData
        let data = "";
        if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            data = await this.organizationService.activeServicesListByOrgV1(serviceTypePayload, user._id);
        }
        // else if(user.role.type === ENUM_ROLE_TYPE.USER){
        //     data = await this.organizationService.activeServicesListByUserV1(serviceType, user._id);
        // }
        else {
            data = await this.organizationService.activeServicesListByStaffV1(serviceType, user._id);
        }
        return {
            data: data,
        };
    }

    @Patch("/services/:id/update-featured-status")
    @HttpCode(HttpStatus.ACCEPTED)
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update service featured status" })
    async updateFeaturedStatus(@GetUser() user, @Body() changeStatusDto: ChangeStatusDto, @Param("id") id: string): Promise<any> {
        await this.organizationService.updateFeaturedStatusByOrg(id, changeStatusDto, user);
        return {
            message: "Featured status updated successfully",
            data: true,
        };
    }

    @Get("/services/:organizationId/featured-list")
    @ApiOperation({ summary: "Get service featured list" })
    async featuredList(@Param("organizationId") organizationId: string): Promise<any> {
        let data = await this.organizationService.featuredList(organizationId);
        return {
            message: "Featured list fetched successfully",
            data: data,
        };
    }

    @Post("/list/groupSubTypeByService")
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "This api will fetch the list of active SubType and Services" })
    async groupSubTypeByService(@GetUser() user, @Body() serviceType: GroupServiceSubtypeDTO): Promise<any> {
        let data = await this.organizationService.groupSubTypeByService(serviceType, user);
        return {
            message: "Group sub type list fetched successfully",
            data: data,
        };
    }

    @Post("/list/servicesFromPackage")
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "This api will fetch the list of active SubType and Services" })
    async servicesFromPackage(@GetUser() user, @Body() serviceType: ServicesFromPackageDto): Promise<any> {
        let data = await this.organizationService.ServicesFromPackage(serviceType, user);
        return {
            message: "Services list fetched successfully",
            data: data,
        };
    }

    @Post("app/active-services/list")
    @ApiOperation({ summary: "Organization service category list" })
    async activeServicesListApp(@Body() servicesListDto: ServicesListDto): Promise<any> {
        let orgId = servicesListDto?.organizationId
        if (!orgId) {
            throw new HttpException("Organization id is required", HttpStatus.BAD_REQUEST);
        }
        let data = "";
        data = await this.organizationService.activeServicesListByOrg(servicesListDto, orgId);
        return {
            message: "Service category list",
            data: data,
        };
    }

    // Revenue Category Endpoints
    @Post("/revenue-category/create")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Add a new revenue category" })
    async addRevenueCategory(@GetOrganizationId() organizationId: IDatabaseObjectId, @Body() addRevenueCategoryDto: AddRevenueCategoryDto): Promise<any> {
        return await this.organizationService.addRevenueCategory(addRevenueCategoryDto, organizationId);
    }

    @Response("Revenue category updated successfully")
    @Patch("/revenue-category/:id/update")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update a revenue category" })
    async updateRevenueCategory(
        @GetUser() user, @Param("id", MongoIdPipe) id: IDatabaseObjectId,
        @Body() updateRevenueCategoryDto: UpdateRevenueCategoryDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        // Ensure the ID in the DTO matches the ID in the URL
        if (!id) {
            throw new BadRequestException("Revenue category is required");
        }
        const data = await this.organizationService.updateRevenueCategory(id, organizationId, updateRevenueCategoryDto);
        return {
            data: data,
        };
    }

    @Response("Revenue category retrieved successfully")
    @Get("/revenue-category/:id/get")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get a revenue category by ID" })
    async getRevenueCategoryById(@GetUser() user, @Param("id") id: string): Promise<any> {
        return await this.organizationService.getRevenueCategoryById(id, user);
    }

    @Response("Revenue categories retrieved successfully")
    @Post("/revenue-category/list")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get a list of revenue categories with filters" })
    async getRevenueCategoryList(
        @GetUser() user,
        @Body() revenueCategoryListDto: RevenueCategoryListDto
    ): Promise<any> {
        return await this.organizationService.getRevenueCategoryList(revenueCategoryListDto, user);
    }

    @Response("Revenue category status updated successfully")
    @Patch("/revenue-category/:id/status")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected() @ApiOperation({ summary: "Update the status of a revenue category" })
    async updateRevenueCategoryStatus(@GetUser() user, @Param("id") id: string, @Body() statusDto: RevenueCategoryStatusDto): Promise<any> {
        return await this.organizationService.updateRevenueCategoryStatus(id, statusDto, user);
    }

    @Response("Revenue category status updated successfully")
    @Delete("/revenue-category/:id/delete")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected() @ApiOperation({ summary: "Update the status of a revenue category" })
    async deleteRevenueCategoryStatus(@GetUser() user, @Param("id") id: string): Promise<any> {
        return await this.organizationService.deleteRevenueCategoryStatus(id, user);
    }
}

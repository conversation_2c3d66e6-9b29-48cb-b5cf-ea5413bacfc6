import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsArray, IsBoolean, IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString, Max, Min, ValidateIf, ValidateNested } from "class-validator";
import { Types } from "mongoose";
import { ActiveTimeFrameDto } from "src/utils/dto/active-time-frame.dto";
import { ClassType } from "src/utils/enums/class-type.enum";
import { DiscountType } from "src/utils/enums/discount.enum";
import { DurationUnit } from "src/utils/enums/duration-unit.enum";
import { SessionType } from "src/utils/enums/session-type.enum";


export class DiscountDto {
    @ApiProperty({
        description: "Type of discount",
        enum: DiscountType,
        example: DiscountType.FLAT,
        required: false,
    })
    @IsOptional()
    @IsEnum(DiscountType, { message: "Invalid discount type" })
    type?: DiscountType;

    @ApiProperty({
        description: "Value of the discount",
        example: 50,
        required: false,
    })
    @IsOptional()
    @IsNumber({}, { message: "Invalid discount value" })
    @Min(0, { message: "Discount value cannot be less than 0" })
    value?: number;

    @ApiProperty({
        description: "Discounted by",
        example: "66cecb432351713ae4447a6b",
        required: false,
    })
    @IsOptional()
    discountedBy?: string;
}
export class ServicesDto {
    @ApiProperty({
        description: "Availability type for facility",
        enum: ClassType,
        example: ClassType.BOOKINGS,
        required: true
    })
    @IsEnum(ClassType, { message: "Invalid type" })
    type: ClassType;

    @ApiProperty({
        description: "The Id of the service category",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsMongoId({ message: "Required valid service category details" })
    serviceCategory: string;

    @ApiProperty({
        description: "Array of appointment type Ids of the service category",
        example: ["66cecb432351713ae4447a6b", "66cecb432351713ae4447a6c"],
        required: false,
    })
    @ValidateIf((req) => req.type === ClassType.PERSONAL_APPOINTMENT)
    @IsArray({ message: "Appointment types must be an array" })
    @IsMongoId({ each: true, message: "Each appointment type must be a valid ObjectId" })
    appointmentType?: string[];

    @ApiProperty({
        description: "Session type for the service in pricing",
        enum: SessionType,
        example: SessionType.SINGLE,
        required: true
    })
    @IsEnum(SessionType, { message: "Invalid session type" })
    sessionType: SessionType;

    @ApiProperty({
        description: "Session count | Must be a number",
        example: 29,
        required: false,
    })
    @ValidateIf((req) => req.sessionType === SessionType.MULTIPLE)
    @IsNumber({}, { message: "Invalid session count" })
    sessionCount?: Number;


    @ApiProperty({
        description: "Session Per Day | Must be a number",
        example: 29,
        required: false,
    })
    @ValidateIf((req) => req.sessionType === SessionType.UNLIMITED)
    @IsNumber({}, { message: "Invalid session Per Day Count" })
    @Type(() => Number)
    sessionPerDay?: Number;


    @ApiProperty({
        description: "Number of days you can consume unlimited sessions (only for DAY_PASS)",
        example: 2,
        required: false,
    })
    @ValidateIf((req) => req.sessionType === SessionType.DAY_PASS)
    @IsNumber({}, { message: "Invalid day pass limit" })
    @Min(1, { message: "Day pass limit must be at least 1" })
    dayPassLimit?: number;

    @ApiProperty({
        description: "Introductory offer | string",
        example: "Animal Flow Select",
        required: false,
    })
    @IsString({ message: "Invalid type of introductory offer" })
    @IsOptional()
    introductoryOffer?: string;

    @ApiProperty({
        description: "Revenue Category | String",
        example: "66cecb432351713ae4447a6b",
        required: false,
    })
    @IsString({ message: "Invalid type of Revenue Category" })
    @IsOptional()
    revenueCategory?: string;

    @ApiProperty({
        description: " Realtionship of service category and the sub type with the Pricing",
        example: [
            {
                serviceCategory: "66cecb432351713ae4447a6b",
                subTypeIds: ["66cecb432351713ae4447a6b", "66cecb432351713ae4447a6c"],
            },
        ],
        required: false,
    })
    @IsOptional()
    @IsArray({ message: "Relationship must be an array" })
    relationShip?: Array<{
        serviceCategory: string;
        subTypeIds: string[];
    }>;
}
export class CreatePricingDto {
    @ApiProperty({
        description: "The name of the pricing.",
        example: "Animal Flow Select",
        required: true,
    })
    @IsString({ message: "Invalid type of pricing name" })
    @IsNotEmpty({ message: "Pricing name cannot be empty" })
    name: string;

    @ApiProperty({
        description: "Price excluding GST | Must be a number",
        example: 299,
        required: true,
    })
    @IsNumber({}, { message: "Invalid price type" })
    price: Number;

    @ApiProperty({
        description: "Can be sold online or not | Boolean",
        example: false,
        required: true,
    })
    @IsBoolean({ message: "Invalid flag value for online indicator" })
    @Type(() => Boolean)
    isSellOnline: Boolean;

    @ApiProperty({
        description: "Tax on pricing | Must be a number",
        example: 12,
        required: true,
    })
    @IsNumber({}, { message: "Invalid tax type" })
    @Min(0, { message: "Tax percentage cannot be less than 0" })
    @Max(40, { message: "Tax percentage cannot be greater than 40" })
    tax: Number;

    @ApiProperty({
        description: "Expiry in days | Must be a number",
        example: 12,
        required: true,
    })
    @IsNumber({}, { message: "Invalid expiry date" })
    expiredInDays: number;

    @ApiProperty({
        description: "HSN/SAC code | Must be a string",
        example: "998314",
        required: false,
    })
    @ValidateIf((o) => o.tax > 0)
    @IsString({ message: "Invalid HSN/SAC code" })
    @IsNotEmpty({ message: "HSN/SAC code cannot be empty" })
    @IsOptional()
    hsnOrSacCode: string;

    @ApiProperty({
        description: "Expiration Unit",
        enum: DurationUnit,
        example: DurationUnit.DAYS,
    })
    @IsEnum(DurationUnit, { message: "Expiration unit is not valid" })
    durationUnit: DurationUnit;

    // @ApiProperty({
    //     description: "Membership included or not | Boolean",
    //     example: false,
    //     required: true,
    // })
    // @IsBoolean({ message: "Invalid value for membership inclusion" })
    // membershipIncluded: Boolean;

    @ApiProperty({
        description: "The Id of the service category",
        example: "66cecb432351713ae4447a6b",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Required valid service category details" })
    membershipId?: string;

    @ApiProperty({
        description: "Service details for this pricing",
        example: ServicesDto,
        required: true,
    })
    @ValidateNested({ message: "Invalid service object" })
    @Type(() => ServicesDto)
    @IsNotEmpty({ message: "Address is required" })
    services: ServicesDto;

    @ApiProperty({
        description: "Discount details for this pricing",
        required: false,
    })
    @ValidateNested({ message: "Invalid discount object" })
    @Type(() => DiscountDto)
    @IsOptional()
    discount?: DiscountDto;

    @ApiProperty({
        description: "Default Promotion Id",
        example: "66cecb432351713ae4447a6b",
        required: false,
    })
    @IsOptional()
    promotionId?: string;

    @ApiProperty({
        description: "Apply promotion to items",
        example: ["60d21b4667d0d8992e610c85", "60d21b4667d0d8992e610c86"],
        required: false,
        type: [String],
    })
    @IsArray()
    @IsOptional()
    applyPromotion?: string[];

    @ApiProperty({
        description: "Revenue Category",
        example: "Membership Included",
        required: false,
    })
    @IsOptional()
    revenueCategory?: string;

    @ApiProperty({
        description: "Active time frames define when this pricing is available for purchase in POS",
        type: [ActiveTimeFrameDto],
        required: false,
    })
    @IsOptional()
    @ValidateNested({ each: true })
    @Type(() => ActiveTimeFrameDto)
    activeTimeFrames?: ActiveTimeFrameDto[];
}






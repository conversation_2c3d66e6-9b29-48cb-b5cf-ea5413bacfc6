import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, PipelineStage, Types } from "mongoose";
import { OrganizationDocument, Organizations } from "../schemas/organization.schema";
import { TransactionService } from "src/utils/services/transaction.service";
import { GeneralService } from "src/utils/services/general.service";
import { OrganizationDto } from "../dto/organization.dto";
import { User, UserDocument } from "src/users/schemas/user.schema";
import { Attributes } from "src/attributes/schema/attribute.schema";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { OrganizationListDto } from "../dto/organization-list.dto";
import { OrganizationPipe } from "../pipes/organization.pipe";
import { OrganizationDetailsDto } from "../dto/organization-details.dto";
import { MailService } from "src/mail/services/mail.service";
import { OrganizationSettingsDto } from "../dto/organization-settings.dto";
import { Otp } from "src/auth/schemas/otp.schema";
import { AddServiceCategoryDto } from "../dto/add-service-category.dto";
import { Services } from "../schemas/services.schema";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { UpdateServiceCategoryDto } from "../dto/update-service-category.dto";
import { AppointmentTypesDto } from "../dto/appointment-types.dto";
import { UpdateAppointmentTypesDto } from "../dto/update-appointment-types.dto";
import { DeleteAppointmentTypesDto } from "../dto/delete-appointment-types.dto";
import { UpdateAppointmentTypeStatusDto } from "../dto/update-appointment-type-status.dto";
import { AssignPricingDto } from "../dto/assign-pricing.dto";
import { ServicesListDto } from "../dto/services-list.dto";
import { ServiceCategoryPricing } from "../schemas/service-category-pricing.schema";
import { UpdateOrganizationDto } from "../dto/update-organization.dto";
import { ClassType, ClassTypeAbbreviationPlural } from "src/utils/enums/class-type.enum";
import { AttributeType } from "src/utils/enums/attribute-type.enum";
import { Clients } from "src/users/schemas/clients.schema";
import { PayRate } from "src/staff/schemas/pay-rate.schema";
import { Pricing } from "src/organization/schemas/pricing.schema";
import { SendMailDto } from "../dto/sendMail.dto";
import { ChangeStatusDto } from "../dto/change-status.dto";
import { promises } from "dns";
import { RemovePricingDto } from "../dto/remove-pricing.dto";
import { GroupServiceSubtypeDTO } from "../dto/groupSubtype-service.dto";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { RoleService } from "src/role/services/role.service";
import { AddRevenueCategoryDto } from "../dto/add-revenue-category.dto";
import { UpdateRevenueCategoryDto } from "../dto/update-revenue-category.dto";
import { RevenueCategoryListDto } from "../dto/revenue-category-list.dto";
import { RevenueCategoryStatusDto } from "../dto/revenue-category-status.dto";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { Type } from 'class-transformer';
import { Scheduling } from "src/scheduling/schemas/scheduling.schema";
import moment from "moment";
import { ScheduleStatusType } from "src/scheduling/enums/schedule-status.enum";
import { ServicesFromPackageDto } from "src/organization/dto/services-from-package.dto";

@Injectable()
export class OrganizationService {
    readonly adminFrontEndHost = process.env.ADMIN_FRONTEND_APP_URL;

    constructor(
        @InjectModel(Organizations.name) private OrganizationModel: Model<Organizations>,
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(Otp.name) private OtpModel: Model<Otp>,
        @InjectModel(StaffProfileDetails.name) private StaffModel: Model<StaffProfileDetails>,
        @InjectModel(Services.name) private ServiceModel: Model<Services>,
        @InjectModel(Attributes.name) private AttributeModel: Model<Attributes>,
        @InjectModel(ServiceCategoryPricing.name) private ServicePricingModel: Model<ServiceCategoryPricing>,
        @InjectModel(Clients.name) private ClientModel: Model<Clients>,
        @InjectModel(PayRate.name) private PayRateModel: Model<PayRate>,
        @InjectModel(PayRate.name) private SchedulingModel: Model<Scheduling>,
        @InjectModel(Pricing.name) private Pricing: Model<Pricing>,
        private organizationPipes: OrganizationPipe,
        private readonly transactionService: TransactionService,
        private readonly generalService: GeneralService,
        private readonly mailService: MailService,
        private readonly roleService: RoleService,
    ) { }

    async findOneById(_id: Types.ObjectId | string, options?: any): Promise<OrganizationDocument> {
        return this.OrganizationModel.findOne({ _id: new Types.ObjectId(_id) }, options);
    }


    private async getOrganizationId(user: IUserDocument): Promise<string> {
        const { role } = user;
        let organizationId: string = null;

        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id.toString();
                break;

            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            case ENUM_ROLE_TYPE.TRAINER:
                const staffDetails = await this.StaffModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                ).lean();

                if (!staffDetails) {
                    throw new BadRequestException("Staff profile not found");
                }
                organizationId = staffDetails.organizationId.toString();
                break;

            case ENUM_ROLE_TYPE.USER:
                const clientDetails = await this.ClientModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                ).lean();

                if (!clientDetails) {
                    throw new BadRequestException("Client profile not found");
                }
                organizationId = clientDetails.organizationId.toString();
                break;

            default:
                throw new BadRequestException(`Invalid role type: ${role.type}`);
        }

        if (!organizationId) {
            throw new BadRequestException("Organization ID not found");
        }

        // Verify organization exists
        const organizationExists = await this.OrganizationModel.exists({ userId: organizationId });
        if (!organizationExists) {
            throw new NotFoundException("Organization not found");
        }

        return organizationId;
    }

    async registerOrganization(createOrganizationDto: OrganizationDto): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const organizationRole = await this.roleService.findOneByType(ENUM_ROLE_TYPE.ORGANIZATION, {
                select: "_id type name",
            });
            if (!organizationRole) throw new BadRequestException("Organization role not found");
            let userData = {
                firstName: createOrganizationDto.organizationName,
                lastName: "",
                newUser: true,
                mobile: createOrganizationDto.mobile,
                email: createOrganizationDto.email,
                role: organizationRole._id,
            };
            let userDetails = new this.UserModel(userData);
            await userDetails.save({ session });

            let organizationData = {
                userId: userDetails["_id"],
                logo: createOrganizationDto.logo,
                address: {
                    state: createOrganizationDto.address.stateId,
                    city: createOrganizationDto.address.cityId,
                    addressLine1: createOrganizationDto.address.addressLine1,
                    postalCode: createOrganizationDto.address.postalCode,
                },
                associatedPerson: createOrganizationDto.associatedPerson,
            };

            let orgDetails = new this.OrganizationModel(organizationData);
            await orgDetails.save({ session });
            let randomNumber = await this.generalService.randomOTP();
            let otpDetails = new this.OtpModel({
                otp: randomNumber,
                for: createOrganizationDto.email.toLowerCase(),
            });
            await otpDetails.save({ session });
            let dataForMail = {
                dashboardUrl: this.adminFrontEndHost + `set-password?uid=${randomNumber}&id=${userDetails["_id"]}&email=${userDetails.email.toLowerCase()}&role=${organizationRole.type}&firstName=${userData.firstName}&lastName=${userData.lastName}`,
                organizationName: createOrganizationDto.organizationName,
                username: createOrganizationDto.email,
            };

            this.mailService.sendMail({
                to: createOrganizationDto.email.toString(),
                subject: `Welcome to ${createOrganizationDto.organizationName} - Set Your New Password`,
                template: "organization-onboarding",
                context: dataForMail,
            });
            await this.transactionService.commitTransaction(session);
            return {
                message: "Organization created successfully",
                data: userDetails,
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async organizationList(organizationListDto: OrganizationListDto): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const pageSize = organizationListDto.pageSize ?? 10;
            const page = organizationListDto.page ?? 1;
            const skip = pageSize * (page - 1);
            let search = "";
            if (organizationListDto.search) {
                search = organizationListDto.search.trim().split(" ").join("|");
            }

            let filters = {};
            if (organizationListDto.stateId) {
                filters["organizationDetails.address.state"] = Types.ObjectId.createFromHexString(organizationListDto.stateId);
            }

            if (organizationListDto.cityId) {
                //convert into objectId
                const cityIdsAsObjectIds = organizationListDto.cityId.map((id: string) => new Types.ObjectId(id));

                // Apply the converted ObjectId array to the filter
                filters["organizationDetails.address.city"] = { $in: cityIdsAsObjectIds };
            }

            const organizationRole = await this.roleService.findOneByType(ENUM_ROLE_TYPE.ORGANIZATION, {
                select: "_id",
            });
            if (!organizationRole) throw new BadRequestException("Organization role not found");
            let pipeline = this.organizationPipes.organizationList(organizationRole._id, skip, pageSize, search, filters);
            let organizationsList = await this.UserModel.aggregate(pipeline);
            await this.transactionService.commitTransaction(session);
            return {
                message: "Organizations list",
                data: {
                    list: organizationsList[0]?.list,
                    count: organizationsList[0]?.total[0]?.total ? organizationsList[0]?.total[0]?.total : 0,
                },
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async organizationDetails(organizationDetailsDto: OrganizationDetailsDto): Promise<any> {
        let pipeline = this.organizationPipes.organizationDetails(organizationDetailsDto);
        let result = await this.UserModel.aggregate(pipeline);
        if (result.length === 0) throw new BadRequestException("Organization not found");
        return result[0];
    }

    async organizationUpdate(organizationUpdateDto: UpdateOrganizationDto, organizationId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const role = await this.roleService.findOneByType(ENUM_ROLE_TYPE.ORGANIZATION, {
                select: "_id",
            });
            if (!role) throw new BadRequestException("Organization role not found");
            let userData = {
                name: organizationUpdateDto.organizationName,
            };

            let updateData = await this.UserModel.findOneAndUpdate(
                {
                    _id: organizationId,
                    role: role._id,
                },
                {
                    $set: userData,
                },
                {
                    new: true,
                    session,
                },
            );
            if (!updateData) throw new NotFoundException("Organization not found");

            let updateOrg = await this.OrganizationModel.findOneAndUpdate(
                {
                    userId: organizationId,
                },
                {
                    $set: {
                        logo: organizationUpdateDto.logo,
                        address: {
                            state: organizationUpdateDto.address.stateId,
                            city: organizationUpdateDto.address.cityId,
                            addressLine1: organizationUpdateDto.address.addressLine1,
                            postalCode: organizationUpdateDto.address.postalCode,
                        },
                        associatedPerson: organizationUpdateDto.associatedPerson,
                    },
                },
                {
                    new: true,
                    session,
                },
            );

            if (!updateOrg) throw new BadRequestException("Organization not found");
            await this.transactionService.commitTransaction(session);
            return {
                message: "Organization updated successfully",
                data: updateOrg,
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async organizationSettingsUpdate(organizationSettingsDto: OrganizationSettingsDto, organizationId: string): Promise<any> {
        let orgPermissions = {
            clientOnboarding: organizationSettingsDto.clientOnboarding,
            staffOnboarding: organizationSettingsDto.staffOnboarding,
        };

        let updateSettings = await this.OrganizationModel.findOneAndUpdate(
            {
                userId: organizationId,
            },
            {
                $set: orgPermissions,
            },
            {
                new: true,
            },
        );
        if (!updateSettings) throw new BadRequestException("Organization not found");

        return {
            message: "Organization settings updated successfully",
            data: updateSettings,
        };
    }

    async organizationSettings(organizationId: string): Promise<any> {
        let settings = await this.OrganizationModel.findOne({ userId: organizationId }, { clientOnboarding: 1, staffOnboarding: 1 });
        if (!settings) throw new BadRequestException("Organization not found");
        return {
            message: "Organization settings",
            data: settings,
        };
    }

    async addServicesByOrg(addServiceCategoryDto: AddServiceCategoryDto, orgId: IDatabaseObjectId): Promise<any> {

        if (addServiceCategoryDto.isFeatured) {
            if (!addServiceCategoryDto.image) {
                throw new BadRequestException("Image is required for featuring a service");
            }
            await this.validateFeaturedItems(addServiceCategoryDto.classType, orgId.toString());
        }

        let data = {
            name: addServiceCategoryDto.name,
            classType: addServiceCategoryDto.classType,
            description: addServiceCategoryDto?.description,
            image: addServiceCategoryDto?.image,
            organizationId: orgId,
            createdBy: orgId,
            isFeatured: addServiceCategoryDto?.isFeatured,
            isActive: addServiceCategoryDto?.isActive
        };

        let serviceData = new this.ServiceModel(data);
        return await serviceData.save();
    }

    async addServicesByWebMaster(addServiceCategoryDto: AddServiceCategoryDto, staffId: string): Promise<any> {
        let staffDetails = await this.StaffModel.findOne({ userId: staffId }, { organizationId: 1 });
        if (!staffDetails) throw new BadRequestException("Staff not found");
        let orgId = staffDetails["organizationId"];
        let data = {
            name: addServiceCategoryDto.name,
            classType: addServiceCategoryDto.classType,
            description: addServiceCategoryDto?.description,
            image: addServiceCategoryDto?.image,
            organizationId: orgId,
            createdBy: staffId,
            isFeatured: addServiceCategoryDto?.isFeatured,
            isActive: addServiceCategoryDto?.isActive

        };

        if (data.isFeatured) {
            if (!data.image) {
                throw new BadRequestException("Image is required for featuring a service");
            }
            await this.validateFeaturedItems(data.classType, orgId);
        }

        let serviceData = new this.ServiceModel(data);
        return await serviceData.save();
    }

    async servicesListByOrg(servicesListDto: ServicesListDto, orgId: string): Promise<any> {
        const pageSize = servicesListDto.pageSize ?? 10;
        const page = servicesListDto.page ?? 1;
        const skip = pageSize * (page - 1);

        let query: any = {
            organizationId: orgId,
        };

        if (servicesListDto?.classType) {
            query["classType"] = servicesListDto.classType;
        }

        const countProm = this.ServiceModel.countDocuments(query);
        const listProm = this.ServiceModel.find(query)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(pageSize);

        const [list, count] = await Promise.all([listProm, countProm]);

        // const filteredList = list.map((service) => ({
        //     ...service.toObject(),
        //     appointmentType: service.appointmentType.filter((type) => type.isActive === true),
        // }));

        return {
            list: list,
            count: count,
        };
    }

    async servicesListByStaff(servicesListDto: ServicesListDto, staffId: string): Promise<any> {
        const pageSize = servicesListDto.pageSize ?? 10;
        const page = servicesListDto.page ?? 1;
        const skip = pageSize * (page - 1);

        const staffDetails = await this.StaffModel.findOne(
            { userId: staffId },
            { organizationId: 1 }
        );
        if (!staffDetails) throw new BadRequestException("Staff not found");
        const orgId = staffDetails.organizationId;

        let query: any = {
            organizationId: orgId,
        };

        if (servicesListDto?.classType) {
            query["classType"] = servicesListDto.classType;
        }

        const countProm = this.ServiceModel.countDocuments(query);
        const listProm = this.ServiceModel.find(query)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(pageSize);

        const [list, count] = await Promise.all([listProm, countProm]);

        // const filteredList = list.map((service) => ({
        //     ...service.toObject(),
        //     appointmentType: service.appointmentType.filter((type) => type.isActive === true),
        // }));

        return {
            list: list,
            count: count,
        };
    }

    async servicesListByUser(servicesListDto: ServicesListDto, userId: string): Promise<any> {
        const pageSize = servicesListDto.pageSize ?? 10;
        const page = servicesListDto.page ?? 1;
        const skip = pageSize * (page - 1);

        const userDetails = await this.ClientModel.findOne(
            { userId: userId },
            { organizationId: 1 }
        );
        if (!userDetails) throw new BadRequestException("User details not found");
        const orgId = userDetails.organizationId;

        let query: any = {
            organizationId: orgId,
            isActive: { $ne: false },
        };

        if (servicesListDto?.classType) {
            query["classType"] = servicesListDto.classType;
        }

        const countProm = this.ServiceModel.countDocuments(query);
        const listProm = this.ServiceModel.find(query)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(pageSize);

        const [list, count] = await Promise.all([listProm, countProm]);

        // const filteredList = list.map((service) => ({
        //     ...service.toObject(),
        //     appointmentType: service.appointmentType.filter((type) => type.isActive === true),
        // }));

        return {
            list: list,
            count: count,
        };
    }
    async activeServicesListByOrg(servicesListDto: ServicesListDto, orgId: string): Promise<any> {
        const pageSize = servicesListDto.pageSize ?? 10;
        const page = servicesListDto.page ?? 1;
        const skip = pageSize * (page - 1);

        let query: any = {
            $or: [
                {
                    organizationId: orgId,
                    isActive: { $ne: false },
                },
                {
                    _id: servicesListDto.serviceCategoryId,
                },
            ],
        };
        if (servicesListDto?.classType) {
            query["classType"] = servicesListDto.classType;
        }

        const countProm = this.ServiceModel.countDocuments(query);
        const listProm = this.ServiceModel.find(query)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(pageSize);

        const [list, count] = await Promise.all([listProm, countProm]);
        const filteredList = list.map((service) => {
            const activeTypes = service.appointmentType.filter((type) => type.isActive === true);

            return {
                ...service.toObject(),
                appointmentType: activeTypes.map((type) => {
                    const typeObj = (type && typeof (type as any).toObject === 'function') ? (type as any).toObject() : type;
                    const typeId = (typeObj as any)?._id?.toString();
                    return {
                        ...typeObj,
                        isSelected: servicesListDto?.appointmentTypes?.includes(typeId),
                    };
                }),
            };
        });

        return {
            list: filteredList,
            count: count,
        };
    }


    async activeServicesListByStaff(servicesListDto: ServicesListDto, staffId: string): Promise<any> {
        const pageSize = servicesListDto.pageSize ?? 10;
        const page = servicesListDto.page ?? 1;
        const skip = pageSize * (page - 1);

        const staffDetails = await this.StaffModel.findOne(
            { userId: staffId },
            { organizationId: 1 }
        );
        if (!staffDetails) throw new BadRequestException("Staff not found");
        const orgId = staffDetails.organizationId;


        let query: any = {
            $or: [
                {
                    organizationId: orgId,
                    isActive: { $ne: false },
                },
                {
                    _id: servicesListDto.serviceCategoryId,
                },
            ],
        };

        if (servicesListDto?.classType) {
            query["classType"] = servicesListDto.classType;
        }

        const countProm = this.ServiceModel.countDocuments(query);
        const listProm = this.ServiceModel.find(query)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(pageSize);

        const [list, count] = await Promise.all([listProm, countProm]);

        const filteredList = list.map((service) => {
            const activeTypes = service.appointmentType.filter((type) => type.isActive === true);

            return {
                ...service.toObject(),
                appointmentType: activeTypes.map((type) => {
                    const typeObj = (type && typeof (type as any).toObject === 'function') ? (type as any).toObject() : type;
                    const typeId = (typeObj as any)?._id?.toString();
                    return {
                        ...typeObj,
                        isSelected: servicesListDto?.appointmentTypes?.includes(typeId),
                    };
                }),
            };
        });

        return {
            list: filteredList,
            count: count,
        };
    }

    async activeServicesListByUser(servicesListDto: ServicesListDto, userId: string): Promise<any> {
        const pageSize = servicesListDto.pageSize ?? 10;
        const page = servicesListDto.page ?? 1;
        const skip = pageSize * (page - 1);

        const userDetails = await this.ClientModel.findOne(
            { userId: userId },
            { organizationId: 1 }
        );
        if (!userDetails) throw new BadRequestException("User details not found");
        const orgId = userDetails.organizationId;

        let query: any = {
            $or: [
                {
                    organizationId: orgId,
                    isActive: { $ne: false },
                },
                {
                    _id: servicesListDto.serviceCategoryId,
                },
            ],
        };

        if (servicesListDto?.classType) {
            query["classType"] = servicesListDto.classType;
        }

        const countProm = this.ServiceModel.countDocuments(query);
        const listProm = this.ServiceModel.find(query)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(pageSize);

        const [list, count] = await Promise.all([listProm, countProm]);

        const filteredList = list.map((service) => {
            const activeTypes = service.appointmentType.filter((type) => type.isActive === true);

            return {
                ...service.toObject(),
                appointmentType: activeTypes.map((type) => {
                    const typeObj = (type && typeof (type as any).toObject === 'function') ? (type as any).toObject() : type;
                    const typeId = (typeObj as any)?._id?.toString();
                    return {
                        ...typeObj,
                        isSelected: servicesListDto?.appointmentTypes?.includes(typeId),
                    };
                }),
            };
        });

        return {
            list: filteredList,
            count: count,
        };
    }

    async updateServicesByOrg(updateServiceCategoryDto: UpdateServiceCategoryDto, orgId: string): Promise<any> {
        let checkService = await this.ServiceModel.findOne({
            _id: updateServiceCategoryDto.serviceId,
            organizationId: orgId,
        });
        if (!checkService) throw new BadRequestException("Service category not found");
        if (!checkService?.isActive || (checkService?.isActive != updateServiceCategoryDto?.isActive && updateServiceCategoryDto?.isActive == false)) {
            let checkServiceMappedWithPricing = await this.Pricing.findOne({
                "services.serviceCategory": new Types.ObjectId(updateServiceCategoryDto.serviceId),
                organizationId: new Types.ObjectId(orgId),
                isActive:true
            });
            if (checkServiceMappedWithPricing && !updateServiceCategoryDto?.isActive) {
                throw new BadRequestException("Service category is mapped with pricing, please remove it from pricing to update");
            }
        }
        const currentISTTime = moment().tz("Asia/Kolkata").format("HH:mm");
        const startOfDay = moment().startOf("day").toDate();
        const startOfNextDay = moment().add(1, 'day').startOf('day').toDate();
        let checkFutureScheduling = await this.SchedulingModel.findOne({
            $or: [
                {
                    date: { $gt: startOfDay },
                    $or: [
                        { from: { $gt: currentISTTime } },
                        { to: { $gt: currentISTTime } }
                    ]
                },
                {
                    date: { $gt: startOfNextDay }
                }
            ],
            scheduleStatus: { $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN] },
            serviceCategoryId: new Types.ObjectId(updateServiceCategoryDto.serviceId),
        })
        if (checkFutureScheduling) {
            throw new BadRequestException("Service category is already booked for future scheduling so it can't be deactivated");
        }
        let data = {
            name: updateServiceCategoryDto.name,
            classType: updateServiceCategoryDto.classType,
            description: updateServiceCategoryDto?.description,
            image: updateServiceCategoryDto?.image,
            isFeatured: updateServiceCategoryDto?.isFeatured,
            isActive: updateServiceCategoryDto?.isActive
        };

        if (updateServiceCategoryDto.isFeatured) {
            const service = await this.ServiceModel.findOne(
                {
                    _id: updateServiceCategoryDto.serviceId,
                    organizationId: orgId,
                },

            );
            if (!updateServiceCategoryDto.image) {
                throw new BadRequestException("Image is required for featuring a service");
            }
            await this.validateFeaturedItems(service.classType, orgId, updateServiceCategoryDto.serviceId);
        }

        let result = await this.ServiceModel.findOneAndUpdate(
            {
                _id: updateServiceCategoryDto.serviceId,
            },
            {
                $set: data,
            },
            {
                new: true,
            },
        );
        return result;
    }

    async updateServicesByWebMaster(updateServiceCategoryDto: UpdateServiceCategoryDto, staffId: string): Promise<any> {
        let staffDetails = await this.StaffModel.findOne({ userId: staffId }, { organizationId: 1 });
        if (!staffDetails) throw new BadRequestException("Staff not found");
        let orgId = staffDetails["organizationId"];
        let checkService = await this.ServiceModel.findOne({
            _id: updateServiceCategoryDto.serviceId,
            organizationId: orgId,
            isActive: true
        });
        if (!checkService) throw new BadRequestException("Service category not found");
        if (!checkService?.isActive || (checkService?.isActive != updateServiceCategoryDto?.isActive && updateServiceCategoryDto?.isActive == false)) {
            let checkServiceMappedWithPricing = await this.Pricing.findOne({
                "services.serviceCategory": new Types.ObjectId(updateServiceCategoryDto.serviceId),
                organizationId: new Types.ObjectId(orgId)
            });
            console.log(checkServiceMappedWithPricing,"llllllllll")
            if (checkServiceMappedWithPricing) {
                throw new BadRequestException("Service category is mapped with pricing, please remove it from pricing to update");
            }
        }
        const currentISTTime = moment().tz("Asia/Kolkata").format("HH:mm");
        const startOfDay = moment().startOf("day").toDate();
        const startOfNextDay = moment().add(1, 'day').startOf('day').toDate();
        let checkFutureScheduling = await this.SchedulingModel.findOne({
            $or: [
                {
                    date: { $gt: startOfDay },
                    $or: [
                        { from: { $gt: currentISTTime } },
                        { to: { $gt: currentISTTime } }
                    ]
                },
                {
                    date: { $gt: startOfNextDay }
                }
            ],
            scheduleStatus: { $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN] },
            serviceCategoryId: new Types.ObjectId(updateServiceCategoryDto.serviceId),
        })
        if (checkFutureScheduling) {
            throw new BadRequestException("Service category is already booked for future scheduling so it can't be deactivated");
        }
        let data = {
            name: updateServiceCategoryDto.name,
            classType: updateServiceCategoryDto.classType,
            description: updateServiceCategoryDto?.description,
            image: updateServiceCategoryDto?.image,
            isFeatured: updateServiceCategoryDto?.isFeatured,
            isActive: updateServiceCategoryDto?.isActive

        };

        if (updateServiceCategoryDto.isFeatured) {
            const service = await this.ServiceModel.findOne(
                {
                    _id: updateServiceCategoryDto.serviceId,
                    organizationId: orgId,
                },

            );
            if (!updateServiceCategoryDto.image) {
                throw new BadRequestException("Image is required for featuring a service");
            }
            await this.validateFeaturedItems(service.classType, orgId, updateServiceCategoryDto.serviceId);
        }

        let result = await this.ServiceModel.findOneAndUpdate(
            {
                _id: updateServiceCategoryDto.serviceId,
            },
            {
                $set: data,
            },
            {
                new: true,
            },
        );
        return result;
    }

    async addAppointmentTypeByOrg(addAppointmentTypeDto: AppointmentTypesDto, orgId: string): Promise<any> {
        const { new: isNew, name, attributeId, durationInMinutes, onlineBookingAllowed, serviceId, image } = addAppointmentTypeDto;

        if (addAppointmentTypeDto.isFeatured) {
            const service = await this.ServiceModel.findOne(
                {
                    _id: serviceId,
                    organizationId: orgId,
                },

            );
            if (!addAppointmentTypeDto.image) {
                throw new BadRequestException("Image is required for featuring a service");
            }
            await this.validateFeaturedItems(service.classType, orgId);
        }

        let attributeData;
        if (isNew) {
            const attributeFilter = {
                organizationId: orgId,
                attributeType: AttributeType.TIER,
                name,
            };

            const existingAttribute = await this.AttributeModel.findOne(attributeFilter);
            if (existingAttribute) {
                throw new BadRequestException("Appointment Type already exists with this name.");
            }

            const newAttribute = new this.AttributeModel({ ...attributeFilter, image, isFeatured: addAppointmentTypeDto.isFeatured });
            attributeData = await newAttribute.save();
        } else {
            attributeData = await this.AttributeModel.findOne({ _id: attributeId });
            if (!attributeData) {
                throw new BadRequestException("Invalid attribute ID provided.");
            }
        }

        const appointmentTypeData = {
            name,
            durationInMinutes,
            onlineBookingAllowed,
            _id: attributeData._id,
            image: image || attributeData.image,
            isFeatured: addAppointmentTypeDto.isFeatured,
        };

        const updatedService = await this.ServiceModel.findOneAndUpdate(
            {
                _id: serviceId,
                organizationId: orgId,
                "appointmentType._id": { $ne: attributeData._id }, // Ensure the attribute is not already mapped
            },
            {
                $push: { appointmentType: appointmentTypeData },
            },
            { new: true }
        );

        if (!updatedService) {
            throw new BadRequestException(
                `${attributeData.name} Appointment Type is already mapped with this service category.`
            );
        }

        return updatedService;
    }

    async addAppointmentTypeByWebMaster(addAppointmentTypeDto: AppointmentTypesDto, staffId: string): Promise<any> {
        const { new: isNew, name, attributeId, durationInMinutes, onlineBookingAllowed, serviceId } = addAppointmentTypeDto;

        const staffDetails = await this.StaffModel.findOne({ userId: staffId }, { organizationId: 1 });
        if (!staffDetails) {
            throw new BadRequestException("Staff not found");
        }
        const orgId = staffDetails.organizationId;


        if (addAppointmentTypeDto.isFeatured) {
            const service = await this.ServiceModel.findOne(
                {
                    _id: serviceId,
                    organizationId: orgId,
                },

            );
            if (!addAppointmentTypeDto.image) {
                throw new BadRequestException("Image is required for featuring a service");
            }
            await this.validateFeaturedItems(service.classType, orgId);
        }

        let attributeData;
        if (isNew) {
            const attributeFilter = {
                organizationId: orgId,
                attributeType: AttributeType.TIER,
                name,
            };

            const existingAttribute = await this.AttributeModel.findOne(attributeFilter);
            if (existingAttribute) {
                throw new BadRequestException("Appointment Type already exists.");
            }

            const newAttribute = new this.AttributeModel({ attributeFilter, image: addAppointmentTypeDto.image, isFeatured: addAppointmentTypeDto.isFeatured });
            attributeData = await newAttribute.save();
        } else {
            attributeData = await this.AttributeModel.findOne({ _id: attributeId });
            if (!attributeData) {
                throw new BadRequestException("Invalid attribute ID provided.");
            }
        }

        const appointmentTypeData = {
            name,
            durationInMinutes,
            onlineBookingAllowed,
            _id: attributeData._id,
            isFeatured: addAppointmentTypeDto.isFeatured,
        };

        const updatedService = await this.ServiceModel.findOneAndUpdate(
            {
                _id: serviceId,
                organizationId: orgId,
                "appointmentType._id": { $ne: attributeData._id },
            },
            {
                $push: { appointmentType: appointmentTypeData },
            },
            { new: true }
        );

        if (!updatedService) {
            throw new BadRequestException(
                `${attributeData.name} Appointment Type is already mapped with this service category.`
            );
        }

        return updatedService;
    }

    async updateAppointmentTypeByOrg(updateAppointmentTypeDto: UpdateAppointmentTypesDto, orgId: string): Promise<any> {

        if (updateAppointmentTypeDto.isFeatured) {
            const service = await this.ServiceModel.findOne(
                {
                    _id: updateAppointmentTypeDto.serviceId,
                    organizationId: orgId,
                },

            );
            if (!updateAppointmentTypeDto.image) {
                throw new BadRequestException("Image is required for featuring a service");
            }
            await this.validateFeaturedItems(service.classType, orgId, undefined, updateAppointmentTypeDto.appointmentTypeId);
        }

        const existingData = await this.AttributeModel.findOne({
            _id: { $ne: updateAppointmentTypeDto.appointmentTypeId },
            organizationId: orgId,
            attributeType: AttributeType.TIER,
            name: updateAppointmentTypeDto.name,
        });
        if (existingData) throw new BadRequestException("Appointment Type already exists.");
        let data = {
            _id: updateAppointmentTypeDto.appointmentTypeId,
            name: updateAppointmentTypeDto.name,
            durationInMinutes: updateAppointmentTypeDto.durationInMinutes,
            onlineBookingAllowed: updateAppointmentTypeDto.onlineBookingAllowed,
            image: updateAppointmentTypeDto.image,
            isFeatured: updateAppointmentTypeDto.isFeatured,
        };

        let updateAppointment = await this.ServiceModel.findOneAndUpdate(
            {
                _id: updateAppointmentTypeDto.serviceId,
                organizationId: orgId,
                appointmentType: { $elemMatch: { _id: { $eq: updateAppointmentTypeDto.appointmentTypeId } } },
            },
            {
                $set: { "appointmentType.$": data },
            },
            {
                new: true,
            },
        );
        await this.AttributeModel.updateOne({ _id: updateAppointmentTypeDto.appointmentTypeId }, { name: updateAppointmentTypeDto.name, image: updateAppointmentTypeDto.image, isFeatured: updateAppointmentTypeDto.isFeatured || false });

        if (!updateAppointment) throw new BadRequestException("Service category not found");
        return updateAppointment;
    }

    async updateAppointmentTypeByWebMaster(updateAppointmentTypeDto: UpdateAppointmentTypesDto, staffId: string): Promise<any> {
        let staffDetails = await this.StaffModel.findOne({ userId: staffId }, { organizationId: 1 });
        if (!staffDetails) throw new BadRequestException("Staff not found");
        let orgId = staffDetails["organizationId"];

        if (updateAppointmentTypeDto.isFeatured) {

            const service = await this.ServiceModel.findOne(
                {
                    _id: updateAppointmentTypeDto.serviceId,
                    organizationId: orgId,
                },

            );
            if (!updateAppointmentTypeDto.image) {
                throw new BadRequestException("Image is required for featuring a service");
            }
            await this.validateFeaturedItems(service.classType, orgId, undefined, updateAppointmentTypeDto.appointmentTypeId);
        }

        const existingData = await this.AttributeModel.findOne({
            _id: { $ne: updateAppointmentTypeDto.appointmentTypeId },
            organizationId: orgId,
            attributeType: AttributeType.TIER,
            name: updateAppointmentTypeDto.name,
        });
        if (existingData) throw new BadRequestException("Appointment Type already exists.");
        let data = {
            _id: updateAppointmentTypeDto.appointmentTypeId,
            name: updateAppointmentTypeDto.name,
            durationInMinutes: updateAppointmentTypeDto.durationInMinutes,
            onlineBookingAllowed: updateAppointmentTypeDto.onlineBookingAllowed,
            image: updateAppointmentTypeDto.image,
            isFeatured: updateAppointmentTypeDto.isFeatured,
        };

        let updateAppointment = await this.ServiceModel.findOneAndUpdate(
            {
                _id: updateAppointmentTypeDto.serviceId,
                organizationId: orgId,
                appointmentType: { $elemMatch: { _id: { $eq: updateAppointmentTypeDto.appointmentTypeId } } },
            },
            {
                $set: { "appointmentType.$": data },
            },
            {
                new: true,
            },
        );

        await this.AttributeModel.updateOne({ _id: updateAppointmentTypeDto.appointmentTypeId }, { name: updateAppointmentTypeDto.name, image: updateAppointmentTypeDto.image, isFeatured: updateAppointmentTypeDto.isFeatured || false });

        if (!updateAppointment) throw new BadRequestException("Service category not found");
        return updateAppointment;
    }

    async serviceDetails(serviceId: string): Promise<any> {
        return await this.ServiceModel.findOne({ _id: serviceId });
    }

    async appointmentTypeDetails(serviceId: string, appointmentId: string): Promise<any> {
        return await this.ServiceModel.findOne({ _id: serviceId, "appointmentType._id": appointmentId }, { appointmentType: { $elemMatch: { _id: appointmentId } } });
    }

    async assignPricingByOrg(assignPricingDto: AssignPricingDto, organizationId: string): Promise<any> {
        const { pricingIds, serviceCategoryId, subTypeId } = assignPricingDto;
        let checkServiceActive = await this.ServiceModel.findOne({ _id: new Types.ObjectId(serviceCategoryId), organizationId: new Types.ObjectId(organizationId) });
        if (!checkServiceActive) throw new BadRequestException("Service category not found");
        if (checkServiceActive?.isActive === false) throw new BadRequestException("Service category is not active");
        const pricingExists = await this.Pricing.exists({ _id: { $in: pricingIds }, organizationId: organizationId });
        if (!pricingExists) {
            throw new BadRequestException("Pricing not found for the given organization");
        }

        for (const pricingId of pricingIds) {
            const updateResult = await this.Pricing.updateOne(
                {
                    _id: pricingId,
                    "services.relationShip.serviceCategory": new Types.ObjectId(serviceCategoryId),
                },
                {
                    $addToSet: {
                        "services.relationShip.$.subTypeIds": new Types.ObjectId(subTypeId),
                    },
                }
            );

            if (updateResult.modifiedCount === 0) {
                await this.Pricing.updateOne(
                    { _id: pricingId },
                    {
                        $addToSet: {
                            "services.relationShip": {
                                serviceCategory: new Types.ObjectId(serviceCategoryId),
                                subTypeIds: [new Types.ObjectId(subTypeId)],
                            },
                        },
                    }
                );
            }
        }

        return "Pricing assigned successfully"
    }

    async assignPricingByStaff(assignPricingDto: AssignPricingDto, staffId: string): Promise<any> {
        const { pricingIds, serviceCategoryId, subTypeId } = assignPricingDto;
        let checkServiceActive = await this.ServiceModel.findOne({ _id: new Types.ObjectId(serviceCategoryId) });
        if (!checkServiceActive) throw new BadRequestException("Service category not found");
        if (checkServiceActive?.isActive === false) throw new BadRequestException("Service category is not active");
        const staffDetails = await this.StaffModel.findOne({ userId: staffId }, { organizationId: 1 });
        if (!staffDetails) {
            throw new BadRequestException("Staff not found");
        }
        const organizationId = staffDetails.organizationId;

        const pricingExists = await this.Pricing.exists({ _id: { $in: pricingIds }, organizationId: organizationId });
        if (!pricingExists) {
            throw new BadRequestException("Pricing not found for the given organization");
        }

        for (const pricingId of pricingIds) {
            const updateResult = await this.Pricing.updateOne(
                {
                    _id: pricingId,
                    "services.relationShip.serviceCategory": new Types.ObjectId(serviceCategoryId),
                },
                {
                    $addToSet: {
                        "services.relationShip.$.subTypeIds": new Types.ObjectId(subTypeId), // Add to existing serviceCategory
                    },
                }
            );

            if (updateResult.modifiedCount === 0) {
                await this.Pricing.updateOne(
                    { _id: pricingId },
                    {
                        $addToSet: {
                            "services.relationShip": {
                                serviceCategory: new Types.ObjectId(serviceCategoryId),
                                subTypeIds: [new Types.ObjectId(subTypeId)],
                            },
                        },
                    }
                );
            }
        }

        return "Pricing assigned successfully"
    }

    async removeAssignedPricingByOrg(removePricingDto: RemovePricingDto, organizationId: string): Promise<any> {
        const { pricingId, serviceCategoryId, subTypeId } = removePricingDto;

        const pricingExists = await this.Pricing.exists({ _id: pricingId, organizationId: organizationId });
        if (!pricingExists) {
            throw new BadRequestException("Pricing not found");
        }

        const updateResult = await this.Pricing.updateOne(
            {
                _id: pricingId,
                "services.relationShip.serviceCategory": new Types.ObjectId(serviceCategoryId),
            },
            {
                $pull: {
                    "services.relationShip.$.subTypeIds": new Types.ObjectId(subTypeId),
                },
            }
        );

        if (updateResult.modifiedCount === 0) {
            throw new BadRequestException("Service category or subTypeId not found");
        }

        await this.Pricing.updateOne(
            {
                _id: pricingId,
            },
            {
                $pull: {
                    "services.relationShip": {
                        serviceCategory: new Types.ObjectId(serviceCategoryId),
                        subTypeIds: { $size: 0 },
                    },
                },
            }
        );

        return "Pricing removed successfully"
    }

    async removeAssignedPricingByStaff(removePricingDto: RemovePricingDto, staffId: string): Promise<any> {
        const { pricingId, serviceCategoryId, subTypeId } = removePricingDto;

        const staffDetails = await this.StaffModel.findOne({ userId: staffId }, { organizationId: 1 });
        if (!staffDetails) {
            throw new BadRequestException("Staff not found");
        }
        const organizationId = staffDetails.organizationId;

        const pricingExists = await this.Pricing.exists({ _id: pricingId, organizationId: organizationId });
        if (!pricingExists) {
            throw new BadRequestException("Pricing not found");
        }

        const updateResult = await this.Pricing.updateOne(
            {
                _id: pricingId,
                "services.relationShip.serviceCategory": new Types.ObjectId(serviceCategoryId),
            },
            {
                $pull: {
                    "services.relationShip.$.subTypeIds": new Types.ObjectId(subTypeId),
                },
            }
        );

        if (updateResult.modifiedCount === 0) {
            throw new BadRequestException("Service category or subTypeId not found");
        }

        await this.Pricing.updateOne(
            {
                _id: pricingId,
            },
            {
                $pull: {
                    "services.relationShip": {
                        serviceCategory: new Types.ObjectId(serviceCategoryId),
                        subTypeIds: { $size: 0 },
                    },
                },
            }
        );

        return "Pricing removed successfully"
    }

    async updateAppointmentTypeStatusByOrg(updateAppointmentTypeStatusDto: UpdateAppointmentTypeStatusDto, orgId: string): Promise<any> {
        const updatedService = await this.ServiceModel.findOneAndUpdate(
            {
                _id: updateAppointmentTypeStatusDto.serviceId,
                organizationId: orgId,
                appointmentType: { $elemMatch: { _id: updateAppointmentTypeStatusDto.appointmentTypeId } },
            },
            {
                $set: { "appointmentType.$.isActive": updateAppointmentTypeStatusDto.isActive },
            },
            {
                new: true,
            },
        );

        if (!updatedService) {
            throw new BadRequestException(
                `Service with ID ${updateAppointmentTypeStatusDto.serviceId} or AppointmentType with ID ${updateAppointmentTypeStatusDto.appointmentTypeId} not found`
            );
        }
        return {
            message: `AppointmentType status updated successfully to ${updateAppointmentTypeStatusDto.isActive ? 'Active' : 'Inactive'
                }`,
            updatedService,
        };
    }

    async updateAppointmentTypeStatusByWebMaster(updateAppointmentTypeStatusDto: UpdateAppointmentTypeStatusDto, staffId: string): Promise<any> {
        const staffDetails = await this.StaffModel.findOne(
            { userId: staffId },
            { organizationId: 1 }
        );

        if (!staffDetails) {
            throw new BadRequestException(`Staff with ID ${staffId} not found`);
        }

        const orgId = staffDetails.organizationId;

        const updatedService = await this.ServiceModel.findOneAndUpdate(
            {
                _id: updateAppointmentTypeStatusDto.serviceId,
                organizationId: orgId,
                classType: ClassType.PERSONAL_APPOINTMENT,
                appointmentType: {
                    $elemMatch: { _id: updateAppointmentTypeStatusDto.appointmentTypeId },
                },
            },
            {
                $set: { "appointmentType.$.isActive": updateAppointmentTypeStatusDto.isActive },
            },
            { new: true }
        );

        if (!updatedService) {
            throw new BadRequestException(
                `Service with ID ${updateAppointmentTypeStatusDto.serviceId} or AppointmentType with ID ${updateAppointmentTypeStatusDto.appointmentTypeId} not found`
            );
        }

        return {
            message: `AppointmentType status updated successfully to ${updateAppointmentTypeStatusDto.isActive ? 'Active' : 'Inactive'
                }`,
            updatedService,
        };
    }

    async deleteAppointmentTypeByOrg(deleteAppointmentTypeDto: DeleteAppointmentTypesDto, orgId: string): Promise<any> {
        const pricingData = await this.Pricing.findOne({
            organizationId: orgId,
            $or: [
                { "services.appointmentType": { $elemMatch: { $eq: Types.ObjectId.createFromHexString(deleteAppointmentTypeDto.appointmentTypeId) } } },
                {
                    "services.relationShip": {
                        $elemMatch: { subTypeIds: { $elemMatch: { $eq: Types.ObjectId.createFromHexString(deleteAppointmentTypeDto.appointmentTypeId) } } },
                    },
                },
            ],
        });;
        if (pricingData) throw new BadRequestException("This subtype is mapped to a pricing.");
        const payRateData = await this.PayRateModel.findOne({ organizationId: orgId, appointmentType: deleteAppointmentTypeDto.appointmentTypeId });
        if (payRateData) throw new BadRequestException("This subtype is mapped to a staff.");
        let deleteAppointment = await this.ServiceModel.findOneAndUpdate(
            {
                _id: deleteAppointmentTypeDto.serviceId,
                organizationId: orgId,
            },
            {
                $pull: {
                    appointmentType: { _id: deleteAppointmentTypeDto.appointmentTypeId },
                },
            },
            {
                new: true,
            },
        );

        if (!deleteAppointment) throw new BadRequestException("Service category not found");
        return deleteAppointment;
    }

    async deleteAppointmentTypeByWebMaster(deleteAppointmentTypeDto: DeleteAppointmentTypesDto, staffId: string): Promise<any> {
        let staffDetails = await this.StaffModel.findOne({ userId: staffId }, { organizationId: 1 });
        if (!staffDetails) throw new BadRequestException("Staff not found");
        let orgId = staffDetails["organizationId"];
        const pricingData = await this.Pricing.findOne({
            organizationId: orgId,
            $or: [
                { "services.appointmentType": { $elemMatch: { $eq: Types.ObjectId.createFromHexString(deleteAppointmentTypeDto.appointmentTypeId) } } },
                {
                    "services.relationShip": {
                        $elemMatch: { subTypeIds: { $elemMatch: { $eq: Types.ObjectId.createFromHexString(deleteAppointmentTypeDto.appointmentTypeId) } } },
                    },
                },
            ],
        });;
        if (pricingData) throw new BadRequestException("This subtype is mapped to a pricing.");
        const payRateData = await this.PayRateModel.findOne({ organizationId: orgId, appointmentType: deleteAppointmentTypeDto.appointmentTypeId });
        if (payRateData) throw new BadRequestException("This subtype is mapped to a staff.");

        let deleteAppointment = await this.ServiceModel.findOneAndUpdate(
            {
                _id: deleteAppointmentTypeDto.serviceId,
                classType: ClassType.PERSONAL_APPOINTMENT,
                organizationId: orgId,
            },
            {
                $pull: {
                    appointmentType: { _id: deleteAppointmentTypeDto.appointmentTypeId },
                },
            },
            {
                new: true,
            },
        );

        if (!deleteAppointment) throw new BadRequestException("Service category not found");
        return deleteAppointment;
    }

    async sendMail(createOrganizationDto: SendMailDto): Promise<any> {
        const session = await this.transactionService.startTransaction();

        try {
            const orgDetails = await this.UserModel.findById(createOrganizationDto.id).session(session);
            if (!orgDetails) throw new BadRequestException("Organization not found");
            if (orgDetails.password && orgDetails.salt) throw new BadRequestException("Password already set");

            const { email, _id: orgId, name, role } = orgDetails;

            let record = await this.OtpModel.findOne({ for: email.toLowerCase() }).session(session);
            let randomNumber = record ? record.otp : this.generalService.randomOTP();

            if (!record) {
                const otpDetails = new this.OtpModel({
                    otp: randomNumber,
                    for: email.toLowerCase(),
                });
                await otpDetails.save({ session });
            }

            const dataForMail = {
                dashboardUrl: `${this.adminFrontEndHost}set-password?uid=${randomNumber}&id=${orgId}&email=${email.toLowerCase()}&role=${role}`,
                organizationName: name,
                username: email,
            };
            this.mailService.sendMail({
                to: email.toString(),
                subject: `Welcome to ${name} - Set Your New Password`,
                template: "organization-onboarding",
                context: dataForMail,
            });

            await this.transactionService.commitTransaction(session);

            return {
                message: "Email Sent Successfully",
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async organizationStatusUpdate(organizationId: string, changeStatusDto: ChangeStatusDto): Promise<any> {
        const organization = await this.UserModel.findById(organizationId);

        if (!organization) {
            throw new NotFoundException(`Organization with ID ${organizationId} not found`);
        }

        if (organization.isActive === changeStatusDto.status) {
            throw new BadRequestException(
                `Organization is already ${changeStatusDto.status ? 'Active' : 'Inactive'}`
            );
        }

        organization["isActive"] = changeStatusDto.status;
        await organization.save();
        return {
            message: `Organization status updated to ${changeStatusDto.status ? 'Active' : 'Inactive'}`,
            organization,
        };
    }
    async activeServicesListByOrgV1(serviceTypePayload: string, userId: string): Promise<any> {
        let query: any = {
            organizationId: userId
        };
        if (serviceTypePayload.length) {
            query["classType"] = { $in: serviceTypePayload }
        }
        const listProm = await this.ServiceModel.find(query)
        return {
            list: listProm
        }
    }
    async activeServicesListByStaffV1(serviceTypePayload: string, staffId: string): Promise<any> {
        const staffDetails = await this.StaffModel.findOne(
            { userId: staffId },
            { organizationId: 1 }
        );
        if (!staffDetails) throw new BadRequestException("Staff not found");
        const orgId = staffDetails.organizationId;
        let query: any = {
            organizationId: orgId,
            isActive: { $ne: false }
        };

        if (serviceTypePayload.length) {
            query["classType"] = { $in: serviceTypePayload }
        }
        const listProm = await this.ServiceModel.find(query)
        return {
            list: listProm
        }
    }


    async updateFeaturedStatusByOrg(id: string, changeStatusDto: ChangeStatusDto, user: IUserDocument): Promise<any> {
        const organizationId = await this.getOrganizationId(user);
        const service = await this.ServiceModel.findOne({
            _id: id,
            organizationId: organizationId,
            isActive: { $ne: false }
        });

        if (!service) {
            throw new BadRequestException("Service not found or inactive");
        }

        // If trying to set featured to false, just update and return
        if (!changeStatusDto.status) {
            service.isFeatured = false;
            await service.save();
            return service;
        }

        // Check how many services are already featured for this classType
        // const featuredCount = await this.ServiceModel.countDocuments({
        //     organizationId: organizationId,
        //     classType: service.classType,
        //     isFeatured: true
        // });

        // if (featuredCount >= 5) {
        //     throw new BadRequestException("Maximum 5 featured services allowed per class type");
        // }

        service.isFeatured = true;
        await service.save();
        return service;
    }

    async featuredList(organizationId: string): Promise<any> {
        if (!organizationId) return [];
        const orgId = new Types.ObjectId(organizationId);
        const groupedServices = await this.ServiceModel.aggregate([
            {
                $match: {
                    organizationId: orgId,
                    isFeatured: true,
                    classType: { $ne: ClassType.COURSES },
                },
            },
            {
                $project: {
                    name: 1,
                    description: { $ifNull: ["$description", ""] },
                    image: 1,
                    classType: 1,
                    isFeatured: 1,
                },
            },
            {
                $group: {
                    _id: "$classType",
                    services: {
                        $push: {
                            serviceId: "$_id",
                            subtypeId: null,
                            name: "$name",
                            description: "$description",
                            image: "$image",
                            isFeatured: "$isFeatured",
                            type: "service",
                        },
                    },
                },
            },
            {
                $project: {
                    _id: 0,
                    classType: "$_id",
                    services: 1,
                },
            },
        ]);

        // Get featured appointment types
        const groupedSubTypeByService = await this.ServiceModel.aggregate([
            {
                $match: {
                    organizationId: orgId,
                    classType: { $ne: ClassType.COURSES },
                    "appointmentType.isFeatured": true,
                },
            },
            {
                $unwind: "$appointmentType",
            },
            {
                $match: {
                    "appointmentType.isFeatured": true,
                },
            },
            {
                $group: {
                    _id: "$classType",
                    services: {
                        $push: {
                            serviceId: "$_id",
                            subtypeId: "$appointmentType._id",
                            durationInMinutes: "$appointmentType.durationInMinutes",
                            name: "$appointmentType.name",
                            description: { $ifNull: ["$appointmentType.description", ""] },
                            image: "$appointmentType.image",
                            isFeatured: "$appointmentType.isFeatured",
                            type: "appointmentType",
                        },
                    },
                },
            },
            {
                $project: {
                    _id: 0,
                    classType: "$_id",
                    services: 1,
                },
            },
        ]);

        const courses = await this.Pricing.aggregate([
            {
                $match: {
                    organizationId: orgId,
                    isBundledPricing: { $ne: true },
                    "services.type": "courses",
                    isFeatured: true,
                    isActive: true,
                },
            },
            {
                $addFields: {
                    expiredDate: {
                        $switch: {
                            branches: [
                                {
                                    case: { $eq: ["$durationUnit", "days"] },
                                    then: {
                                        $add: ["$createdAt", { $multiply: ["$expiredInDays", 24 * 60 * 60 * 1000] }],
                                    },
                                },
                                {
                                    case: { $eq: ["$durationUnit", "months"] },
                                    then: {
                                        $add: ["$createdAt", { $multiply: ["$expiredInDays", 30 * 24 * 60 * 60 * 1000] }],
                                    },
                                },
                                {
                                    case: { $eq: ["$durationUnit", "years"] },
                                    then: {
                                        $add: ["$createdAt", { $multiply: ["$expiredInDays", 365 * 24 * 60 * 60 * 1000] }],
                                    },
                                },
                            ],
                            default: "$createdAt",
                        },
                    },
                },
            },
            {
                $match: {
                    $expr: {
                        $gt: ["$expiredDate", new Date()],
                    },
                },
            },
            {
                $project: {
                    name: 1,
                    startDate: "$createdAt",
                    endDate: "$expiredDate",
                    description: "$description",
                    image: "$image",
                    isFeatured: "$isFeatured",
                },
            },
        ]);

        // Merge results by classType
        const orderClassType = [
            ClassType.PERSONAL_APPOINTMENT,
            ClassType.CLASSES,
            ClassType.COURSES,
            ClassType.BOOKINGS,
        ];

        let index = 0;
        console.log(123456, courses)
        const result = orderClassType
            .map((classType) => {
                const serviceGroup = groupedServices.find((s) => s.classType === classType);
                const appointmentGroup = groupedSubTypeByService.find((s) => s.classType === classType);

                const services = [
                    ...(serviceGroup?.services || []),
                    ...(appointmentGroup?.services || []),
                ];

                if (classType === ClassType.COURSES && courses.length > 0) {
                    services.push(
                        ...courses.map((course) => ({
                            name: course.name,
                            courseId: course._id,
                            startDate: course.startDate,
                            endDate: course.endDate,
                            type: "course",
                            image: course.image || "",
                            isFeatured: true,
                            description: course.description || "",
                        })),
                    );
                }

                if (services.length > 0) {
                    return {
                        label: ClassTypeAbbreviationPlural[classType],
                        index: index++,
                        classType,
                        services,
                    };
                }

                return null;
            })
            .filter(Boolean)
            .sort((a, b) => a.index - b.index);

        return result;
    }


    async groupSubTypeByService(reqBody: GroupServiceSubtypeDTO, user: any): Promise<any> {
        let { search, page, pageSize, organizationId, classType } = reqBody
        if (!organizationId) {
            throw new Error("Organization ID is required.");
        }
        // if (isNaN(page) || page < 1) {
        //     page = 1;
        // }
        // if (isNaN(pageSize) || pageSize < 1) {
        //     pageSize = 10;
        // }
        // const skip = (page - 1) * pageSize;
        let searchQuery = [];
        if (search?.length > 0) {
            searchQuery.push(
                { name: { $regex: search, $options: "i" } },
                { "appointmentType.name": { $regex: search, $options: "i" } }
            );
        }

        let services = await this.ServiceModel.aggregate([
            {
                $match: {
                    organizationId: new Types.ObjectId(organizationId),
                    classType: classType,
                    isActive: { $ne: false },

                    ...(searchQuery.length > 0 && { $or: searchQuery })
                }
            },
            {
                $project: {
                    _id: 1,
                    name: 1,
                    appointmentType: {
                        $filter: {
                            input: "$appointmentType",
                            as: "appointment",
                            cond: {
                                $eq: ["$$appointment.isActive", true]
                            }
                        }
                    },
                    createdAt: 1,
                    updatedAt: 1
                }
            },
            {
                $sort: { name: 1 }
            },
            {
                $addFields: {
                    appointmentType: {
                        $sortArray: { input: "$appointmentType", sortBy: { name: 1 } }
                    }
                }
            },
            {
                $facet: {
                    list: [
                        // { $skip: skip },
                        // { $limit: pageSize }
                    ],
                    total: [
                        { $count: "total" }
                    ]
                }
            }
        ]);

        return {
            list: services[0].list,
            count: services[0].total.length > 0 ? services[0].total[0].total : 0,
        };
    }

    async ServicesFromPackage(reqBody: ServicesFromPackageDto, user: any): Promise<any> {
        let { search = "", page = 1, pageSize = 10, packageId } = reqBody;

        const searchQuery: any[] = [];
        if (search.trim()) {
            searchQuery.push(
                { name: { $regex: search, $options: "i" } },
                { "appointmentType.name": { $regex: search, $options: "i" } }
            );
        }

        const pipeline: any[] = [
            {
                $match: {
                    _id: new Types.ObjectId(packageId),
                },
            },
            {
                $lookup: {
                    from: "services",
                    let: {
                        serviceCategoryId: "$services.serviceCategory",
                        relationshipCategories: {
                            $map: {
                                input: { $ifNull: ["$services.relationShip", []] },
                                as: "rel",
                                in: "$$rel.serviceCategory",
                            },
                        },
                        appointmentIds: { $ifNull: ["$services.appointmentType", []] },
                        subTypeIds: {
                            $reduce: {
                                input: { $ifNull: ["$services.relationShip", []] },
                                initialValue: [],
                                in: {
                                    $concatArrays: ["$$value", { $ifNull: ["$$this.subTypeIds", []] }],
                                },
                            },
                        },
                    },
                    pipeline: [
                        {
                            $match: {
                                ...(searchQuery.length ? { $or: searchQuery } : {}),
                                $expr: {
                                    $in: [
                                        "$_id",
                                        {
                                            $concatArrays: [
                                                "$$relationshipCategories",
                                                [{ $ifNull: ["$$serviceCategoryId", null] }],
                                            ],
                                        },
                                    ],
                                },
                            },
                        },
                        {
                            $addFields: {
                                appointmentType: {
                                    $filter: {
                                        input: "$appointmentType",
                                        as: "appt",
                                        cond: {
                                            $in: [
                                                "$$appt._id",
                                                { $concatArrays: ["$$appointmentIds", "$$subTypeIds"] },
                                            ],
                                        },
                                    },
                                },
                            },
                        },
                    ],
                    as: "serviceData",
                },
            },
            { $unwind: "$serviceData" },
            { $replaceRoot: { newRoot: "$serviceData" } },
            {
                $facet: {
                    list: [
                        // { $skip: (page - 1) * pageSize },
                        // { $limit: pageSize },
                    ],
                    total: [
                        { $count: "total" }
                    ],
                },
            },
        ];

        const result = await this.Pricing.aggregate(pipeline);

        return {
            list: result[0].list,
            count: result[0].total?.[0]?.total || 0,
        };
    }

    async findOrganizationByUser(user: any): Promise<OrganizationDocument | null> {
        const organizationId = await this.getOrganizationId(user);
        if (!organizationId) {
            return null;
        }

        const organization = await this.OrganizationModel.findOne({
            userId: organizationId
        }).exec();

        return organization;
    }

    async validateFeaturedItems(classType: string, organizationId: string, serviceId?: string, appointmentTypeId?: string): Promise<any> {
        const pip: PipelineStage[] = [
            {
                $match: {
                    organizationId: new Types.ObjectId(organizationId),
                    classType: classType,
                    appointmentType: {
                        $elemMatch: {
                            isFeatured: true,
                            _id: { $ne: appointmentTypeId ? new Types.ObjectId(appointmentTypeId) : undefined }
                        }
                    }
                }
            },
            {
                $unwind: "$appointmentType"
            },
            {
                $match: {
                    "appointmentType.isFeatured": true
                }
            },
            {
                $count: "total"
            }
        ]

        // const serviceFeaturedCount = await this.ServiceModel.countDocuments({
        //     _id: { $ne: (serviceId ? new Types.ObjectId(serviceId) : undefined) },
        //     organizationId: organizationId,
        //     classType: classType,
        //     isFeatured: true
        // });

        // const featuredCount = await this.ServiceModel.aggregate(pip).exec();
        // if ((featuredCount[0]?.total || 0) + serviceFeaturedCount >= 5) {
        //     throw new BadRequestException("Maximum 5 featured services are allowed per class type");
        // }
    }

    // Revenue Category CRUD Operations
    async addRevenueCategory(addRevenueCategoryDto: AddRevenueCategoryDto, organizationId: IDatabaseObjectId): Promise<any> {

        const organization = await this.OrganizationModel.findOne({ userId: organizationId });
        if (!organization) {
            throw new NotFoundException("Organization not found");
        }

        // Check if revenue category with same name already exists
        const existingCategory = organization.revenueCategory?.find(
            (category) => category.name.toLowerCase() === addRevenueCategoryDto.name.toLowerCase()
        );

        if (existingCategory) {
            throw new BadRequestException("Revenue category with this name already exists");
        }

        // Create new revenue category
        const newRevenueCategory = {
            name: addRevenueCategoryDto.name,
            description: addRevenueCategoryDto.description || "",
            isActive: addRevenueCategoryDto.isActive !== undefined ? addRevenueCategoryDto.isActive : true,
        };

        // Add to organization's revenue categories
        const updatedOrganization = await this.OrganizationModel.findOneAndUpdate(
            { userId: organizationId },
            { $push: { revenueCategory: newRevenueCategory } },
            { new: true }
        );

        // Get the newly added revenue category
        const addedCategory = updatedOrganization.revenueCategory[updatedOrganization.revenueCategory.length - 1];

        return {
            message: "Revenue category added successfully",
            data: addedCategory,
        };
    }

    async updateRevenueCategory(id: any, organizationId: any, updateRevenueCategoryDto: UpdateRevenueCategoryDto): Promise<any> {

        const organization = await this.OrganizationModel.findOne({ userId: organizationId });
        if (!organization) {
            throw new NotFoundException("Organization not found");
        }

        // Check if revenue category exists
        const categoryIndex = organization.revenueCategory?.findIndex(
            (category) => category._id.toString() === id.toString()
        );

        if (categoryIndex === -1 || categoryIndex === undefined) {
            throw new NotFoundException("Revenue category not found");
        }

        // Check if another category with the same name exists (excluding the current one)
        const duplicateName = organization.revenueCategory?.find(
            (category) =>
                category.name.toLowerCase() === updateRevenueCategoryDto.name.toLowerCase() &&
                category._id.toString() !== id.toString()
        );

        if (duplicateName) {
            throw new BadRequestException("Another revenue category with this name already exists");
        }

        // Update the revenue category
        const updateQuery = {};
        updateQuery[`revenueCategory.${categoryIndex}.name`] = updateRevenueCategoryDto.name;
        updateQuery[`revenueCategory.${categoryIndex}.description`] = updateRevenueCategoryDto.description || "";

        if (updateRevenueCategoryDto.isActive !== undefined) {
            updateQuery[`revenueCategory.${categoryIndex}.isActive`] = updateRevenueCategoryDto.isActive;
        }

        const updatedOrganization = await this.OrganizationModel.findOneAndUpdate(
            { userId: organizationId },
            { $set: updateQuery },
            { new: true }
        );

        return updatedOrganization.revenueCategory[categoryIndex]
    }

    async getRevenueCategoryList(revenueCategoryListDto: RevenueCategoryListDto, user: IUserDocument): Promise<any> {
        const organizationId = revenueCategoryListDto.organizationId || await this.getOrganizationId(user);

        const organization = await this.OrganizationModel.findOne({ userId: organizationId });
        if (!organization) {
            throw new NotFoundException("Organization not found");
        }

        let revenueCategories = organization.revenueCategory || [];

        // Apply filters
        if (revenueCategoryListDto.isActive !== undefined) {
            revenueCategories = revenueCategories.filter(category => category.isActive === revenueCategoryListDto.isActive);
        }

        return {
            message: "Revenue categories retrieved successfully",
            data: revenueCategories
        };
    }

    async getRevenueCategoryById(categoryId: string, user: IUserDocument): Promise<any> {
        const organizationId = await this.getOrganizationId(user);

        const organization = await this.OrganizationModel.findOne({ userId: organizationId });
        if (!organization) {
            throw new NotFoundException("Organization not found");
        }

        const category = organization.revenueCategory?.find(
            (category) => category._id.toString() === categoryId
        );

        if (!category) {
            throw new NotFoundException("Revenue category not found");
        }

        return {
            message: "Revenue category retrieved successfully",
            data: category,
        };
    }

    async updateRevenueCategoryStatus(categoryId: string, statusDto: RevenueCategoryStatusDto, user: IUserDocument): Promise<any> {
        const organizationId = await this.getOrganizationId(user);

        const organization = await this.OrganizationModel.findOne({ userId: organizationId });
        if (!organization) {
            throw new NotFoundException("Organization not found");
        }

        // Check if revenue category exists
        const categoryIndex = organization.revenueCategory?.findIndex(
            (category) => category._id.toString() === categoryId
        );

        if (categoryIndex === -1 || categoryIndex === undefined) {
            throw new NotFoundException("Revenue category not found");
        }

        // Update the status
        const updateQuery = {};
        updateQuery[`revenueCategory.${categoryIndex}.isActive`] = statusDto.isActive;

        const updatedOrganization = await this.OrganizationModel.findOneAndUpdate(
            { userId: organizationId },
            { $set: updateQuery },
            { new: true }
        );

        return {
            message: "Revenue category status updated successfully",
            data: updatedOrganization.revenueCategory[categoryIndex],
        };
    }

    async deleteRevenueCategoryStatus(categoryId: string, user: IUserDocument): Promise<any> {
        const organizationId = await this.getOrganizationId(user);

        const organization = await this.OrganizationModel.findOne({ userId: organizationId });
        if (!organization) {
            throw new NotFoundException("Organization not found");
        }

        // Check if revenue category exists
        const categoryIndex = organization.revenueCategory?.findIndex(
            (category) => category._id.toString() === categoryId.toString()
        );

        if (categoryIndex === -1 || categoryIndex === undefined) {
            throw new NotFoundException("Revenue category not found");
        }

        // Remove the revenue category
        const updatedOrganization = await this.OrganizationModel.findOneAndUpdate(
            { userId: organizationId },
            { $pull: { revenueCategory: { _id: categoryId } } },
            { new: true }
        );

        return {
            message: "Revenue category deleted successfully",
            data: updatedOrganization.revenueCategory,
        };
    }
}



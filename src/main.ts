import { NestFactory } from "@nestjs/core";
import { AppModule } from "./app/app.module";
import { Logger, ValidationPipe } from "@nestjs/common";
import compression from "compression";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";
import { ConfigService } from "@nestjs/config";
import session from 'express-session';
import { RedisStore } from 'connect-redis';
import { CachingService } from "./common/caching/services/caching.service";

async function bootstrap() {
    const app = await NestFactory.create(AppModule);
    const configService = app.get(ConfigService);
    const cachingService = app.get(CachingService);

    // Trust proxy - required for secure cookies to work behind proxies
    (app as any).set('trust proxy', 1);

    // Cors - configured for cross-site cookies
    app.enableCors({
        // For production, you should specify exact origins instead of allowing all
        // origin: true, // This allows all origins - replace with specific origins in production
        origin: true, // This allows all origins - replace with specific origins in production
        credentials: true, // Allow credentials (cookies, authorization headers, etc.)
        exposedHeaders: ['Set-Cookie'],
    });

    // Get Redis client from CachingService
    const redisClient = await cachingService.getClient();

    // Initialize Redis store
    const redisStore = new RedisStore({
        client: redisClient,
        prefix: "user-session:",
    });

    const config = new DocumentBuilder()
        .setTitle("Gym Application")
        .setDescription("Gym Service Backend APIs")
        .setVersion("1.0")
        .addServer(`http://localhost:${process.env.PORT}/`, "Local Server")
        .addServer(`${process.env.HOST_URL}/`, "Server")
        .addBearerAuth({
            type: "http",
            bearerFormat: "JWT",
            scheme: "bearer",
        })
        .build();
    const document = SwaggerModule.createDocument(app, config);
    if (process.env.NODE_ENV !== 'production') {
        SwaggerModule.setup("docs", app, document, {
            explorer: false,
            swaggerOptions: {
                filter: true,
                showRequestDuration: true,
            },
        });
    }

    app.useGlobalPipes(
        new ValidationPipe({
            transform: true,
            transformOptions: {
                enableImplicitConversion: true,
            },
            whitelist: true,
            forbidNonWhitelisted: false,
            errorHttpStatusCode: 422,
        }),
    );
    // Configure session middleware with Redis store
    app.use(
        session({
            store: redisStore,
            secret: configService.get<string>('auth.session.secret'),
            resave: true,  // Changed to true to enable session updates on activity
            saveUninitialized: false,
            rolling: true, // This ensures cookie expiry is reset on every request
            name: configService.get<string>('auth.session.name'),
            proxy: true,
            cookie: {
                httpOnly: configService.get<boolean>('auth.session.cookie.httpOnly'),
                secure: configService.get<boolean>('auth.session.cookie.secure'),
                sameSite: configService.get<'lax' | 'strict' | 'none'>('auth.session.cookie.sameSite'),
                path: configService.get<string>('auth.session.cookie.path'),
                // No maxAge specified - makes it a session cookie (expires when browser closes)
            },
        })
    );
    // app.useGlobalFilters(new MongoExceptionFilter());
    app.use(
        compression({
            level: 9,
            threshold: 0,
            filter: (req, res) => {
                if (req.headers["x-no-compression"]) {
                    return false;
                }
                return compression.filter(req, res);
            },
        }),
    );

    await app.listen(process.env.PORT ? parseInt(process.env.PORT) : 3000, process.env.HOST ? process.env.HOST : '0.0.0.0', () => {
        Logger.log(`Server started at http://localhost:${process.env.PORT || 3000} url`);
    });
}

bootstrap().catch((error) => {
    Logger.error('Bootstrap failed:', error);
    process.exit(1);
});

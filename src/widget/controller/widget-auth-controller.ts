import { BadRequestException, Body, Controller, Get, HttpCode, HttpException, HttpStatus, Param, Patch, Post, Req, Res, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { WidgetRegisterUserDto } from "../dto/widget-Register-dto";
import { IRequestApp } from "src/common/request/interfaces/request.interface";
import { WidgetAuthService } from "../service/widget-auth.service";
import { AuthService } from "src/auth/services/auth.service";
import { Response } from "express";

@ApiTags("widget-authentication")
@ApiBearerAuth()
@Controller("widget-auth")
export class widgetAuthenticationController {
    constructor(private widgetAuthService: WidgetAuthService,
        private authService: AuthService,
    ) { }
    @Post("/register")
    @HttpCode(201)
    @ApiOperation({ summary: "Register a new user through widget" })
    async registerUser(@Body() registerUserDto: WidgetRegisterUserDto, @Req() request: IRequestApp, @Res({ passthrough: true }) response: Response): Promise<object> {
        const data = await this.widgetAuthService.registerUser(registerUserDto);
        //  const sessionId = await this.authService.startNewSession(request, data.user);
        //  await this.authService.setCookieData(response, data.user, sessionId.toString(), data.accessToken, data.loginDate, data.organizationId);
        return {
            message: "User registered",
            data: data,
        };
    }
}
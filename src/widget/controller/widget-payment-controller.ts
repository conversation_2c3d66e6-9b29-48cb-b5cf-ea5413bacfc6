// src/modules/widget-payment/controller/widget-payment.controller.ts
import {
  BadRequestException,
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { WidgetPaymentService } from '../service/widget-payment.service';

@ApiTags('widget-payment')
@ApiBearerAuth()
@Controller('widget-payment')
export class WidgetPaymentController {
  constructor(private readonly widgetPaymentService: WidgetPaymentService) {}

  @Post('create-payment-order')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Create Razorpay order from widget' })
  async createOrder(
    @Body()
    body: {
      amount: number;
      packageId: string;
      userId: string;
      widgetId: string;
    },
  ) {
    const { amount, packageId, userId } = body;
console.log(body)
    // if (!amount || !packageId || !userId) {
    //   throw new BadRequestException('Missing required parameters');
    // }

    return this.widgetPaymentService.createPaymentOrder(body);
  }

  @Post('verify-payment')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify Razorpay payment signature' })
  async verifyPayment(
    @Body()
    body: {
      razorpay_order_id: string;
      razorpay_payment_id: string;
      razorpay_signature: string;
      packageId: string;
      userId: string;
      widgetId: string; // Add widgetId to the body
      amount: number;
      date:string;
    },
  ) {
    return this.widgetPaymentService.verifyPaymentSignature(body);
  }
}

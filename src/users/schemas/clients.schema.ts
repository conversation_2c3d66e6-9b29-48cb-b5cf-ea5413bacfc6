import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Types } from "aws-sdk/clients/acm";
import { HydratedDocument, SchemaTypes } from "mongoose";
import * as crypto from "crypto";
import { Gender } from "src/utils/enums/gender.enum";
import { BusinessAddressDocument, BusinessAddressSchema } from "./business-address.schema";
import { AddressDocument, AddressSchema } from "./address.schema";
import { RELATION_ENUM } from "src/utils/enums/relation.enum";

@Schema({ timestamps: true })
export class Clients {

    @Prop({ type: SchemaTypes.ObjectId, required: false, index: true, default: null, ref: "User" })
    createdBy: String;

    @Prop({ type: SchemaTypes.ObjectId, required: true })
    organizationId: String;

    @Prop({ type: SchemaTypes.ObjectId, index: true, ref: "Facility" })
    facilityId?: String;

    @Prop({ type: SchemaTypes.ObjectId, required: true, index: true, ref: "User" })
    userId: String;

    @Prop({ type: String, required: false })
    membershipId?: String;

    @Prop({ type: String, unique: true, required: false })
    clientId?: string;

    // @Prop({ type: String, required: false })
    // name: String;

    // @Prop({ type: String, required: false })
    // firstName: String;

    // @Prop({ type: String, required: false })
    // lastName: String;

    @Prop({ type: Date, required: false, default: null })
    dob: Date;

    @Prop({ type: String, enum: Gender })
    gender: string;

    @Prop({ enum: RELATION_ENUM, required: false })
    relation?: RELATION_ENUM;

    @Prop({ type: String, required: false })
    activityLevel?: String;

    // @Prop({ type: String, unique: false, required: true })
    // mobile: String;

    // @Prop({ type: String, unique: false, required: true })
    // email: String;

    // @Prop({
    //     type: {
    //         addressLine1: { type: String, required: false },
    //         addressLine2: { type: String, required: false },
    //         postalCode: { type: Number, required: false },
    //         city: { type: SchemaTypes.ObjectId, ref: "Cities", required: false },
    //         state: { type: SchemaTypes.ObjectId, ref: "State", required: false },
    //         country: { type: String, required: false },
    //     },
    //     required: false,
    // })
    // address: { addressLine1: string; addressLine2: string; postalCode: Number; city: string; state: string; country: string };

    @Prop({
        type: AddressSchema,
        required: false,
        default: null
    })
    address: AddressDocument;

    @Prop({
        type: BusinessAddressSchema,
        validate: {
            validator: function (this: any, value: any) {
                return this.isBusiness ? !!value : true;
            },
            message: "Business address is required when isBusiness is true.",
        },
        default: null,
    })
    businessAddress: BusinessAddressDocument

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    isBusiness: boolean

    @Prop({ type: String, required: false })
    emergencyContactPerson: String;

    @Prop({ type: String, required: false })
    emergencyContactPhone: String;

    @Prop({ type: String, required: false, default: "" })
    photo?: string;

    @Prop({
        type: [
            {
                policyType: String,
                documentUrl: { type: String, default: "" },
                isEnabled: Boolean, // Check that 'Cinema' is the correct model name
            },
        ],
        required: false,
    })
    policies: { policyType: string; documentUrl: String; isEnabled: Boolean }[];

    @Prop({ type: Object, required: false, default: {} })
    basicAssessments: Object;

    @Prop()
    createdAt: Date;

    @Prop()
    updatedAt: Date;

    @Prop({ type: String, required: false })
    source: String;

    @Prop({ type: String, required: false })
    sourceId: String;
}

export const ClientSchema = SchemaFactory.createForClass(Clients);
export type ClientDocument = HydratedDocument<Clients>;

ClientSchema.index(
    { userId: 1, organizationId: 1 },
    {
        unique: true,
    },
);

ClientSchema.pre("save", async function (next) {
    if (this.isNew && !this.clientId) {
        const hash = crypto.createHash('sha256').update(this._id.toString()).digest('hex');
        const randomId = hash.substring(0, 7);
        this.clientId = `C-${randomId}`;
    }

    // for business validations
    if (this.isNew && !this.isBusiness) {
        this.businessAddress = null;
    }
    next();
});

import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, Model, Schema as MongooseSchema, SchemaTypes, Types } from "mongoose";
import * as bcrypt from "bcrypt";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { RoleEntity } from "src/role/repository/entities/role.entity";
import { PolicyEntity } from "src/policy/repository/entities/policy.entity";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";

export type UserDocument = HydratedDocument<User>;

@Schema({ timestamps: true })
export class User {
    @Prop({ type: String, required: false })
    name: String;

    @Prop({ type: String, required: false })
    firstName: String;

    @Prop({ type: String, required: false, default: "" })
    lastName: String;

    @Prop({ type: String, unique: false, required: false })
    mobile: String;
    @Prop({ type: String, unique: false, required: false })
    countryCode: String;

    @Prop({ type: String, unique: false, required: false, lowercase: true })
    email: String;

    @Prop({ type: Boolean, default: false })
    isActive: Boolean;

    @Prop({ type: Boolean, default: false })
    newUser: Boolean;

    // @Prop({ required: true, default: ENUM_ROLE_TYPE.USER, enum: RoleType })
    // role: String;

    @Prop({
        ref: RoleEntity.name,
        required: true,
        type: MongooseSchema.Types.ObjectId,
    })
    role: Types.ObjectId;

    @Prop({
        ref: PolicyEntity.name,
        required: false,
        default: [],
        type: [MongooseSchema.Types.ObjectId],
    })
    assignedPolicies: Types.ObjectId[];

    @Prop({
        ref: PolicyEntity.name,
        required: false,
        default: [],
        type: [MongooseSchema.Types.ObjectId],
    })
    restrictedPolicies: Types.ObjectId[];

    @Prop({ required: false })
    salt?: String;

    @Prop({ required: false })
    password?: string;

    @Prop({ isuserAcceptTerms: { type: Boolean, default: false } })
    isUserAcceptTerms: Boolean;


    @Prop({ type: SchemaTypes.ObjectId, ref: "User", default: null })
    parent: Types.ObjectId;

    // Method to validate password
    validatePassword: Function;

    @Prop({
        required: false,
        select: false,
        trim: true,
        length: 6,
        type: String,
    })
    pin?: string;

    // @Prop({ 
    //     required: false,
    //     select: false,
    //  })
    // pinSalt?: string;


    @Prop()
    createdAt: Date;

    @Prop()
    updatedAt: Date;

    validatePin?: Function;
}

export const UserSchema = SchemaFactory.createForClass(User);

UserSchema.methods.validatePin = async function (pin: string): Promise<boolean> {
    if (!this.pin) {
        return false;
    }
    const hash = pin // await bcrypt.hash(pin, this.pinSalt);
    return this.pin === hash;
};

UserSchema.pre('save', function (next) {
    const user = this;

    // Remove duplicates from arrays
    if (user.assignedPolicies) {
        user.assignedPolicies = [...new Set(user.assignedPolicies.map(p => p))];
    }

    // Ensure no policy is both assigned and restricted
    const assignedSet = new Set(user.assignedPolicies?.map(p => p.toString()));
    const restrictedSet = new Set(user.restrictedPolicies?.map(p => p.toString()));

    const conflicts = [...assignedSet].filter(x => restrictedSet.has(x));

    if (conflicts.length > 0) {
        next(new Error('A policy cannot be both assigned and restricted'));
    } else {
        next();
    }
});

UserSchema.pre<UserDocument>("save", async function (next) {
    if (this.isNew && !this.parent) {
        if (!this.mobile && !this.email) {
            return next(new Error("Either mobile or email is required."));
        }

        const existingUser = await (this.constructor as Model<UserDocument>).findOne({
            $or: [
                { mobile: this.mobile || null },
                { email: this.email || null }
            ].filter(condition => Object.values(condition)[0] !== null)
        })

        if (existingUser) {
            return next(new Error("Mobile number or email already exists."));
        }
    }

    if (this.isNew && this.parent) {
        if (!this.mobile && !this.email) {
            return next(new Error("Either mobile or email of parent's is required for minor users."));
        }
    }

    if (!this.isNew && this.isModified("parent")) {
        if (this.parent) {
            const parentUser = await (this.constructor as Model<UserDocument>).findById(this.parent);
            if (!parentUser) {
                return next(new Error("Parent user not found."));
            }
            this.mobile = parentUser.mobile;
            this.email = this.email ? 'temp_' + this.email : this.email;
        }
    }

    next();
});

async function handlePreUpdate(this: any, next: Function) {
    const update = this.getUpdate() || {};
    const userId = this.getFilter()?._id;

    const mobile = update.mobile || update?.$set?.mobile;
    const email = update.email || update?.$set?.email;

    const queryConditions: any[] = [];
    if (mobile) queryConditions.push({ mobile });
    if (email) queryConditions.push({ email });
    if (queryConditions.length > 0) {
        const userBeingUpdated = await (this.model as Model<UserDocument>).findOne({ _id: userId });
        let existingUser = null;
        // if child
        if (userBeingUpdated?.parent) {
            existingUser = await (this.model as Model<UserDocument>).findOne({
                $and: [
                    {
                        $or: queryConditions,
                    },
                    { _id: { $ne: userBeingUpdated?.parent } },
                    { parent: { $ne: userBeingUpdated?.parent } },
                ],
                $or: [
                    {
                        parent: userBeingUpdated.parent,
                    },
                    {
                        parent: { $exists: false },
                    },
                    {
                        parent: null,
                    },
                ],
            });
        }

        // if parent
        if (!userBeingUpdated?.parent) {
            existingUser = await (this.model as Model<UserDocument>).findOne({
                $and: [
                    {
                        $or: queryConditions,
                    },
                    { _id: { $ne: userId } },
                    { parent: { $ne: userId } },
                ],
                $or: [
                    {
                        parent: { $exists: false },
                    },
                    {
                        parent: null,
                    },
                ],
            });
        }
        if (existingUser) {
            return next(new Error("Mobile number or email already exists."));
        }
    }

    this.setUpdate(update);
    next();
}

UserSchema.pre("findOneAndUpdate", handlePreUpdate);
UserSchema.pre("updateOne", handlePreUpdate);
UserSchema.pre("updateMany", handlePreUpdate);
UserSchema.methods.validatePassword = async function (password: string): Promise<boolean> {
    const hash = await bcrypt.hash(password, this.salt);
    return this.password === hash;
};

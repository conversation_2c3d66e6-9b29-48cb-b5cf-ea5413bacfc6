import { BadRequestException, HttpException, HttpStatus, Injectable, InternalServerErrorException, NotFoundException, StreamableFile } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import mongoose, { Model, PipelineStage, Types } from "mongoose";
import { CreateInvoicePurchaseDto } from "../dto/packages-purchasing.dto";
import { Purchase, PurchaseDocument, Suspensions } from "../schemas/purchased-packages.schema";
import { User, UserDocument } from "src/users/schemas/user.schema";
import { Pricing } from "src/organization/schemas/pricing.schema";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { GetPackagesDto } from "../dto/get-packages.dto";
import { Invoice } from "../schemas/invoice.schema";
import { SessionType } from "src/utils/enums/session-type.enum";
import { DurationUnit } from "src/utils/enums/duration-unit.enum";
import { PricingListDTO } from "../dto/pricing-lis.dto";
import { GetInvoicesDto } from "../dto/get-invoices.dto";
import { membership } from "src/membership/schema/membership.schema";
import { Clients } from "../schemas/clients.schema";
import { MailService } from "src/mail/services/mail.service";
import { InvoiceService } from "../services/invoice.service";
import { UpdatePaymentStatusDto } from "src/staff/dto/update-payment-status.dto";
import { CancelOrder } from "src/staff/dto/cancel-order.dto";
import { InvoiceStatus } from "src/utils/enums/invoice-status.enum";
import { PaymentStatus } from "src/utils/enums/payment.enum";
import { Facility, FacilityDocument } from "src/facility/schemas/facility.schema";
import { PaginationDto } from "src/utils/dto/pagination.dto";
import { PaymentMethod } from "src/utils/enums/paymentMethod.enum";
import { DiscountType } from "src/utils/enums/discount.enum";
import moment from "moment-timezone";
import { ExportInvoicesDto } from "../dto/export-invoices.dto";
import { SuspendMembershipDto } from "../dto/suspend-membership.dto";
import { ResumeMembershipDto } from "../dto/resume-suspended-membership.dto";
import { UpdateSuspensionDto } from "../dto/update-suspended-membership.dto";
import { Scheduling } from "src/scheduling/schemas/scheduling.schema";
import { ScheduleStatusType } from "src/scheduling/enums/schedule-status.enum";
import { UpdateQuery } from "mongoose";
import { UsersPipe } from "../pipes/users.pipe";
import { Inventory } from "src/merchandise/schema/inventory.schema";
import { Product, ProductType } from "src/merchandise/schema/product.schema";
import { ProductVariant } from "src/merchandise/schema/product-variant.schema";
import { isArray } from "class-validator";
import { CustomPackage } from "src/customPackage/schemas/custom-package.schema";
import { ExportZOutReportDto } from "../dto/z-out-report.dto";
import { ClassType } from "src/utils/enums/class-type.enum";
import { ExportScheduleReportDto } from "../dto/schedule-report.dto";
import { Reconciliation } from "src/transactions/schema/reconciliation.schema";
import { PaymentMethodSchema } from "src/paymentMethod/schemas/payment-method.schema";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
const numberToWords = require("number-to-words");
const { write, utils } = require("xlsx");
import * as puppeteer from "puppeteer";
import * as Handlebars from "handlebars";
import * as fs from "fs";
import * as path from "path";
import { ExportSalesReportDto } from "../dto/sales-report.dto";
import { ExportSalesByEmpDto } from "../dto/sales-by-emp.dto";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { IUserDocument } from "../interfaces/user.interface";
import { ENUM_ITEM_TYPE } from "src/promotions/enums/item-type.enum";
import { PurchaseRequestDto } from "../dto/purchase-request.dto";
import { PurchaseResponseDto } from "../dto/response/purchase-response.dto";
import { TransactionService } from "src/utils/services/transaction.service";
import { CartService } from "src/cart/services/cart.service";
import * as QRCode from 'qrcode';
import { v4 as uuidV4 } from 'uuid';
import { UploadService } from 'src/utils/services/upload.service';
import { UpdatePurchaseSessionDto } from "../dto/update-purchase-session.dto";
import { PricingSessionLogs } from "../schemas/pricingSessions.log";
import { Enrollment } from "src/courses/schemas/enrollment.schema";


@Injectable()
export class PurchaseService {
    constructor(
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(Clients.name) private ClientModel: Model<Clients>,
        @InjectModel(Purchase.name) private readonly PurchaseModel: Model<PurchaseDocument>,
        @InjectModel(Pricing.name) private PricingModel: Model<Pricing>,
        @InjectModel(StaffProfileDetails.name) private StaffDetailsModel: Model<StaffProfileDetails>,
        @InjectModel(Invoice.name) private InvoiceModel: Model<Invoice>,
        @InjectModel(membership.name) private readonly MembershipModel: Model<membership>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(Scheduling.name) private SchedulingModel: Model<Scheduling>,
        @InjectModel(Inventory.name) private InventoryModel: Model<Inventory>,
        @InjectModel(Product.name) private productModel: Model<Product>,
        @InjectModel(ProductVariant.name) private productVariantModel: Model<ProductVariant>,
        @InjectModel(CustomPackage.name) private customPackageModel: Model<CustomPackage>,
        @InjectModel(Reconciliation.name) private ReconciliationModel: Model<Reconciliation>,
        @InjectModel("PaymentMethod") private readonly paymentMethodModel: Model<typeof PaymentMethodSchema>,
        @InjectModel(PricingSessionLogs.name) private PricingSessionLogsModel: Model<PricingSessionLogs>,
        @InjectModel(Enrollment.name) private EnrollmentModel: Model<Enrollment>,


        private usersPipe: UsersPipe,
        private readonly mailService: MailService,
        private readonly cartService: CartService,
        private readonly invoiceService: InvoiceService,
        private readonly uploadService: UploadService,
        private readonly transactionService: TransactionService,
    ) { }
    convertAmountToWords(amount) {
        const amountInWords = numberToWords.toWords(amount);
        return amountInWords.charAt(0).toUpperCase() + amountInWords.slice(1) + " Rupees Only";
    }
    checkRequiredFields(billingDetails: any) {
        if (!billingDetails[0]?.clientDetails?.customerId) {
            throw new NotFoundException("Clients Billing details not found");
        }
        if (!billingDetails[0]?.clientDetails?.name) {
            throw new NotFoundException("Clients Billing details not found");
        }
        if (!billingDetails[0]?.clientDetails?.phone) {
            throw new NotFoundException("Clients Billing details not found");
        }
        if (!billingDetails[0]?.clientDetails?.stateId) {
            throw new NotFoundException("Clients Billing details not found");
        }
        if (!billingDetails[0]?.clientDetails?.utCode) {
            throw new NotFoundException("Clients Billing details not found");
        }
        if (!billingDetails[0]?.billingDetails?.facilityName) {
            throw new NotFoundException("Facility Billing details not found");
        }
        if (!billingDetails[0]?.billingDetails?.billingName) {
            throw new NotFoundException("Facility Billing details not found");
        }
        if (!billingDetails[0]?.billingDetails?.addressLine1) {
            throw new NotFoundException("Facility Billing details not found");
        }
        if (!billingDetails[0]?.billingDetails?.cityId) {
            throw new NotFoundException("Facility Billing details not found");
        }
        if (!billingDetails[0]?.billingDetails?.stateId) {
            throw new NotFoundException("Facility Billing details not found");
        }
        if (!billingDetails[0]?.billingDetails?.email) {
            throw new NotFoundException("Facility Billing details not found");
        }
        if (!billingDetails[0]?.billingDetails?.phone) {
            throw new NotFoundException("Facility Billing details not found");
        }
        if (!billingDetails[0]?.billingDetails?.utCode) {
            throw new NotFoundException("Facility Billing details not found");
        }
        // Asked to change to specific error messages
        // if (!billingDetails[0]?.clientDetails?.customerId) {
        //     throw new NotFoundException("Client ID is Missing");
        // }
        // if (!billingDetails[0]?.clientDetails?.name) {
        //     throw new NotFoundException("Client name is Missing");
        // }
        // if (!billingDetails[0]?.clientDetails?.phone) {
        //     throw new NotFoundException("Client phone number is Missing");
        // }
        // if (!billingDetails[0]?.clientDetails?.stateId) {
        //     throw new NotFoundException("Client state is Missing");
        // }
        // if (!billingDetails[0]?.clientDetails?.utCode) {
        //     throw new NotFoundException("Client UT code is Missing");
        // }
        // if (!billingDetails[0]?.billingDetails?.facilityName) {
        //     throw new NotFoundException("Facility name is Missing");
        // }
        // if (!billingDetails[0]?.billingDetails?.billingName) {
        //     throw new NotFoundException("Billing name is Missing");
        // }
        // if (!billingDetails[0]?.billingDetails?.addressLine1) {
        //     throw new NotFoundException("Billing address is Missing");
        // }
        // if (!billingDetails[0]?.billingDetails?.cityId) {
        //     throw new NotFoundException("Billing city is Missing");
        // }
        // if (!billingDetails[0]?.billingDetails?.stateId) {
        //     throw new NotFoundException("Billing state is Missing");
        // }
        // if (!billingDetails[0]?.billingDetails?.email) {
        //     throw new NotFoundException("Billing email is Missing");
        // }
        // if (!billingDetails[0]?.billingDetails?.phone) {
        //     throw new NotFoundException("Billing phone number is Missing");
        // }
        // if (!billingDetails[0]?.billingDetails?.utCode) {
        //     throw new NotFoundException("Billing UT code is Missing");
        // }
    }
    async createPurchase(createPurchaseDto: CreateInvoicePurchaseDto, user: IUserDocument): Promise<any> {
        const session = await this.InvoiceModel.db.startSession();
        session.startTransaction();
        try {
            const { organizationId, facilityId, cartDiscount, platform, paymentDetails, billingAddressId, isSplittedPayment, amountPaid, cartDiscountType, returnPurchaseIds, returnTotal } = createPurchaseDto;
            let userId = new Types.ObjectId(createPurchaseDto.userId); // user will get package
            let billingUserId = new Types.ObjectId(createPurchaseDto.userId); // User to be billed

            const productsItem = (createPurchaseDto.productsItem || []) as {
                inventoryId: string;
                quantity: number;
            }[];
            let totalEffectiveReturnUnitPrice = 0
            if (returnPurchaseIds && returnPurchaseIds.length > 0) {
                const returnPurchases = await this.PurchaseModel.aggregate([
                    {
                        $match: {
                            _id: { $in: returnPurchaseIds.map(id => new Types.ObjectId(id)) },
                            organizationId: new Types.ObjectId(organizationId),
                            userId: new Types.ObjectId(userId),
                            isExpired: false,
                            endDate: { $gte: new Date() },
                            startDate: { $lte: new Date() },
                            isActive: { $ne: false },
                            bundledPricingId: { $exists: false },
                            isExchanged: { $ne: true },
                            exchangedInvoiceId: { $exists: false },
                            sharePass: { $ne: true }
                        }
                    },
                    {
                        $lookup: {
                            from: "invoices",
                            localField: "invoiceId",
                            foreignField: "_id",
                            as: "invoiceDetails"
                        }
                    },
                    {
                        $unwind: {
                            path: "$invoiceDetails",
                            preserveNullAndEmptyArrays: false
                        }
                    },
                    {
                        $addFields: {
                            matchedPurchaseItem: {
                                $arrayElemAt: [
                                    {
                                        $filter: {
                                            input:
                                                "$invoiceDetails.purchaseItems",
                                            as: "item",
                                            cond: {
                                                $eq: [
                                                    "$$item.packageId",
                                                    "$packageId"
                                                ]
                                            }
                                        }
                                    },
                                    0
                                ]
                            }
                        }
                    },
                    {
                        $project: {
                            _id: 1,
                            invoiceId: 1,
                            "matchedPurchaseItem.unitPrice": 1,
                            "matchedPurchaseItem.quantity": 1,
                            "matchedPurchaseItem.discountExcludeCart": 1,
                            "matchedPurchaseItem.discountIncludeCart": 1,
                            paymentStatus: "$invoiceDetails.paymentStatus"
                        }
                    }
                ]).session(session);

                if (returnPurchases.length !== returnPurchaseIds.length) {
                    throw new BadRequestException(
                        'Some selected returns are not eligible (expired or inactive)'
                    );
                }

                totalEffectiveReturnUnitPrice = returnPurchases.reduce((total, purchase) => {
                    const item = purchase.matchedPurchaseItem;
                    const unitPrice = item?.unitPrice || 0;
                    const discountExcludeCart = Number(item?.discountExcludeCart) / (item?.quantity >= 1 ? item?.quantity : 1) || 0;
                    const discountIncludeCart = Number(item?.discountIncludeCart) / (item?.quantity >= 1 ? item?.quantity : 1) || 0;

                    const effectiveUnitPrice = purchase.paymentStatus === PaymentStatus.COMPLETED ? unitPrice - (discountExcludeCart + discountIncludeCart) : 0;
                    return total + effectiveUnitPrice;
                }, 0);

                console.log('Total Effective Unit Price:', totalEffectiveReturnUnitPrice);
            }
            const purchaseItems = (createPurchaseDto.purchaseItems || []) as {
                packageId: string;
                quantity: number;
                isBundledPricing?: boolean;
                bundledPricingId?: string;
            }[];

            const customPackageItems = (createPurchaseDto.customPackageItems || []) as {
                customPackageId: string;
                quantity: number;
            }[];

            const paymentDetailsData = paymentDetails?.map((detail: any) => ({
                paymentMethod: detail.paymentMethod,
                paymentMethodId: detail.paymentMethodId,
                transactionId: detail.transactionId || "",
                amount: detail.amount,
                paymentDate: detail.paymentDate,
                paymentStatus: detail.paymentStatus,
                paymentGateway: detail.paymentGateway || "",
                description: detail.description,
                denominations: detail.paymentMethod === PaymentMethod.CASH ? detail.denominations || {} : {},
            }));
            let membershipId = "";
            let newPurchaseItems = [];

            if ((!purchaseItems || purchaseItems.length === 0) && (!productsItem || productsItem.length === 0) && (!customPackageItems || customPackageItems.length === 0)) {
                throw new BadRequestException("Purchase/Product items are required");
            }

            if (!userId) {
                throw new BadRequestException('User is required');
            }

            const clientUser = await this.UserModel.findOne({ _id: userId }).exec();
            if (!clientUser) {
                throw new NotFoundException('User not found');
            }
            const client = await this.ClientModel.findOne({ userId: clientUser._id }).exec();
            billingUserId = clientUser.parent ?? billingUserId;


            let pipeline = this.usersPipe.billingDetails(userId.toString(), facilityId);
            const billingDetails = await this.UserModel.aggregate(pipeline).exec();

            if (!billingDetails || billingDetails.length === 0 || !billingAddressId) {
                throw new NotFoundException("Insufficient data for billing details");
            }
            let clientBillingDetails = billingDetails[0]?.clientDetails;
            let isForBusiness = false;
            if (billingDetails[0].clientBusinessDetails && billingDetails[0].clientBusinessDetails?._id?.toString() === billingAddressId) {
                clientBillingDetails = billingDetails[0].clientBusinessDetails;
                isForBusiness = true;
                if (!clientBillingDetails.gstNumber) throw new BadRequestException("Please add GST number first for business related purchases");
            }

            this.checkRequiredFields([
                {
                    clientDetails: clientBillingDetails,
                    billingDetails: billingDetails[0]?.billingDetails,
                },
            ]);
            let productBilling: any = {};
            let customPackageBilling: any = {};
            const packageIds = purchaseItems.map((item) => new Types.ObjectId(item.packageId));
            let checkValidPackage = await this.PricingModel.find({ _id: { $in: packageIds }, isActive: true })
                .lean()
                .exec();
            const productId = productsItem.map((item: any) => new Types.ObjectId(item.inventoryId));
            let checkProductInInventory = await this.InventoryModel.find({ _id: { $in: productId } })
                .lean()
                .exec();
            const customPackageIds = customPackageItems.map((item) => new Types.ObjectId(item.customPackageId));
            let checkValidCustomPackage = await this.customPackageModel
                .find({ _id: { $in: customPackageIds }, isActive: true })
                .lean()
                .exec();
            // 1. Get subTotal of products and packages BEFORE discount
            // ✅ Calculate actual post-item-discount effective subtotals
            let productEffectiveTotal = 0;
            let packageEffectiveTotal = 0;
            let customPackageEffectiveTotal = 0;

            for (const item of productsItem) {
                const inventory = checkProductInInventory.find((p) => p._id.toString() === item.inventoryId.toString());
                const unitPrice = Number(inventory.salePrice || 0);
                const discount = (Number(inventory.discount || 0) / 100) * unitPrice;
                const effective = unitPrice - discount;
                productEffectiveTotal += effective * Number(item.quantity || 1);
            }

            for (const item of purchaseItems) {
                const pkg = checkValidPackage.find((p) => p._id.toString() === item.packageId.toString());
                const unitPrice = Number(pkg?.price || 0);
                let discount = 0;

                if (pkg?.discount?.type === DiscountType.FLAT) {
                    discount = Number(pkg.discount.value || 0);
                } else if (pkg?.discount?.type === DiscountType.PERCENTAGE) {
                    discount = (Number(pkg.discount.value || 0) / 100) * unitPrice;
                }

                const effective = unitPrice - discount;
                packageEffectiveTotal += effective * Number(item.quantity || 1);
            }

            for (const item of customPackageItems) {
                const pkg = checkValidCustomPackage.find((p) => p._id.toString() === item.customPackageId.toString());
                const unitPrice = Number(pkg?.unitPrice || 0);
                let discount = 0;

                if (pkg?.discount?.type === DiscountType.FLAT) {
                    discount = Number(pkg.discount.value || 0);
                } else if (pkg?.discount?.type === DiscountType.PERCENTAGE) {
                    discount = (Number(pkg.discount.value || 0) / 100) * unitPrice;
                }

                const effective = unitPrice - discount;
                customPackageEffectiveTotal += effective * Number(item.quantity || 1);
            }

            const totalEffective = productEffectiveTotal + packageEffectiveTotal + customPackageEffectiveTotal;

            // ✅ Calculate proportional cart discount
            let productCartDiscount = 0;
            let packageCartDiscount = 0;
            let customPackageCartDiscount = 0;

            if (cartDiscountType === DiscountType.FLAT && totalEffective > 0) {
                productCartDiscount = (productEffectiveTotal / totalEffective) * cartDiscount;
                packageCartDiscount = (packageEffectiveTotal / totalEffective) * cartDiscount;
                customPackageCartDiscount = (customPackageEffectiveTotal / totalEffective) * cartDiscount;
            } else if (cartDiscountType === DiscountType.PERCENTAGE) {
                productCartDiscount = cartDiscount;
                packageCartDiscount = cartDiscount;
                customPackageCartDiscount = cartDiscount;
            }

            if (checkProductInInventory.length !== productId.length && productsItem.length > 0) {
                throw new NotFoundException("Some Product are invalid");
            } else if (checkProductInInventory.length === productId.length && productsItem.length > 0) {
                productBilling = await this.purchaseProduct(productsItem, productCartDiscount, cartDiscountType, productEffectiveTotal);
            }
            if (customPackageItems.length > 0 && checkValidCustomPackage.length !== customPackageIds.length) {
                throw new NotFoundException("Some custom package are invalid");
            } else if (customPackageItems.length > 0 && checkValidCustomPackage.length === customPackageIds.length) {
                customPackageBilling = await this.purchaseCustomPackage(
                    customPackageItems,
                    customPackageCartDiscount,
                    cartDiscountType,
                    customPackageEffectiveTotal,
                    checkValidCustomPackage,
                );
            }
            if (checkValidPackage.length !== purchaseItems.length) {
                throw new NotFoundException("Some packages are invalid or inactive");
            }

            let bundledPackageIds = purchaseItems.filter((item) => item?.isBundledPricing == true).map((item) => new Types.ObjectId(item.packageId));

            if (bundledPackageIds?.length > 0) {
                for (const item of purchaseItems) {
                    if (bundledPackageIds.toString().includes(item.packageId.toString())) {
                        let packageDetails = checkValidPackage.find((pkg) => pkg._id.toString() === item.packageId.toString());
                        const checkBundledPackage = await this.PricingModel.find({
                            _id: { $in: packageDetails.pricingIds.map((id) => new Types.ObjectId(id)) },
                            isActive: true,
                        }).exec();
                        if (checkBundledPackage.length !== packageDetails.pricingIds.length) {
                            throw new NotFoundException("Some packages are invalid or inactive in bundled pricing");
                        }
                        checkValidPackage = [...checkValidPackage, ...checkBundledPackage];
                        packageDetails.pricingIds.map((id) => {
                            newPurchaseItems.push({
                                packageId: id,
                                quantity: item.quantity,
                                isBundledPricing: true,
                                bundledPricingId: item.packageId,
                            });
                        });
                    } else {
                        newPurchaseItems.push({
                            packageId: item.packageId,
                            quantity: item.quantity,
                            isBundledPricing: false,
                        });
                    }
                }
            }
            else {
                newPurchaseItems = [...newPurchaseItems, ...purchaseItems]
            }

            // let pipeline = this.usersPipe.billingDetails(userId, facilityId);
            // const billingDetails = await this.UserModel.aggregate(pipeline).exec();

            if (!billingDetails || billingDetails.length === 0 || !billingAddressId) {
                throw new NotFoundException('Insufficient data for billing details');
            }

            const highestInvoice = await this.InvoiceModel.findOne({ organizationId, facilityId }, { invoiceNumber: 1 }).sort({ invoiceNumber: -1 }).lean();
            const invoiceNumber = highestInvoice ? highestInvoice.invoiceNumber + 1 : 1;

            const highestOrderId = await this.InvoiceModel.findOne({ organizationId, facilityId }, { orderId: 1 }).sort({ orderId: -1 }).lean();
            const orderId = highestOrderId?.orderId ? highestOrderId?.orderId + 1 : 1;
            let invoiceSubTotal = 0;
            let invoiveDiscountExculdeCart = 0;
            let invoiveDiscountIncludeCart = 0;
            let invoiceGstAmount = 0;
            let invoiceAmountAfterGst = 0;
            let roundOff: any;
            let grandTotal = 0;
            let totalFlatCartDiscountAllocated = 0;
            for (let i = 0; i < purchaseItems.length; i++) {
                const item = purchaseItems[i];
                const packageDetails = checkValidPackage.find((pkg) => pkg._id.toString() === item.packageId.toString());
                if (!packageDetails) {
                    throw new NotFoundException(`Package with ID ${item.packageId} not found`);
                }
                const { durationUnit, expiredInDays: expireIn } = packageDetails;

                if (!durationUnit || expireIn == null) {
                    throw new Error(`Invalid duration unit or expiration days in package ${item.packageId}`);
                }
                const startDate = new Date();
                const endDate = new Date(startDate);

                switch (durationUnit) {
                    case DurationUnit.DAYS:
                        endDate.setDate(endDate.getDate() + expireIn);
                        break;
                    case DurationUnit.MONTHS:
                        endDate.setMonth(endDate.getMonth() + expireIn);
                        break;
                    case DurationUnit.YEARS:
                        endDate.setFullYear(endDate.getFullYear() + expireIn);
                        break;
                    default:
                        throw new Error(`Invalid duration unit: ${durationUnit}`);
                }
                let discountExcludeCart = 0;
                let discountIncludeCart = 0;
                let gstAmount = 0;
                let totalAmountExcludeCartDiscount = 0;

                if (packageDetails?.discount?.type === DiscountType.PERCENTAGE) {
                    discountExcludeCart = Number(item.quantity) * Number(((Number(packageDetails?.discount?.value) / 100) * Number(packageDetails?.price)).toFixed(2));
                } else if (packageDetails?.discount?.type === DiscountType.FLAT) {
                    discountExcludeCart = Number(item.quantity) * Number(Number(packageDetails?.discount?.value).toFixed(2));
                }

                const totalUnitPrice = Number(item.quantity) * Number(packageDetails?.price);
                invoiceSubTotal += totalUnitPrice;
                invoiveDiscountExculdeCart += discountExcludeCart;
                totalAmountExcludeCartDiscount = totalUnitPrice - discountExcludeCart;
                if (cartDiscount && cartDiscountType === DiscountType.PERCENTAGE) {
                    discountIncludeCart = Number(((Number(cartDiscount) / 100) * Number(totalAmountExcludeCartDiscount)).toFixed(2));
                    gstAmount = Number((totalUnitPrice - (discountExcludeCart + discountIncludeCart)) * (Number(packageDetails?.tax) / 100));
                    invoiceGstAmount += gstAmount;
                    invoiveDiscountIncludeCart += discountIncludeCart;
                } else if (packageCartDiscount && cartDiscountType === DiscountType.FLAT) {
                    const denominator = productEffectiveTotal + packageEffectiveTotal;
                    const itemShare = denominator > 0 ? totalAmountExcludeCartDiscount / denominator : 0;
                    // Handle last item for accurate rounding
                    if (i === purchaseItems.length - 1) {
                        discountIncludeCart = Number((Number(packageCartDiscount) - totalFlatCartDiscountAllocated).toFixed(2));
                    } else {
                        discountIncludeCart = Number((itemShare * Number(packageCartDiscount)).toFixed(2));
                        totalFlatCartDiscountAllocated += discountIncludeCart;
                    }

                    invoiveDiscountIncludeCart += discountIncludeCart;
                    gstAmount = Number((totalUnitPrice - (discountExcludeCart + discountIncludeCart)) * (Number(packageDetails?.tax) / 100));
                    invoiceGstAmount += gstAmount;
                } else {
                    gstAmount = Number((totalAmountExcludeCartDiscount * (Number(packageDetails?.tax) / 100)).toFixed(2));
                    invoiceGstAmount += gstAmount;
                }
                item["packageName"] = packageDetails?.name;
                item["expireIn"] = packageDetails?.expiredInDays;
                item["durationUnit"] = packageDetails?.durationUnit;
                item["startDate"] = startDate;
                item["endDate"] = endDate;
                item["unitPrice"] = packageDetails?.price;
                item["discountType"] = packageDetails?.discount?.type;
                item["discountValue"] = packageDetails?.discount?.value;
                item["discountExcludeCart"] = discountExcludeCart?.toFixed(2);
                item["discountIncludeCart"] = discountIncludeCart?.toFixed(2);
                item["hsnOrSacCode"] = packageDetails?.hsnOrSacCode;
                item["tax"] = packageDetails?.tax;
                item["gstAmount"] = gstAmount.toFixed(2) || 0;
            }

            invoiceAmountAfterGst = invoiceSubTotal + invoiceGstAmount - (invoiveDiscountIncludeCart + invoiveDiscountExculdeCart) || 0;
            if (checkProductInInventory.length === productId.length && productsItem.length > 0) {
                invoiceSubTotal += productBilling?.invoiceSubTotal;
                invoiveDiscountExculdeCart += productBilling?.invoiveDiscountExculdeCart;
                invoiveDiscountIncludeCart += productBilling?.invoiveDiscountIncludeCart;
                invoiceGstAmount += productBilling?.invoiceGstAmount;
                invoiceAmountAfterGst += productBilling?.invoiceAmountAfterGst;
            }
            if (customPackageItems.length > 0) {
                invoiceSubTotal += customPackageBilling?.invoiceSubTotal;
                invoiveDiscountExculdeCart += customPackageBilling?.invoiveDiscountExculdeCart;
                invoiveDiscountIncludeCart += customPackageBilling?.invoiveDiscountIncludeCart;
                invoiceGstAmount += customPackageBilling?.invoiceGstAmount;
                invoiceAmountAfterGst += customPackageBilling?.invoiceAmountAfterGst;
            }
            roundOff = (invoiceAmountAfterGst - Math.floor(invoiceAmountAfterGst)).toFixed(2);
            grandTotal = Math.floor(invoiceAmountAfterGst);
            const invoice = new this.InvoiceModel({
                createdBy: user._id,
                invoiceNumber,
                orderId,
                userId: billingUserId,
                organizationId,
                facilityId,
                purchaseItems,
                productItem: productBilling?.productDetail,
                customPackageItems: customPackageBilling?.productDetail,
                subTotal: invoiceSubTotal?.toFixed(2),
                discount: invoiveDiscountExculdeCart?.toFixed(2),
                cartDiscount: cartDiscount || 0,
                cartDiscountType: cartDiscountType,
                cartDiscountAmount: invoiveDiscountIncludeCart?.toFixed(2) || 0,
                totalGstValue: invoiceGstAmount?.toFixed(2) || 0,
                totalAmountAfterGst: invoiceAmountAfterGst.toFixed(2),
                roundOff: roundOff,
                grandTotal: grandTotal.toFixed(2),
                amountInWords: this.convertAmountToWords(Math.floor(grandTotal)),
                invoiceDate: new Date(),
                platform,
                paymentStatus: paymentDetailsData[0].paymentStatus,
                paymentDetails: paymentDetailsData,
                isSplittedPayment,
                amountPaid,
                clientDetails: {
                    _id: client?.userId || "",
                    customerId: client?.clientId || "",
                    name: clientUser.name || "",
                    email: billingDetails[0]?.clientDetails?.email || "",
                    phone: billingDetails[0]?.clientDetails?.phone || "",
                },
                clientBillingDetails: {
                    _id: clientBillingDetails?._id || "",
                    customerId: clientBillingDetails?.customerId || "",
                    name: clientBillingDetails?.name || "",
                    addressLine1: clientBillingDetails?.addressLine1 || "",
                    addressLine2: clientBillingDetails?.addressLine2 || "",
                    postalCode: clientBillingDetails?.postalCode || "",
                    cityId: new Types.ObjectId(clientBillingDetails?.cityId) || "",
                    cityName: clientBillingDetails?.cityName || "",
                    stateId: new Types.ObjectId(clientBillingDetails?.stateId) || "",
                    stateName: clientBillingDetails?.stateName || "",
                    gstNumber: clientBillingDetails?.gstNumber,
                    email: clientBillingDetails?.email || "",
                    phone: clientBillingDetails?.phone || "",
                    utCode: clientBillingDetails?.utCode || "",
                },
                billingDetails: {
                    facilityName: billingDetails[0]?.billingDetails?.facilityName || "",
                    billingName: billingDetails[0]?.billingDetails?.billingName || "",
                    gstNumber: billingDetails[0]?.billingDetails?.gstNumber || "",
                    email: billingDetails[0]?.billingDetails?.email || "",
                    phone: billingDetails[0]?.billingDetails?.phone || "",
                    addressLine1: billingDetails[0]?.billingDetails?.addressLine1 || "",
                    addressLine2: billingDetails[0]?.billingDetails?.addressLine2 || "",
                    postalCode: billingDetails[0]?.billingDetails?.postalCode || "",
                    cityId: new Types.ObjectId(billingDetails[0]?.billingDetails?.cityId) || "",
                    cityName: billingDetails[0]?.billingDetails?.cityName || "",
                    stateId: new Types.ObjectId(billingDetails[0]?.billingDetails?.stateId) || "",
                    stateName: billingDetails[0]?.billingDetails?.stateName || "",
                    utCode: billingDetails[0]?.billingDetails?.utCode || "",
                },
                isForBusiness,
            });

            const savedInvoice = await invoice.save({ session });
            const invoiceId = savedInvoice._id;

            if (returnPurchaseIds && returnPurchaseIds.length > 0) {

                await this.PurchaseModel.updateMany(
                    { _id: { $in: returnPurchaseIds.map(id => new Types.ObjectId(id)) } },
                    {
                        $set: {
                            isActive: false,
                            isExchanged: true,
                            exchangedInvoiceId: invoiceId,
                            exchangeDate: new Date()
                        }
                    },
                    { session }
                );

                savedInvoice.returnDetails = {
                    returnPurchaseIds,
                    returnTotal: totalEffectiveReturnUnitPrice,
                    exchangedOn: new Date(),
                };
                await savedInvoice.save({ session });

            }

            const purchases = [];
            const newEnrollments = [];
            for (const item of newPurchaseItems) {
                const packageDetails = checkValidPackage.find((pkg) => pkg._id.toString() === item.packageId.toString());
                if (!packageDetails) {
                    throw new NotFoundException(`Package with ID ${item.packageId} not found`);
                }
                if (packageDetails?.membershipId) {
                    membershipId = packageDetails?.membershipId || "";
                }

                for (let i = 0; i < item.quantity; i++) {
                    const { durationUnit, expiredInDays: expireIn } = packageDetails;
                    if (!durationUnit || expireIn == null) {
                        throw new Error(`Invalid duration unit or expiration days in package ${item.packageId}`);
                    }

                    const startDate = new Date();
                    const endDate = new Date(startDate);
                    switch (durationUnit) {
                        case DurationUnit.DAYS:
                            endDate.setDate(endDate.getDate() + expireIn);
                            break;
                        case DurationUnit.MONTHS:
                            endDate.setMonth(endDate.getMonth() + expireIn);
                            break;
                        case DurationUnit.YEARS:
                            endDate.setFullYear(endDate.getFullYear() + expireIn);
                            break;
                        default:
                            throw new Error(`Invalid duration unit: ${durationUnit}`);
                    }

                    // Temporarily push without QR and insert first to get _id
                    const purchaseDoc = new this.PurchaseModel({
                        invoiceId,
                        packageId: item.packageId,
                        userId: userId,
                        sponsorUser: userId.toString() === billingUserId.toString() ? null : billingUserId,
                        organizationId,
                        facilityId,
                        ...(item?.isBundledPricing == true && {
                            bundledPricingId: item.bundledPricingId,
                        }),
                        purchasedBy: clientUser._id,
                        membershipId: packageDetails?.membershipId || null,
                        purchaseDate: new Date(),
                        paymentStatus: paymentDetailsData[0].paymentStatus,
                        isExpired: false,
                        sessionType: packageDetails.services.sessionType,
                        ...([SessionType.UNLIMITED, SessionType.DAY_PASS].includes(packageDetails.services.sessionType) && {
                            sessionPerDay: packageDetails.services.sessionPerDay,
                        }),
                        ...(packageDetails.services.sessionType === SessionType.DAY_PASS && {
                            dayPassLimit: packageDetails.services.dayPassLimit,
                        }),
                        totalSessions: packageDetails.services.sessionType === SessionType.SINGLE
                            ? 1
                            : packageDetails.services.sessionCount,
                        sessionConsumed: 0,
                        startDate,
                        endDate,
                    });

                    // Save to generate _id
                    await purchaseDoc.save({ session });
                    if (packageDetails?.services?.type === "courses") {
                        const schedulings = await this.SchedulingModel.find({ packageId: item.packageId }).exec();
                        for (const scheduling of schedulings) {
                            const enrollments = await this.EnrollmentModel.find({
                                schedulingId: scheduling._id,
                            }).exec();
                            if (scheduling.classCapacity && enrollments.length < scheduling.classCapacity)
                                newEnrollments.push({
                                    schedulingId: scheduling._id,
                                    userId,
                                    packageId: item.packageId,
                                    purchaseId: purchaseDoc._id,
                                });
                        }
                    }


                    // Generate QR Code with purchase_id
                    // const qrUrl = `https://example.com?purchase_id=${purchaseDoc._id}`;
                    // const qrCodeBuffer = await QRCode.toBuffer(qrUrl, { type: 'png' });

                    const qrData = JSON.stringify({ purchase_id: purchaseDoc._id });
                    const qrCodeBuffer = await QRCode.toBuffer(qrData, { type: 'png' });

                    // Upload to S3
                    const s3Result = await this.uploadService.upload(
                        qrCodeBuffer,
                        'purchase-qr/',
                        `purchase-${purchaseDoc._id}.png`
                    );

                    // Update QR URL in doc
                    purchaseDoc.qrCodeUrl = s3Result?.Location || '';
                    await purchaseDoc.save({ session });
                }
            }
            if (newEnrollments.length) await this.EnrollmentModel.insertMany(newEnrollments);

            // if (purchases.length > 0) {
            //     await this.PurchaseModel.insertMany(purchases, { session });
            // }
            if (membershipId != "") {
                const clientProfile = await this.ClientModel.findOne({ userId: userId });
                if (!clientProfile?.membershipId) {
                    let checkCounter = await this.MembershipModel.findById(membershipId);

                    if (!checkCounter?.lastcounter || checkCounter?.lastcounter == 0) {
                        await this.ClientModel.findOneAndUpdate({ userId: userId }, { membershipId: `${checkCounter.prefix}-${checkCounter.counter}`.toString() }, { session });
                        await this.MembershipModel.findByIdAndUpdate(membershipId, { lastcounter: checkCounter.counter + 1 }, { session });
                    } else {
                        await this.ClientModel.findOneAndUpdate({ userId: userId }, { membershipId: `${checkCounter.prefix}-${checkCounter.lastcounter}` }, { session });
                        await this.MembershipModel.findByIdAndUpdate(membershipId, { lastcounter: checkCounter.lastcounter + 1 }, { session });
                    }
                }
            }

            await session.commitTransaction();

            if (paymentDetailsData[0].paymentStatus !== PaymentStatus.PENDING) {
                const organization = await this.UserModel.findById(organizationId).select("email");
                const organizationEmail = organization?.email || "";
                await this.invoiceService.generateInvoice(invoice, organizationEmail);
            }
            return { message: "Purchase created successfully", invoiceId, purchaseCount: purchases.length };
        } catch (error) {
            await session.abortTransaction();
            throw new BadRequestException(error.message); // ✅ This will return HTTP 400
        } finally {
            session.endSession(); // Ensure session is always closed
        }
    }

    async processPurchase(purchaseRequest: PurchaseRequestDto, user: any): Promise<PurchaseResponseDto> {
        const session = await this.transactionService.startTransaction();

        // Declare variables that need to be accessed in finally block
        let savedInvoice: any = null;
        let invoiceId: any = null;
        let organizationId: string = '';
        let paymentDetailsData: any[] = [];

        try {

            // Re-validate the cart
            const validatedCart = await this.cartService.revalidateCart(purchaseRequest.cart);

            // Check if the cart has validation errors
            if (validatedCart.validationErrors && validatedCart.validationErrors.length > 0) {
                throw new BadRequestException(
                    `Cart validation failed: ${validatedCart.validationErrors.join(', ')}`,
                );
            }

            const { cart, paymentDetails, isSplittedPayment, amountPaid, platform, billingAddressId } = purchaseRequest;
            const { facilityId, userId } = cart;
            // Update organizationId for use in finally block
            organizationId = cart.organizationId;

            // Format payment details
            paymentDetailsData = paymentDetails.map((detail) => ({
                paymentMethod: detail.paymentMethod,
                paymentMethodId: detail.paymentMethodId || '',
                transactionId: detail.transactionId || '',
                amount: detail.amount,
                paymentDate: detail.paymentDate,
                paymentStatus: detail.paymentStatus,
                paymentGateway: detail.paymentGateway || '',
                description: detail.description || '',
                denominations: detail.denominations || {},
            }));

            // Get billing details
            const pipeline = this.usersPipe.billingDetails(userId, facilityId);
            const billingDetails = await this.UserModel.aggregate(pipeline).exec();

            if (!billingDetails || billingDetails.length === 0 || !billingAddressId) {
                throw new NotFoundException('Insufficient data for billing details');
            }

            let clientBillingDetails = billingDetails[0]?.clientDetails;
            let isForBusiness = false;

            if (
                billingDetails[0].clientBusinessDetails &&
                billingDetails[0].clientBusinessDetails?._id?.toString() === billingAddressId
            ) {
                clientBillingDetails = billingDetails[0].clientBusinessDetails;
                isForBusiness = true;
                if (!clientBillingDetails.gstNumber) {
                    throw new BadRequestException('Please add GST number first for business related purchases');
                }
            }

            // Check required fields
            this.checkRequiredFields([
                {
                    clientDetails: clientBillingDetails,
                    billingDetails: billingDetails[0]?.billingDetails,
                },
            ]);

            // Prepare purchase items
            const purchaseItems = [];
            const productItems = [];
            const customPackageItems = [];

            // Process each item based on its type
            for (const item of validatedCart.items) {
                switch (item.itemType) {
                    case ENUM_ITEM_TYPE.SERVICE:
                        const service = await this.PricingModel.findOne({
                            _id: new Types.ObjectId(item._id),
                            isActive: true,
                        }).lean();

                        if (!service) {
                            throw new NotFoundException(`Service with ID ${item._id} not found or inactive`);
                        }

                        // Check if this is a bundled pricing package
                        if (service.isBundledPricing) {
                            // Validate all bundled packages are active
                            if (!service.pricingIds || service.pricingIds.length === 0) {
                                throw new BadRequestException(`Bundled pricing package ${item._id} has no associated packages`);
                            }

                            const checkBundledPackage = await this.PricingModel.find({
                                _id: { $in: service.pricingIds.map((id) => new Types.ObjectId(id)) },
                                isActive: true
                            }).exec();

                            if (checkBundledPackage.length !== service.pricingIds.length) {
                                throw new NotFoundException('Some packages are invalid or inactive in bundled pricing');
                            }
                        }

                        purchaseItems.push({
                            packageId: item._id,
                            packageName: item.name,
                            quantity: item.quantity,
                            isBundledPricing: service.isBundledPricing || false,
                            expireIn: service.expiredInDays,
                            durationUnit: service.durationUnit,
                            startDate: new Date(),
                            endDate: this.calculateEndDate(new Date(), service.expiredInDays, service.durationUnit),
                            unitPrice: item.price,
                            discountType: item.discountType ? this.mapPromotionTypeToDiscountType(item.discountType as any) : undefined,
                            discountValue: item.discountValue || 0,
                            discountedBy: item?.discountedBy,
                            discountExcludeCart: item.discountAmount,
                            discountIncludeCart: 0, // Will be calculated later
                            hsnOrSacCode: item.hsnOrSacCode,
                            tax: item.taxRate,
                            gstAmount: item.taxAmount,
                        });
                        break;

                    case ENUM_ITEM_TYPE.PRODUCT:
                        const inventory: any = await this.InventoryModel.findOne({
                            _id: new Types.ObjectId(item._id),
                        }).populate(
                            {
                                path: 'productId',
                                select: 'name',
                            }
                        );

                        if (!inventory) {
                            throw new NotFoundException(`Product with ID ${item._id} not found`);
                        }

                        if (inventory.quantity < item.quantity) {
                            throw new BadRequestException(`Insufficient stock for product ${inventory?.productId?.name}`);
                        }

                        productItems.push({
                            inventoryId: item._id,
                            productId: inventory.productId,
                            productVariantId: item.variantId || inventory.productVariantId,
                            productName: item.name,
                            quantity: item.quantity,
                            salePrice: item.price,
                            mrp: inventory.mrp,
                            finalPrice: item.finalPrice / item.quantity,
                            discountType: item.discountType ? this.mapPromotionTypeToDiscountType(item.discountType as any) : undefined,
                            discountedBy: item.discountedBy,
                            discountValue: item.discountValue || 0,
                            discountExcludeCart: item.discountAmount,
                            discountIncludeCart: 0, // Will be calculated later
                            hsnOrSacCode: item.hsnOrSacCode,
                            tax: item.taxRate,
                            gstAmount: item.taxAmount,
                        });
                        break;

                    case ENUM_ITEM_TYPE.PRODUCT: // Custom package
                        const customPackage = await this.customPackageModel.findOne({
                            _id: new Types.ObjectId(item._id),
                            isActive: true,
                        });

                        if (!customPackage) {
                            throw new NotFoundException(`Custom package with ID ${item._id} not found or inactive`);
                        }

                        customPackageItems.push({
                            customPackageId: item._id,
                            packageName: item.name,
                            quantity: item.quantity,
                            unitPrice: item.price,
                            discountType: item.discountType ? this.mapPromotionTypeToDiscountType(item.discountType as any) : undefined,
                            discountValue: item.discountValue || 0,
                            discountedBy: item.discountedBy,
                            discountExcludeCart: item.discountAmount || 0,
                            discountIncludeCart: 0, // Will be calculated later
                            hsnOrSacCode: item.hsnOrSacCode,
                            tax: item.taxRate,
                            gstAmount: item.taxAmount,
                        });
                        break;
                }
            }

            // No cart-level discount distribution needed with the new format

            // Generate invoice number and order ID
            const highestInvoice = await this.InvoiceModel
                .findOne({ organizationId, facilityId }, { invoiceNumber: 1 })
                .sort({ invoiceNumber: -1 })
                .lean();
            const invoiceNumber = highestInvoice ? highestInvoice.invoiceNumber + 1 : 1;

            const highestOrderId = await this.InvoiceModel
                .findOne({ organizationId, facilityId }, { orderId: 1 })
                .sort({ orderId: -1 })
                .lean();
            const orderId = highestOrderId?.orderId ? highestOrderId?.orderId + 1 : 1;

            // Create invoice
            const invoice = new this.InvoiceModel({
                createdBy: user._id,
                invoiceNumber,
                orderId,
                userId,
                organizationId,
                facilityId,
                discountedBy: validatedCart.discountedBy ?? null,
                paymentBy: purchaseRequest.paymentBy ?? user._id,
                purchaseItems,
                productItem: productItems,
                customPackageItems,
                subTotal: validatedCart.subTotal.toFixed(2),
                discount: validatedCart.discount.toFixed(2),
                cartDiscount: validatedCart.cartDiscount,
                cartDiscountType: validatedCart.cartDiscount > 0 ? DiscountType.FLAT : undefined,
                cartDiscountAmount: validatedCart.cartDiscountAmount.toString(),
                totalGstValue: validatedCart.totalGstValue.toFixed(2),
                totalAmountAfterGst: validatedCart.totalAmountAfterGst.toFixed(2),
                roundOff: validatedCart.roundOff.toFixed(2),
                grandTotal: validatedCart.grandTotal.toFixed(2),
                amountInWords: this.convertAmountToWords(Math.floor(validatedCart.grandTotal)),
                invoiceDate: new Date(),
                platform,
                paymentStatus: paymentDetailsData[0].paymentStatus,
                paymentDetails: paymentDetailsData,
                isSplittedPayment,
                amountPaid,
                clientDetails: {
                    _id: billingDetails[0]?.clientDetails?.userId || '',
                    customerId: billingDetails[0]?.clientDetails?.customerId || '',
                    name: billingDetails[0]?.clientDetails?.name || '',
                    email: billingDetails[0]?.clientDetails?.email || '',
                    phone: billingDetails[0]?.clientDetails?.phone || '',
                },
                clientBillingDetails: {
                    _id: clientBillingDetails?._id || '',
                    customerId: clientBillingDetails?.customerId || '',
                    name: clientBillingDetails?.name || '',
                    addressLine1: clientBillingDetails?.addressLine1 || '',
                    addressLine2: clientBillingDetails?.addressLine2 || '',
                    postalCode: clientBillingDetails?.postalCode || '',
                    cityId: new Types.ObjectId(clientBillingDetails?.cityId) || '',
                    cityName: clientBillingDetails?.cityName || '',
                    stateId: new Types.ObjectId(clientBillingDetails?.stateId) || '',
                    stateName: clientBillingDetails?.stateName || '',
                    gstNumber: clientBillingDetails?.gstNumber,
                    utCode: clientBillingDetails?.utCode || '',
                },
                billingDetails: {
                    facilityName: billingDetails[0]?.billingDetails?.facilityName || '',
                    billingName: billingDetails[0]?.billingDetails?.billingName || '',
                    gstNumber: billingDetails[0]?.billingDetails?.gstNumber || '',
                    email: billingDetails[0]?.billingDetails?.email || '',
                    phone: billingDetails[0]?.billingDetails?.phone || '',
                    addressLine1: billingDetails[0]?.billingDetails?.addressLine1 || '',
                    addressLine2: billingDetails[0]?.billingDetails?.addressLine2 || '',
                    postalCode: billingDetails[0]?.billingDetails?.postalCode || '',
                    cityId: new Types.ObjectId(billingDetails[0]?.billingDetails?.cityId) || '',
                    cityName: billingDetails[0]?.billingDetails?.cityName || '',
                    stateId: new Types.ObjectId(billingDetails[0]?.billingDetails?.stateId) || '',
                    stateName: billingDetails[0]?.billingDetails?.stateName || '',
                    utCode: billingDetails[0]?.billingDetails?.utCode || '',
                },
                isForBusiness,
            });

            // Save invoice and store for use in finally block
            savedInvoice = await invoice.save({ session });
            invoiceId = savedInvoice._id;

            // Create purchase records for services
            let membershipId = '';
            const purchases = [];
            const newPurchaseItems = [];

            // Process bundled pricing packages
            for (const item of purchaseItems) {
                const packageDetails = await this.PricingModel.findOne({
                    _id: new Types.ObjectId(item.packageId),
                }).lean();

                if (!packageDetails) {
                    throw new NotFoundException(`Package with ID ${item.packageId} not found`);
                }

                if (packageDetails?.membershipId) {
                    membershipId = packageDetails?.membershipId.toString() || '';
                }

                // Handle bundled pricing packages
                if (packageDetails.isBundledPricing && packageDetails.pricingIds && packageDetails.pricingIds.length > 0) {
                    // Add bundled packages to newPurchaseItems
                    for (const bundledId of packageDetails.pricingIds) {
                        newPurchaseItems.push({
                            packageId: bundledId,
                            quantity: item.quantity,
                            isBundledPricing: true,
                            bundledPricingId: item.packageId
                        });
                    }
                } else {
                    // Add regular package to newPurchaseItems
                    newPurchaseItems.push({
                        packageId: item.packageId,
                        quantity: item.quantity,
                        isBundledPricing: false
                    });
                }
            }

            // Create purchase records for all items (bundled and regular)
            for (const item of newPurchaseItems) {
                const packageDetails = await this.PricingModel.findOne({
                    _id: new Types.ObjectId(item.packageId),
                });

                if (!packageDetails) {
                    throw new NotFoundException(`Package with ID ${item.packageId} not found`);
                }

                if (packageDetails?.membershipId && !membershipId) {
                    membershipId = packageDetails?.membershipId.toString() || '';
                }

                for (let i = 0; i < item.quantity; i++) {
                    const startDate = new Date();
                    const endDate = this.calculateEndDate(
                        startDate,
                        packageDetails.expiredInDays,
                        packageDetails.durationUnit,
                    );

                    purchases.push({
                        invoiceId,
                        packageId: item.packageId,
                        userId,
                        organizationId,
                        facilityId,
                        ...(item.isBundledPricing && {
                            bundledPricingId: item.bundledPricingId,
                        }),
                        purchasedBy: user._id,
                        membershipId: packageDetails?.membershipId ? packageDetails?.membershipId : null,
                        purchaseDate: new Date(),
                        paymentStatus: paymentDetailsData[0].paymentStatus,
                        isExpired: false,
                        sessionType: packageDetails.services.sessionType,
                        ...(packageDetails.services.sessionType === (SessionType.UNLIMITED || SessionType.DAY_PASS) && {
                            sessionPerDay: packageDetails.services.sessionPerDay,
                        }),
                        ...(packageDetails.services.sessionType === SessionType.DAY_PASS && {
                            dayPassLimit: packageDetails.services.dayPassLimit
                        }),
                        totalSessions: packageDetails.services.sessionType === SessionType.SINGLE ? 1 : packageDetails.services.sessionCount,
                        sessionConsumed: 0,
                        startDate: startDate, // new Date(startDate.setHours(0, 0, 0, 0)),
                        endDate: endDate, // new Date(endDate.setHours(23, 59, 59, 999)),
                    });
                }
            }

            if (purchases.length > 0) {
                await this.PurchaseModel.insertMany(purchases, { session });
            }

            // Update inventory for products
            for (const item of productItems) {
                await this.InventoryModel.findOneAndUpdate(
                    { _id: item.inventoryId },
                    { $inc: { quantity: -Number(item.quantity) } },
                    { session },
                );
            }

            // Handle membership assignment if needed
            if (membershipId !== '') {
                const clientProfile = await this.ClientModel.findOne({ userId });
                if (!clientProfile?.membershipId) {
                    const checkCounter = await this.MembershipModel.findById(membershipId);

                    if (!checkCounter?.lastcounter || checkCounter?.lastcounter == 0) {
                        await this.ClientModel.findOneAndUpdate(
                            { userId },
                            { membershipId: `${checkCounter.prefix}-${checkCounter.counter}`.toString() },
                            { session },
                        );
                        await this.MembershipModel.findByIdAndUpdate(
                            membershipId,
                            { lastcounter: checkCounter.counter + 1 },
                            { session },
                        );
                    } else {
                        await this.ClientModel.findOneAndUpdate(
                            { userId },
                            { membershipId: `${checkCounter.prefix}-${checkCounter.lastcounter}`.toString() },
                            { session },
                        );
                        await this.MembershipModel.findByIdAndUpdate(
                            membershipId,
                            { lastcounter: checkCounter.lastcounter + 1 },
                            { session },
                        );
                    }
                }
            }

            // Commit transaction
            await session.commitTransaction();

            return {
                success: true,
                message: 'Purchase completed successfully',
                invoiceId: invoiceId.toString(),
                invoiceNumber,
                orderId,
                grandTotal: validatedCart.grandTotal,
                amountPaid: validatedCart.amountPaid,
            };
        } catch (error) {
            // Abort transaction on error
            await session.abortTransaction();

            // Log the error for debugging
            console.error('Error processing purchase:', error);

            // Return structured error response
            if (error instanceof NotFoundException) {
                return {
                    success: false,
                    message: 'Purchase failed: Item not found',
                    error: error.message,
                };
            } else if (error instanceof BadRequestException) {
                return {
                    success: false,
                    message: 'Purchase failed: Invalid request',
                    error: error.message,
                };
            } else if (error instanceof HttpException) {
                return {
                    success: false,
                    message: 'Purchase failed',
                    error: error.message,
                };
            }

            // For unexpected errors, throw internal server error
            throw new InternalServerErrorException('An error occurred while processing the purchase');
        } finally {
            session.endSession();

            // Generate invoice if payment is not pending and invoice was saved successfully
            try {
                if (savedInvoice && paymentDetailsData && paymentDetailsData.length > 0 &&
                    paymentDetailsData[0].paymentStatus !== PaymentStatus.PENDING) {
                    const organization = await this.UserModel.findById(organizationId).select("email");
                    const organizationEmail = organization?.email || "";
                    await this.invoiceService.generateInvoice(savedInvoice, organizationEmail);
                }
            } catch (invoiceError) {
                console.error('Error generating invoice:', invoiceError);
                // Don't throw error here as the purchase was successful
            }
        }
    }

    private calculateEndDate(startDate: Date, expireIn: number, durationUnit: string): Date {
        const endDate = new Date(startDate);

        switch (durationUnit) {
            case DurationUnit.DAYS:
                endDate.setDate(endDate.getDate() + expireIn);
                break;
            case DurationUnit.MONTHS:
                endDate.setMonth(endDate.getMonth() + expireIn);
                break;
            case DurationUnit.YEARS:
                endDate.setFullYear(endDate.getFullYear() + expireIn);
                break;
            default:
                throw new Error(`Invalid duration unit: ${durationUnit}`);
        }

        return endDate;
    }

    private mapPromotionTypeToDiscountType(promotionType: DiscountType): string {
        switch (promotionType) {
            case DiscountType.PERCENTAGE:
                return DiscountType.PERCENTAGE;
            case DiscountType.FLAT:
                return DiscountType.FLAT;
            default:
                return DiscountType.FLAT;
        }
    }

    async purchaseProduct(productItem: any, cartDiscount: any, cartDiscountType: any, productSubTotal: number) {
        let invoiceSubTotal = 0;
        let invoiveDiscountExculdeCart = 0;
        let invoiveDiscountIncludeCart = 0;
        let invoiceGstAmount = 0;
        let invoiceAmountAfterGst = 0;
        let roundOff: any;
        let grandTotal = 0;
        let discountExcludeCart = 0;
        let discountIncludeCart = 0;
        let gstAmount = 0;
        let totalAmountExcludeCartDiscount = 0;
        let productDetail: any = {};
        let variantProductDetail = {};
        let finalProductDetail: any = {};
        let finalProduct = [];
        let totalFlatCartDiscountAllocated = 0;
        for (let i = 0; i < productItem.length; i++) {
            const item = productItem[i];
            const inventoryDetail: any = await this.InventoryModel.findOne({ _id: item.inventoryId });

            if (!inventoryDetail) {
                throw new NotFoundException(`Product  with ID ${item.productId} not found`);
            }
            const { mrp, salePrice, discount, quantity } = inventoryDetail;
            if (quantity === 0) {
                throw new BadRequestException("The Product is Out of Stock.");
            }
            if (quantity < item.quantity) {
                throw new BadRequestException("The requested purchase quantity exceeds the available stock.");
            }

            if (item.productType === ProductType.SIMPLE) {
                productDetail = await this.productModel.findOne({ _id: item.productId });
            }
            if (item.productType === ProductType.VARIABLE) {
                variantProductDetail = await this.productVariantModel.findOne({ _id: item.productVariantId }).populate({ path: "productId" });
                productDetail = await this.productModel.findOne({ _id: item.productId });
            }
            const totalUnitPrice = Number(item.quantity) * Number(inventoryDetail?.salePrice);
            invoiceSubTotal += totalUnitPrice;

            discountExcludeCart = Number(item.quantity) * Number(((Number(inventoryDetail?.discount) / 100) * Number(inventoryDetail?.salePrice)).toFixed(2));

            invoiveDiscountExculdeCart += discountExcludeCart;
            totalAmountExcludeCartDiscount = totalUnitPrice - discountExcludeCart;
            if (cartDiscount && cartDiscountType === DiscountType.PERCENTAGE) {
                discountIncludeCart = Number(((Number(cartDiscount) / 100) * Number(totalAmountExcludeCartDiscount)).toFixed(2));
                gstAmount = Number((totalUnitPrice - (discountExcludeCart + discountIncludeCart)) * (Number(productDetail?.gst) / 100));
                invoiceGstAmount += gstAmount;
                invoiveDiscountIncludeCart += discountIncludeCart;
            } else if (cartDiscount && cartDiscountType === DiscountType.FLAT) {
                const itemShare = totalAmountExcludeCartDiscount / productSubTotal;

                if (i === productItem.length - 1) {
                    discountIncludeCart = Number((Number(cartDiscount) - totalFlatCartDiscountAllocated).toFixed(2));
                } else {
                    discountIncludeCart = Number((itemShare * Number(cartDiscount)).toFixed(2));
                    totalFlatCartDiscountAllocated += discountIncludeCart;
                }

                invoiveDiscountIncludeCart += discountIncludeCart;
                gstAmount = Number((totalUnitPrice - (discountExcludeCart + discountIncludeCart)) * (Number(productDetail?.gst) / 100));
                invoiceGstAmount += gstAmount;
            } else {
                gstAmount = Number((totalAmountExcludeCartDiscount * (Number(productDetail?.gst) / 100)).toFixed(2));
                invoiceGstAmount += gstAmount;
            }

            invoiceAmountAfterGst = invoiceSubTotal + invoiceGstAmount - (invoiveDiscountIncludeCart + invoiveDiscountExculdeCart) || 0;
            roundOff = (invoiceAmountAfterGst - Math.floor(invoiceAmountAfterGst)).toFixed(2);
            grandTotal = Math.floor(invoiceAmountAfterGst);
            finalProductDetail = item.productType === ProductType.VARIABLE ? variantProductDetail : productDetail;
            const hsnOrSacCode: any = productDetail.hsn || "";
            finalProduct.push({
                productName: finalProductDetail.title || finalProductDetail.name,
                sku: finalProductDetail.sku,
                productId: item.productId,
                productVariantId: item.productVariantId,
                quantity: item.quantity,
                discountExcludeCart,
                discountIncludeCart,
                hsnOrSacCode: hsnOrSacCode,
                salePrice: inventoryDetail.salePrice,
                finalPrice: inventoryDetail.discountPrice,
                discount: item.discount,
                tax: item.tax,
                mrp: item.mrp,
                gstAmount: gstAmount,
            });
            const updatedInventory = await this.InventoryModel.findOneAndUpdate({ _id: item.inventoryId }, { $inc: { quantity: -Number(item.quantity) } }, { new: true });
        }

        return {
            invoiceSubTotal,
            invoiveDiscountExculdeCart,
            invoiveDiscountIncludeCart,
            invoiceGstAmount,
            invoiceAmountAfterGst,
            roundOff,
            grandTotal,
            discountExcludeCart,
            discountIncludeCart,
            gstAmount,
            totalAmountExcludeCartDiscount,
            productDetail: finalProduct,
        };
    }

    async purchaseCustomPackage(customPackageItems: any, cartDiscount: any, cartDiscountType: any, customPackageSubTotal: number, checkValidCustomPackage: any) {
        let invoiceSubTotal = 0;
        let invoiveDiscountExculdeCart = 0;
        let invoiveDiscountIncludeCart = 0;
        let invoiceGstAmount = 0;
        let invoiceAmountAfterGst = 0;
        let roundOff: any;
        let grandTotal = 0;
        let finalProduct = [];
        for (let i = 0; i < customPackageItems.length; i++) {
            const item = customPackageItems[i];
            let gstAmount = 0;
            let discountIncludeCart = 0;
            let discountExcludeCart = 0;
            let totalAmountExcludeCartDiscount = 0;
            let totalFlatCartDiscountAllocated = 0;
            const packageData = checkValidCustomPackage.find((pkg) => pkg._id.toString() === item.customPackageId.toString());
            if (!packageData) throw new NotFoundException(`Custom package  with ID ${item.customPackageId} not found`);
            const totalUnitPrice = Number(item.quantity) * Number(packageData?.quantity * Number(packageData?.unitPrice));
            invoiceSubTotal += totalUnitPrice;

            if (packageData?.discount?.type === DiscountType.PERCENTAGE) {
                discountExcludeCart =
                    Number(item.quantity) * Number(((Number(packageData?.discount?.value) / 100) * Number(packageData?.quantity * Number(packageData?.unitPrice))).toFixed(2));
            } else if (packageData?.discount?.type === DiscountType.FLAT) {
                discountExcludeCart = Number(item.quantity) * Number(Number(packageData?.discount?.value).toFixed(2));
            }

            invoiveDiscountExculdeCart += discountExcludeCart;
            totalAmountExcludeCartDiscount = totalUnitPrice - discountExcludeCart;
            if (cartDiscount && cartDiscountType === DiscountType.PERCENTAGE) {
                discountIncludeCart = Number(((Number(cartDiscount) / 100) * Number(totalAmountExcludeCartDiscount)).toFixed(2));
                gstAmount = Number((totalUnitPrice - (discountExcludeCart + discountIncludeCart)) * (Number(packageData?.tax) / 100));
                invoiceGstAmount += gstAmount;
                invoiveDiscountIncludeCart += discountIncludeCart;
            } else if (cartDiscount && cartDiscountType === DiscountType.FLAT) {
                const itemShare = totalAmountExcludeCartDiscount / customPackageSubTotal;

                if (i === customPackageItems.length - 1) {
                    discountIncludeCart = Number((Number(cartDiscount) - totalFlatCartDiscountAllocated).toFixed(2));
                } else {
                    discountIncludeCart = Number((itemShare * Number(cartDiscount)).toFixed(2));
                    totalFlatCartDiscountAllocated += discountIncludeCart;
                }

                invoiveDiscountIncludeCart += discountIncludeCart;
                gstAmount = Number((totalUnitPrice - (discountExcludeCart + discountIncludeCart)) * (Number(packageData?.tax) / 100));
                invoiceGstAmount += gstAmount;
            } else {
                gstAmount = Number((totalAmountExcludeCartDiscount * (Number(packageData?.tax) / 100)).toFixed(2));
                invoiceGstAmount += gstAmount;
            }
            invoiceAmountAfterGst = invoiceSubTotal + invoiceGstAmount - (invoiveDiscountIncludeCart + invoiveDiscountExculdeCart) || 0;
            roundOff = (invoiceAmountAfterGst - Math.floor(invoiceAmountAfterGst)).toFixed(2);
            grandTotal = Math.floor(invoiceAmountAfterGst);
            finalProduct.push({
                packageName: packageData.name,
                customPackageId: packageData._id,
                quantity: packageData.quantity,
                discountExcludeCart,
                discountIncludeCart,
                hsnOrSacCode: packageData.hsnOrSacCode || "",
                unitPrice: packageData.unitPrice,
                finalPrice: packageData.total,
                discountType: packageData?.discount?.type,
                discountValue: packageData?.discount?.value,
                tax: packageData.tax,
                gstAmount: gstAmount,
            });
        }

        return {
            invoiceSubTotal,
            invoiveDiscountExculdeCart,
            invoiveDiscountIncludeCart,
            invoiceGstAmount,
            invoiceAmountAfterGst,
            roundOff,
            grandTotal,
            productDetail: finalProduct,
        };
    }
    async formatInvoiceData(invoice: any) {
        let pipeline = this.usersPipe.invoiceAllData(invoice._id);
        const invoiceData = await this.InvoiceModel.aggregate(pipeline);
        return invoiceData[0];
    }
    async purchaseListByClassType(classType: string, user: any, pricingListDTO: PricingListDTO): Promise<any> {
        let { startDate, endDate, facilityIds, serviceCategoryIds, page, pageSize, search } = pricingListDTO;
        if (!classType) {
            throw new BadRequestException("Class type is required");
        }

        pageSize = pageSize ?? 10;
        page = page ?? 1;
        const skip = pageSize * (page - 1);
        const query: any = {};
        let ordId;

        if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            query.organizationId = user._id;
            ordId = user._id;
        }

        if ([ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER].includes(user.role.type)) {
            const staffDetails = await this.StaffDetailsModel.findOne({ userId: user._id }, { organizationId: 1, facilityId: 1 }).exec();

            query.organizationId = staffDetails.organizationId;
            query.facilityId = { $in: staffDetails.facilityId.map((id) => new Types.ObjectId(id)) };
            ordId = staffDetails.organizationId;
        }

        if (startDate) {
            query.startDate = { $gte: new Date(startDate) };
        }
        if (endDate) {
            query.endDate = { $lte: new Date(endDate) };
        }
        if (facilityIds) {
            query.facilityId = { $in: facilityIds.map((id) => new Types.ObjectId(id)) };
        }

        const pipeline: any = [
            {
                $match: query,
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "pricingDetails",
                    pipeline: [
                        {
                            $match: {
                                "services.type": classType,
                                ...(serviceCategoryIds && {
                                    "services.serviceCategory": { $in: serviceCategoryIds.map((id) => new Types.ObjectId(id)) },
                                }),
                            },
                        },
                        {
                            $project: {
                                name: 1,
                                services: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$pricingDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "userDetails",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                name: 1,
                                email: 1,
                                mobile: 1,
                                firstName: 1,
                                lastName: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$userDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $lookup: {
                    from: "clients",
                    localField: "userId",
                    foreignField: "userId",
                    as: "userProfileDetails",
                    pipeline: [
                        {
                            $project: {
                                clientId: 1,
                                organizationId: 1,
                                membershipId: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$userProfileDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $match: {
                    "userProfileDetails.organizationId": new Types.ObjectId(ordId),
                },
            },
            {
                $lookup: {
                    from: "facilities",
                    localField: "facilityId",
                    foreignField: "_id",
                    as: "facilityDetails",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                facilityName: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$facilityDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $lookup: {
                    from: "services",
                    localField: "pricingDetails.services.serviceCategory",
                    foreignField: "_id",
                    as: "servicesDetails",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$servicesDetails",
                    preserveNullAndEmptyArrays: false,
                },
            }, {
                $lookup: {
                    from: "schedulings",
                    let: {
                        purchaseId: "$_id",
                        clientId: "$userId",
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$purchaseId", "$$purchaseId"] },
                                        { $eq: ["$clientId", "$$clientId"] },
                                        { $ne: ["$scheduleStatus", ScheduleStatusType.CANCELED] },
                                    ],
                                },
                            },
                        },
                        {
                            $group: {
                                _id: "$date",
                            },
                        },
                    ],
                    as: "bookedDates",
                },
            },
            {
                $addFields: {
                    consumedDayPassLimit: { $size: "$bookedDates" },
                },
            },
            {
                $addFields: {
                    remainingSessions: {
                        $switch: {
                            branches: [
                                {
                                    case: { $eq: ["$pricingDetails.services.sessionType", "single"] },
                                    then: { $cond: [{ $eq: ["$sessionConsumed", 0] }, 1, 0] },
                                },
                                {
                                    case: { $eq: ["$pricingDetails.services.sessionType", "multiple"] },
                                    then: {
                                        $max: [{ $subtract: ["$totalSessions", "$sessionConsumed"] }, 0],
                                    },
                                },
                                {
                                    case: { $eq: ["$pricingDetails.services.sessionType", "day_pass"] },
                                    then: {
                                        $concat: [
                                            {
                                                $toString: {
                                                    $max: [
                                                        {
                                                            $subtract: [{ $subtract: ["$dayPassLimit", "$consumedDayPassLimit"] }, "$sessionShared"],
                                                        },
                                                        0,
                                                    ],
                                                },
                                            },
                                            " day pass(es)",
                                        ],
                                    },
                                },
                            ],
                            default: 9999,
                        },
                    },
                },
            },
            {
                $project: {
                    customerId: "$userProfileDetails.clientId",
                    // clientName: '$userDetails.name',
                    clientName: {
                        $concat: ["$userDetails.firstName", " ", "$userDetails.lastName"],
                    },
                    membershipId: "$userProfileDetails.membershipId",
                    clientId: "$userDetails._id",
                    facilityId: "$facilityDetails._id",
                    packageName: "$pricingDetails.name",
                    date: "$purchaseDate",
                    email: "$userDetails.email",
                    mobile: "$userDetails.mobile",
                    remainingSessions: "$remainingSessions",
                    location: "$facilityDetails.facilityName",
                    serviceCategory: "$servicesDetails.name",
                    paymentStatus: 1,
                    classType: "$pricingDetails.services.type",
                    suspensions: 1,
                    startDate: 1,
                    endDate: 1,
                    isActive: 1,
                    createdAt: 1,
                    updatedAt: 1,
                    sessionType: 1,
                },
            },
        ];
        if (search) {
            pipeline.push({
                $match: {
                    $or: [
                        { clientName: { $regex: search, $options: "i" } },
                        { email: { $regex: search, $options: "i" } },
                        { packageName: { $regex: search, $options: "i" } },
                        { location: { $regex: search, $options: "i" } },
                        { serviceCategory: { $regex: search, $options: "i" } },
                    ],
                },
            });
        }

        const facetPipeline = [
            {
                $facet: {
                    total: [{ $count: "count" }],
                    data: [
                        { $sort: { createdAt: -1 } },
                        { $skip: skip },
                        { $limit: pageSize },
                        {
                            $set: {
                                suspensions: {
                                    $filter: {
                                        input: { $cond: ["$suspensions", "$suspensions", []] },
                                        as: "item",
                                        cond: {
                                            $and: [{ $ne: ["$$item.isResumed", true] }, { $gt: ["$$item.endDate", new Date()] }],
                                        },
                                    },
                                },
                            },
                        },
                        {
                            $set: {
                                isSuspended: {
                                    $cond: [{ $gt: [{ $size: "$suspensions" }, 0] }, true, false],
                                },
                            },
                        },
                    ],
                },
            },
            {
                $project: {
                    total: { $arrayElemAt: ["$total.count", 0] },
                    data: 1,
                },
            },
        ];

        const result = await this.PurchaseModel.aggregate([...pipeline, ...facetPipeline]);
        return result[0];
    }
    async getPurchasesForUser(user: any, getPackageDto?: GetPackagesDto): Promise<Purchase[]> {
        const query = { userId: user._id };
        let purchases = await this.PurchaseModel.find(query).populate([
            { path: "organizationId", select: "_id name email" },
            { path: "userId", select: "_id firstName lastName email" },
            { path: "packageId", select: "_id name price expiredInDays durationUnit services" },
            { path: "purchasedBy", select: "_id name firstName lastName email" },
        ]);
        if (getPackageDto.classType) {
            purchases = purchases.filter((item: any) => item.packageId?.services?.type === getPackageDto.classType);
        }
        return purchases;
    }
    async getPurchasesForOrganization(user: any, getPackageDto?: GetPackagesDto): Promise<Purchase[]> {
        const query = { organizationId: user._id };
        if (getPackageDto.clientId) {
            query["userId"] = getPackageDto.clientId;
        }
        let purchases = await this.PurchaseModel.find(query).populate([
            { path: "organizationId", select: "_id name email" },
            { path: "userId", select: "_id firstName lastName email" },
            { path: "packageId", select: "_id name price expiredInDays durationUnit services" },
            { path: "purchasedBy", select: "_id name firstName lastName email" },
        ]);
        if (getPackageDto.classType) {
            purchases = purchases.filter((item: any) => item.packageId?.services?.type === getPackageDto.classType);
        }
        return purchases;
    }
    async getPurchasesForWebmaster(user: any, getPackageDto?: GetPackagesDto): Promise<Purchase[]> {
        const staffDetails = await this.StaffDetailsModel.findOne({ userId: user._id }, { organizationId: 1 }).exec();
        const query = { organizationId: staffDetails.organizationId };
        if (getPackageDto.clientId) {
            query["userId"] = getPackageDto.clientId;
        }
        let purchases = await this.PurchaseModel.find(query).populate([
            { path: "organizationId", select: "_id name email" },
            { path: "userId", select: "_id firstName lastName email" },
            { path: "packageId", select: "_id name price expiredInDays durationUnit services" },
            { path: "purchasedBy", select: "_id name firstName lastName email" },
        ]);
        if (getPackageDto.classType) {
            purchases = purchases.filter((item: any) => item.packageId?.services?.type === getPackageDto.classType);
        }
        return purchases;
    }
    async getPurchasesForFrontDesk(user: any, getPackageDto?: GetPackagesDto): Promise<Purchase[]> {
        const staffDetails = await this.StaffDetailsModel.findOne({ userId: user._id }, { organizationId: 1 }).exec();
        const query = { organizationId: staffDetails.organizationId };
        if (getPackageDto.clientId) {
            query["userId"] = getPackageDto.clientId;
        }
        let purchases = await this.PurchaseModel.find(query).populate([
            { path: "organizationId", select: "_id name email" },
            { path: "userId", select: "_id firstName lastName email" },
            { path: "packageId", select: "_id name price expiredInDays durationUnit services" },
            { path: "purchasedBy", select: "_id name firstName lastName email" },
        ]);
        if (getPackageDto.classType) {
            purchases = purchases.filter((item: any) => item.packageId?.services?.type === getPackageDto.classType);
        }
        return purchases;
    }
    async getPurchaseById(id: string, user: any): Promise<Purchase> {
        const purchase = await this.PurchaseModel.findById(id).exec();
        if (!purchase) {
            throw new NotFoundException(`Purchase with ID ${id} not found`);
        }
        return purchase;
    }
    async deletePurchaseById(id: string, user: any): Promise<Purchase> {
        const deletedPurchase = await this.PurchaseModel.findByIdAndDelete(id).exec();
        if (!deletedPurchase) {
            throw new NotFoundException(`Purchase with ID ${id} not found`);
        }
        return deletedPurchase;
    }

    async activePricingByUserId(userId: string, body: PaginationDto, user: any): Promise<any> {
        const { page, pageSize } = body;
        const skip = pageSize * (page - 1);

        const query: any = {};
        query['$or'] = [
            { userId: new Types.ObjectId(userId) },
            // { sponsorUser: new Types.ObjectId(userId) }
        ]

        if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            query.organizationId = new Types.ObjectId(user._id);
            // query.userId = new Types.ObjectId(userId);
        } else if ([ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.TRAINER].includes(user.role.type)) {
            const staffDetails = await this.StaffDetailsModel.findOne({ userId: user._id }, { organizationId: 1, facilityId: 1 }).exec();

            query.organizationId = new Types.ObjectId(staffDetails.organizationId);
            // query.userId = new Types.ObjectId(userId);
            //query.facilityId = { $in: staffDetails.facilityId.map((id) => new Types.ObjectId(id)) };
        } else if (user.role.type === ENUM_ROLE_TYPE.USER) {

        } else {
            throw new BadRequestException("Access Denied");
        }

        // query.isExpired = false;
        // query.endDate = { $gte: new Date() };
        // query.startDate = { $lte: new Date() };
        // query.isActive = { $ne: false }

        const pipeline: any = [
            {
                $match: query,
            },
            {
                $lookup: {
                    from: "invoices",
                    localField: "invoiceId",
                    foreignField: "_id",
                    as: "invoiceDetails",
                },
            },
            {
                $unwind: { path: "$invoiceDetails", preserveNullAndEmptyArrays: false },
            },
            {
                $addFields: {
                    matchedPurchaseItem: {
                        $arrayElemAt: [
                            {
                                $filter: {
                                    input: "$invoiceDetails.purchaseItems",
                                    as: "item",
                                    cond: {
                                        $eq: [
                                            "$$item.packageId",
                                            {
                                                $cond: {
                                                    if: { $gt: [{ $ifNull: ["$bundledPricingId", null] }, null] },
                                                    then: "$bundledPricingId",
                                                    else: "$packageId",
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                            0,
                        ],
                    },
                },
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "pricingDetails",
                    pipeline: [
                        {
                            $lookup: {
                                from: "services",
                                let: {
                                    serviceCategoryId: "$services.serviceCategory",
                                    relationshipIds: { $ifNull: ["$services.relationShip.serviceCategory", []] },
                                    serviceSubTypeIds: { $ifNull: ["$services.relationShip.subTypeIds", []] }, // Extracting subTypeIds (appointmentType)
                                    serviceTypes: "$services.appointmentType", // Extracting appointmentType._id from pricing services
                                },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: {
                                                $or: [{ $eq: ["$_id", "$$serviceCategoryId"] }, { $in: ["$_id", "$$relationshipIds"] }],
                                            },
                                        },
                                    },
                                    {
                                        $project: { _id: 1, name: 1, appointmentType: 1 },
                                    },
                                    {
                                        $addFields: {
                                            filteredAppointmentType: {
                                                $filter: {
                                                    input: "$appointmentType",
                                                    as: "appt",
                                                    cond: {
                                                        $or: [
                                                            { $in: ["$$appt._id", "$$serviceTypes"] }, // Check in services.appointmentType
                                                            { $in: ["$$appt._id", "$$serviceSubTypeIds"] }, // Check in services.relationShip.subTypeIds
                                                        ],
                                                    },
                                                },
                                            },
                                        },
                                    },
                                ],
                                as: "serviceCategories",
                            },
                        },
                        {
                            $lookup: {
                                from: "services",
                                localField: "services.relationShip.subTypeIds",
                                foreignField: "appointmentType._id",
                                as: "subTypeDetails",
                            },
                        },
                        {
                            $addFields: {
                                subTypeNames: {
                                    $map: { input: "$subTypeDetails", as: "subType", in: "$$subType.name" },
                                },
                            },
                        },
                        {
                            $addFields: {
                                appointmentTypeName: {
                                    $reduce: {
                                        input: "$serviceCategories",
                                        initialValue: [],
                                        in: { $concatArrays: ["$$value", "$$this.filteredAppointmentType.name"] },
                                    },
                                },
                            },
                        },
                        {
                            $addFields: {
                                appointmentTypeName: { $concatArrays: ["$appointmentTypeName", "$subTypeNames"] },
                            },
                        },
                        {
                            $project: {
                                name: 1,
                                "services.type": 1,
                                serviceCategoryNames: {
                                    $map: { input: "$serviceCategories", as: "category", in: "$$category.name" },
                                },
                                appointmentTypeName: 1,
                                price: 1,
                                "services.sessionType": 1,
                                "services.sessionCount": 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: { path: "$pricingDetails", preserveNullAndEmptyArrays: false },
            },
            {
                $addFields: {
                    remainingSession: {
                        $cond: {
                            if: { $eq: ["$totalSessions", Infinity] },
                            then: "Unlimited",
                            else: {
                                $subtract: [
                                    {
                                        $cond: {
                                            if: { $eq: ["$sharePass", true] },
                                            then: "$totalSessions",
                                            else: "$totalSessions",
                                        },
                                    },
                                    "$sessionConsumed",
                                ],
                            },
                        },
                    },
                },
            },
            {
                $match: { $or: [{ remainingSession: { $gt: 0 } }, { remainingSession: "Unlimited" }] },
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "bundledPricingId",
                    foreignField: "_id",
                    as: "bundledPricingDetails",
                    pipeline: [{ $project: { _id: 1, name: 1 } }],
                },
            },
            {
                $addFields: {
                    bundledPricingName: { $arrayElemAt: ["$bundledPricingDetails.name", 0] },
                    isBundledPricing: { $gt: [{ $size: "$bundledPricingDetails" }, 0] },
                },
            },
            {
                $lookup: {
                    from: "facilities",
                    localField: "facilityId",
                    foreignField: "_id",
                    as: "facilityDetails",
                    pipeline: [{ $project: { facilityName: 1 } }],
                },
            },
            {
                $unwind: { path: "$facilityDetails", preserveNullAndEmptyArrays: false },
            },
            {
                $project: {
                    _id: 1,
                    packageId: 1,
                    userId: 1,
                    updatedAt: 1,
                    createdAt: 1,
                    packageName: "$matchedPurchaseItem.packageName",
                    location: "$facilityDetails.facilityName",
                    sessionType: 1,
                    expiryDate: "$endDate",
                    type: "$pricingDetails.services.type",
                    serviceNames: "$pricingDetails.serviceCategoryNames",
                    appointmentTypeName: "$pricingDetails.appointmentTypeName",
                    // price: "$pricingDetails.price",
                    price: {
                        $cond: {
                            if: "$sharePass",
                            then: 0,
                            else: "$matchedPurchaseItem.unitPrice",
                        },
                    },
                    bundledPricingName: 1,
                    isBundledPricing: 1,
                    totalSessions: {
                        $cond: {
                            if: { $eq: ["$totalSessions", Infinity] },
                            then: "unlimited",
                            else: "$totalSessions",
                        },
                    },
                    sessionConsumed: 1,
                    suspensions: 1,
                    isActive: 1,
                    isExpired: 1,
                    remainingSession: 1,
                    dayPassLimit: 1,
                },
            },
            {
                $facet: {
                    total: [{ $count: "count" }],
                    data: [
                        { $sort: { updatedAt: -1 } },
                        { $skip: skip },
                        { $limit: pageSize },
                        {
                            $set: {
                                suspensions: {
                                    $filter: {
                                        input: { $cond: ["$suspensions", "$suspensions", []] },
                                        as: "item",
                                        cond: {
                                            $and: [{ $ne: ["$$item.isResumed", true] }, { $gt: ["$$item.endDate", new Date()] }],
                                        },
                                    },
                                },
                            },
                        },
                        {
                            $set: {
                                isSuspended: {
                                    $cond: [{ $gt: [{ $size: "$suspensions" }, 0] }, true, false],
                                },
                            },
                        },
                    ],
                },
            },
            {
                $project: {
                    total: { $arrayElemAt: ["$total.count", 0] },
                    data: 1,
                },
            },
        ];

        const result = await this.PurchaseModel.aggregate(pipeline);
        if (result.length && result[0].data) {
            const dayPassPurchases = result.length ? result[0].data.filter(item => item.sessionType === SessionType.DAY_PASS) : [];
            const dayPassPurchaseIds = dayPassPurchases.map(item => new Types.ObjectId(item._id));

            const sessionsCount = await this.SchedulingModel.aggregate([
                {
                    $match: {
                        purchaseId: { $in: dayPassPurchaseIds },
                        scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                    },
                },
                {
                    $group: {
                        _id: { purchaseId: "$purchaseId", date: "$date" },
                    },
                },
            ]);

            const sessionsByPurchaseId = sessionsCount.reduce((acc, session) => {
                const purchaseId = session._id.purchaseId.toString();
                const date = session._id.date.toISOString().split("T")[0];
                if (!acc[purchaseId]) acc[purchaseId] = [];
                acc[purchaseId].push(date);
                return acc;
            }, {});

            const todayUTC = new Date();
            todayUTC.setUTCHours(0, 0, 0, 0);
            const todayISTDate = todayUTC.toISOString().split("T")[0];

            for (const item of result[0]?.data || []) {
                if (item.sessionType === SessionType.DAY_PASS) {

                    const bookedDates = sessionsByPurchaseId[item._id.toString()] || [];
                    const remainingDays = bookedDates.filter((date) => date < todayISTDate);
                    const consumedDayPassLimit = remainingDays.length;
                    const assignedDayPassLimit = item.dayPassLimit || 0;
                    item.remainingSession = !isNaN(assignedDayPassLimit - consumedDayPassLimit) ? `${assignedDayPassLimit - consumedDayPassLimit} x Day Pass(es)` : 0;
                }
            }

        }

        return {
            data: result.length ? result[0].data : [],
            count: result.length ? result[0].total : 0,
        };
    }

    async inactivePricingByUserId(userId: string, body: PaginationDto, organizationId: IDatabaseObjectId): Promise<any> {
        const { page, pageSize } = body;
        const skip = pageSize * (page - 1);

        const query: any = {};
        query.userId = new Types.ObjectId(userId);
        query.organizationId = new Types.ObjectId(organizationId);

        query.$or = [{ isActive: false }, { isExpired: true }, { remainingSession: { $lte: 0 } }, { endDate: { $lt: new Date() } }];

        const pipeline: any = [
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "pricingDetails",
                    pipeline: [
                        {
                            $lookup: {
                                from: "services",
                                let: {
                                    serviceCategoryId: "$services.serviceCategory",
                                    relationshipIds: { $ifNull: ["$services.relationShip.serviceCategory", []] },
                                    serviceSubTypeIds: { $ifNull: ["$services.relationShip.subTypeIds", []] }, // Extracting subTypeIds (appointmentType)
                                    serviceTypes: "$services.appointmentType", // Extracting appointmentType._id from pricing services
                                },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: {
                                                $or: [{ $eq: ["$_id", "$$serviceCategoryId"] }, { $in: ["$_id", "$$relationshipIds"] }],
                                            },
                                        },
                                    },
                                    {
                                        $project: { _id: 1, name: 1, appointmentType: 1 },
                                    },
                                    {
                                        $addFields: {
                                            filteredAppointmentType: {
                                                $filter: {
                                                    input: "$appointmentType",
                                                    as: "appt",
                                                    cond: {
                                                        $or: [
                                                            { $in: ["$$appt._id", "$$serviceTypes"] }, // Check in services.appointmentType
                                                            { $in: ["$$appt._id", "$$serviceSubTypeIds"] }, // Check in services.relationShip.subTypeIds
                                                        ],
                                                    },
                                                },
                                            },
                                        },
                                    },
                                ],
                                as: "serviceCategories",
                            },
                        },
                        {
                            $lookup: {
                                from: "services",
                                localField: "services.relationShip.subTypeIds",
                                foreignField: "appointmentType._id",
                                as: "subTypeDetails",
                            },
                        },
                        {
                            $addFields: {
                                subTypeNames: {
                                    $map: { input: "$subTypeDetails", as: "subType", in: "$$subType.name" },
                                },
                            },
                        },
                        {
                            $addFields: {
                                appointmentTypeName: {
                                    $reduce: {
                                        input: "$serviceCategories",
                                        initialValue: [],
                                        in: { $concatArrays: ["$$value", "$$this.filteredAppointmentType.name"] },
                                    },
                                },
                            },
                        },
                        {
                            $addFields: {
                                appointmentTypeName: { $concatArrays: ["$appointmentTypeName", "$subTypeNames"] },
                            },
                        },
                        {
                            $project: {
                                name: 1,
                                "services.type": 1,
                                serviceCategoryNames: {
                                    $map: { input: "$serviceCategories", as: "category", in: "$$category.name" },
                                },
                                appointmentTypeName: 1,
                                price: 1,
                                "services.sessionType": 1,
                                "services.sessionCount": 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: { path: "$pricingDetails", preserveNullAndEmptyArrays: false },
            },
            {
                $addFields: {
                    remainingSession: {
                        $cond: {
                            if: { $eq: ["$totalSessions", Infinity] },
                            then: "Unlimited",
                            else: {
                                $subtract: [
                                    {
                                        $cond: {
                                            if: { $eq: ["$sharePass", true] },
                                            then: "$totalSessions",
                                            else: "$totalSessions",
                                        },
                                    },
                                    "$sessionConsumed",
                                ],
                            },
                        },
                    },
                },
            },
            {
                $match: query,
            },
            {
                $lookup: {
                    from: "invoices",
                    localField: "invoiceId",
                    foreignField: "_id",
                    as: "invoiceDetails",
                },
            },
            {
                $unwind: { path: "$invoiceDetails", preserveNullAndEmptyArrays: false },
            },
            {
                $addFields: {
                    matchedPurchaseItem: {
                        $arrayElemAt: [
                            {
                                $filter: {
                                    input: "$invoiceDetails.purchaseItems",
                                    as: "item",
                                    cond: {
                                        $eq: [
                                            "$$item.packageId",
                                            {
                                                $cond: {
                                                    if: { $gt: [{ $ifNull: ["$bundledPricingId", null] }, null] },
                                                    then: "$bundledPricingId",
                                                    else: "$packageId",
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                            0,
                        ],
                    },
                },
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "bundledPricingId",
                    foreignField: "_id",
                    as: "bundledPricingDetails",
                    pipeline: [{ $project: { _id: 1, name: 1 } }],
                },
            },
            {
                $addFields: {
                    bundledPricingName: { $arrayElemAt: ["$bundledPricingDetails.name", 0] },
                    isBundledPricing: { $gt: [{ $size: "$bundledPricingDetails" }, 0] },
                },
            },
            {
                $lookup: {
                    from: "facilities",
                    localField: "facilityId",
                    foreignField: "_id",
                    as: "facilityDetails",
                    pipeline: [{ $project: { facilityName: 1 } }],
                },
            },
            {
                $unwind: { path: "$facilityDetails", preserveNullAndEmptyArrays: false },
            },
            {
                $project: {
                    _id: 1,
                    userId: 1,
                    packageId: 1,
                    updatedAt: 1,
                    createdAt: 1,
                    packageName: "$matchedPurchaseItem.packageName",
                    location: "$facilityDetails.facilityName",
                    sessionType: 1,
                    expiryDate: "$endDate",
                    type: "$pricingDetails.services.type",
                    serviceNames: "$pricingDetails.serviceCategoryNames",
                    appointmentTypeName: "$pricingDetails.appointmentTypeName",
                    // price: "$pricingDetails.price",
                    price: {
                        $cond: {
                            if: "$sharePass",
                            then: 0,
                            else: "$matchedPurchaseItem.unitPrice",
                        },
                    },
                    bundledPricingName: 1,
                    isBundledPricing: 1,
                    totalSessions: {
                        $cond: {
                            if: { $eq: ["$totalSessions", Infinity] },
                            then: "unlimited",
                            else: "$totalSessions",
                        },
                    },
                    sessionConsumed: 1,
                    isActive: 1,
                    isExpired: 1,
                    suspensions: 1,
                    remainingSession: 1,
                    paymentStatus: 1,
                    dayPassLimit: 1,
                },
            },
            {
                $facet: {
                    total: [{ $count: "count" }],
                    data: [
                        { $sort: { updatedAt: -1 } },
                        { $skip: skip },
                        { $limit: pageSize },
                        {
                            $set: {
                                suspensions: {
                                    $filter: {
                                        input: { $cond: ["$suspensions", "$suspensions", []] },
                                        as: "item",
                                        cond: {
                                            $and: [{ $ne: ["$$item.isResumed", true] }, { $gt: ["$$item.endDate", new Date()] }],
                                        },
                                    },
                                },
                            },
                        },
                        {
                            $set: {
                                isSuspended: {
                                    $cond: [{ $gt: [{ $size: "$suspensions" }, 0] }, true, false],
                                },
                            },
                        },
                    ],
                },
            },
            {
                $project: {
                    total: { $arrayElemAt: ["$total.count", 0] },
                    data: 1,
                },
            },
        ];

        const result = await this.PurchaseModel.aggregate(pipeline);
        if (result.length && result[0].data) {
            const dayPassPurchases = result.length ? result[0].data.filter(item => item.sessionType === SessionType.DAY_PASS) : [];
            const dayPassPurchaseIds = dayPassPurchases.map(item => new Types.ObjectId(item._id));

            const sessionsCount = await this.SchedulingModel.aggregate([
                {
                    $match: {
                        purchaseId: { $in: dayPassPurchaseIds },
                        scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                    },
                },
                {
                    $group: {
                        _id: { purchaseId: "$purchaseId", date: "$date" },
                    },
                },
            ]);

            const sessionsByPurchaseId = sessionsCount.reduce((acc, session) => {
                const purchaseId = session._id.purchaseId.toString();
                const date = session._id.date.toISOString().split("T")[0];
                if (!acc[purchaseId]) acc[purchaseId] = [];
                acc[purchaseId].push(date);
                return acc;
            }, {});

            const todayUTC = new Date();
            todayUTC.setUTCHours(0, 0, 0, 0);
            const todayISTDate = todayUTC.toISOString().split("T")[0];

            for (const item of result[0]?.data || []) {
                if (item.sessionType === SessionType.DAY_PASS) {

                    const bookedDates = sessionsByPurchaseId[item._id.toString()] || [];
                    const remainingDays = bookedDates.filter((date) => date < todayISTDate);
                    const consumedDayPassLimit = remainingDays.length;
                    const assignedDayPassLimit = item.dayPassLimit || 0;
                    item.remainingSession = !isNaN(assignedDayPassLimit - consumedDayPassLimit) ? `${assignedDayPassLimit - consumedDayPassLimit} x Day Pass(es)` : 0;
                }
            }

        }

        return {
            data: result.length ? result[0].data : [],
            count: result.length ? result[0].total : 0,
        };
    }

    private async getOrganizationId(user: IUserDocument) {
        const { role } = user;
        switch (role.type) {
            case ENUM_ROLE_TYPE.USER:
                return user._id

            case ENUM_ROLE_TYPE.ORGANIZATION:
                return user._id;

            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            case ENUM_ROLE_TYPE.TRAINER:
            case ENUM_ROLE_TYPE.WEB_MASTER:
                const staffDetails = await this.StaffDetailsModel.findOne({ userId: user._id }, { organizationId: 1 });
                if (!staffDetails) return null;
                return staffDetails.organizationId;

            default:
                null;
        }
    }

    async getInvoiceList(organizationId: IDatabaseObjectId, body: GetInvoicesDto) {
        const { search, facilityId, classType, serviceCategory, startDate, endDate, paymentStatus, page, pageSize } = body;
        const skip = pageSize * (page - 1);
        const filter: any = { organizationId: organizationId };
        const serviceCategoryIds = serviceCategory.map((_id) => new Types.ObjectId(_id));
        if (facilityId.length) {
            filter["facilityId"] = { $in: facilityId.map((_id) => new Types.ObjectId(_id)) };
        }
        // if(classType){
        //     filter['classType'] = classType
        // }
        // if(serviceCategory.length){
        //     filter['serviceCategoryId'] = { $in: serviceCategory.map(_id => new Types.ObjectId(_id))}
        // }
        if (paymentStatus) {
            filter["paymentStatus"] = paymentStatus;
        }
        if (startDate && endDate) {
            filter["invoiceDate"] = {
                $gte: startDate,
                $lte: endDate,
            };
        }

        const agg: PipelineStage[] = [
            {
                $match: filter,
            },

            {
                $unwind: {
                    path: "$purchaseItems",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $unwind: {
                    path: "$customPackageItems",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $unwind: {
                    path: "$productItem",
                    preserveNullAndEmptyArrays: true,
                },
            },
            ...(serviceCategory.length || classType || search
                ? [
                    {
                        $lookup: {
                            from: "pricings",
                            localField: "purchaseItems.packageId",
                            foreignField: "_id",
                            as: "pricing",
                            pipeline: [
                                {
                                    $project: {
                                        name: 1,
                                        services: 1,
                                        hsnOrSacCode: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $match: {
                            pricing: {
                                $ne: [],
                            },
                        },
                    },
                ]
                : []),
            ...(classType
                ? [
                    {
                        $match: {
                            "pricing.services.type": classType,
                        },
                    },
                ]
                : []),
            ...(serviceCategoryIds.length
                ? [
                    {
                        $match: {
                            "pricing.services.serviceCategory": { $in: serviceCategoryIds },
                        },
                    },
                ]
                : []),
            ...(search
                ? [
                    {
                        $lookup: {
                            from: "users",
                            localField: "userId",
                            foreignField: "_id",
                            as: "client",
                            pipeline: [
                                {
                                    $project: {
                                        name: 1,
                                        firstName: 1,
                                        lastName: 1,
                                        mobile: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $set: {
                            client: {
                                $arrayElemAt: ["$client", 0],
                            },
                            pricing: {
                                $arrayElemAt: ["$pricing", 0],
                            },
                        },
                    },
                    {
                        $addFields: {
                            searched: {
                                $cond: [
                                    {
                                        $or: [
                                            {
                                                $regexMatch: {
                                                    input: "$client.name",
                                                    regex: search,
                                                    options: "i",
                                                },
                                            },
                                            {
                                                $regexMatch: {
                                                    input: "$client.mobile",
                                                    regex: search,
                                                    options: "i",
                                                },
                                            },
                                            {
                                                $regexMatch: {
                                                    input: "$pricing.name",
                                                    regex: search,
                                                    options: "i",
                                                },
                                            },
                                        ],
                                    },
                                    true,
                                    false,
                                ],
                            },
                        },
                    },
                    {
                        $match: {
                            searched: true,
                        },
                    },
                ]
                : []),
            {
                $group: {
                    _id: "$_id",
                    updatedAt: {
                        $first: "$updatedAt",
                    },
                },
            },
            {
                $facet: {
                    total: [
                        {
                            $count: "count",
                        },
                    ],
                    invoiceIds: [
                        {
                            $sort: {
                                updatedAt: -1,
                            },
                        },
                        {
                            $skip: skip,
                        },
                        {
                            $limit: pageSize,
                        },
                    ],
                },
            },
            {
                $replaceRoot: {
                    newRoot: {
                        total: {
                            $arrayElemAt: ["$total.count", 0],
                        },
                        invoiceIds: "$invoiceIds",
                    },
                },
            },
        ];

        const idsList = await this.InvoiceModel.aggregate(agg);
        const { total = 0, invoiceIds } = idsList.length ? idsList[0] : { total: 0, invoiceIds: [] };
        const populateFields = [
            { path: "createdBy", select: "_id name firstName lastName email" },
            {
                path: "facilityId",
                select: "_id facilityName address profilePicture paymentMethods", // ✅ Remove comma before 'paymentMethods'
                populate: {
                    path: "paymentMethods.paymentMethodId",
                    model: "PaymentMethod",
                    select: "_id name methodType shortId", // ✅ Added shortId
                },
            },
        ];

        let invoices = await this.InvoiceModel.find({ _id: { $in: invoiceIds.map((item) => item._id) } })
            .sort({ createdAt: -1 })
            .populate(populateFields);
        const updatedInvoices = invoices.map((invoice: any) => {
            if (invoice.facilityId && invoice.facilityId.paymentMethods) {
                // ✅ Convert to plain JavaScript object
                const invoiceObject = JSON.parse(JSON.stringify(invoice));

                const updatedPaymentDetails = invoiceObject.paymentDetails.map((detail: any) => {
                    const matchedMethod = invoiceObject.facilityId.paymentMethods.find(
                        (pm: any) =>
                            pm.paymentMethodId &&
                            pm.paymentMethodId.shortId &&
                            detail.paymentMethod &&
                            pm.paymentMethodId.shortId.trim().toLowerCase() === detail.paymentMethod.trim().toLowerCase(),
                    );

                    return {
                        ...detail,
                        paymentMethodName: matchedMethod ? matchedMethod.paymentMethodId.name : "Unknown",
                    };
                });

                // ✅ Update the converted object
                invoiceObject.paymentDetails = updatedPaymentDetails;
                invoiceObject.updatedPaymentDetails = updatedPaymentDetails;

                return invoiceObject; // ✅ Return modified invoice
            }

            return invoice; // ✅ Return original if no changes were made
        });
        invoices = updatedInvoices;
        return {
            count: total,
            data: invoices.map((item: any) => ({
                _id: item._id,
                createdBy: item.createdBy ? item.createdBy._id : null,
                createdByName: item.createdBy ? (item.createdBy.name ? item.createdBy.name : item.createdBy.firstName + " " + item.createdBy.lastName) : null,
                invoiceDate: item?.invoiceDate,
                invoiceNumber: item?.invoiceNumber || "",
                orderId: item?.orderId || "",
                organizationId: item.organizationId,
                userId: item?.userId,
                userName: item?.clientDetails?.name || "",
                customerId: item?.clientDetails?.customerId || "",
                staffId: null,
                staffName: "",
                totalItems: item.purchaseItems.length,
                purchaseItems: item.purchaseItems.map((pricing: any) => ({
                    _id: pricing?._id,
                    packageId: pricing?.packageId,
                    name: pricing?.packageName,
                    hsnOrSacCode: pricing?.hsnOrSacCode || "",
                    price: pricing?.unitPrice,
                    quantity: pricing?.quantity,
                })),
                productItems: item.productItem.map((product: any) => ({
                    _id: product?._id,
                    productId: product?.productId,
                    productName: product?.productName,
                    hsnorSacCode: product?.hsnOrSacCode || "",
                    price: product?.salePrice,
                    quantity: product?.quantity,
                })),
                customPackageItems: item.customPackageItems.map((pricing: any) => ({
                    _id: pricing?._id,
                    customPackageId: pricing?.customPackageId,
                    name: pricing?.packageName,
                    hsnOrSacCode: pricing?.hsnOrSacCode || "",
                    price: pricing?.unitPrice,
                    quantity: pricing?.quantity,
                })),
                facilityId: item.facilityId._id,
                facilityName: item.facilityId.facilityName,
                discount: item?.discount || 0,
                subTotal: item?.subTotal,
                total: item?.grandTotal,
                paymentStatus: item?.paymentStatus,
                paymentMethod: item?.paymentDetails?.map((detail) => detail.paymentMethod).join(", "),
                transactionId: item?.paymentDetails[0]?.transactionId,
                paymentDate: item?.paymentDetails[0]?.paymentDate,
                invoiceStatus: item?.invoiceStatus,
                refundStatus: item?.refundStatus,
                platform: item?.platform,
                updatedPaymentDetails: item?.updatedPaymentDetails?.map((detail) => detail.paymentMethodName).join(", "),
                paymentReason: item?.paymentReason,
            })),
        };
    }

    async exportInvoiceList(user: IUserDocument, body: ExportInvoicesDto, userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone) {
        const organizationId = await this.getOrganizationId(user);
        const { facilityId, classType, serviceCategory, startDate, endDate, paymentStatus } = body;
        const filter: any = { organizationId: organizationId };
        const serviceCategoryIds = serviceCategory.map((_id) => new Types.ObjectId(_id));
        if (facilityId.length) {
            filter["facilityId"] = { $in: facilityId.map((_id) => new Types.ObjectId(_id)) };
        }
        // if(classType){
        //     filter['classType'] = classType
        // }
        // if(serviceCategory.length){
        //     filter['serviceCategoryId'] = { $in: serviceCategory.map(_id => new Types.ObjectId(_id))}
        // }
        if (paymentStatus) {
            filter["paymentStatus"] = paymentStatus;
        }
        if (startDate && endDate) {
            const startOfDay = new Date(new Date(startDate).setHours(0, 0, 0, 0));
            const endOfDay = new Date(new Date(endDate).setHours(23, 59, 59, 999));
            filter["invoiceDate"] = {
                $gte: startOfDay,
                $lte: endOfDay,
            };
        }
        const agg: PipelineStage[] = [
            {
                $match: filter,
            },
            {
                $unwind: "$purchaseItems",
            },
            ...(serviceCategory.length || classType
                ? [
                    {
                        $lookup: {
                            from: "pricings",
                            localField: "purchaseItems.packageId",
                            foreignField: "_id",
                            as: "pricing",
                            pipeline: [
                                {
                                    $project: {
                                        name: 1,
                                        services: 1,
                                        hsnOrSacCode: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $match: {
                            pricing: {
                                $ne: [],
                            },
                        },
                    },
                ]
                : []),
            ...(classType
                ? [
                    {
                        $match: {
                            "pricing.services.type": classType,
                        },
                    },
                ]
                : []),
            ...(serviceCategoryIds.length
                ? [
                    {
                        $match: {
                            "pricing.services.serviceCategory": { $in: serviceCategoryIds },
                        },
                    },
                ]
                : []),
            {
                $group: {
                    _id: "$_id",
                    updatedAt: {
                        $first: "$updatedAt",
                    },
                },
            },
            {
                $facet: {
                    total: [
                        {
                            $count: "count",
                        },
                    ],
                    invoiceIds: [
                        {
                            $sort: {
                                updatedAt: -1,
                            },
                        },
                    ],
                },
            },
            {
                $replaceRoot: {
                    newRoot: {
                        total: {
                            $arrayElemAt: ["$total.count", 0],
                        },
                        invoiceIds: "$invoiceIds",
                    },
                },
            },
        ];

        const idsList = await this.InvoiceModel.aggregate(agg);
        const { total = 0, invoiceIds } = idsList.length ? idsList[0] : { total: 0, invoiceIds: [] };
        const populateFields = [
            { path: "userId", select: "_id name firstName lastName email" },
            { path: "createdBy", select: "_id name firstName lastName email" },
            { path: "cancelledBy", select: "firstName lastName name" },
            // { path: 'organizationId', select: '_id name email' },
            { path: "facilityId", select: "_id facilityName address profilePicture" },
            { path: "purchaseItems.packageId", select: "_id name price services expiredInDays durationUnit hsnOrSacCode" },
        ];
        const invoices = await this.InvoiceModel.find({ _id: { $in: invoiceIds.map((item) => item._id) } })
            .sort({ updatedAt: -1 })
            .populate(populateFields);
        const clients = await this.ClientModel.find({ userId: { $in: invoices.map((item: any) => item?.userId?._id) } }, { userId: 1, clientId: 1 });

        return {
            count: total,
            data: `Client ID, Order ID, Client Name, Billed To,Billing Address, Location, Order Date (${userTimezone}), Time (${userTimezone}), Sub Total, Gst Amount, Amount, Staff, Payment Method, Payment Status, UT Code, Notes/TXN ID, Cancelled By, Reason\n` +
                invoices.map((item: any) => {
                    const row = [
                        clients.find(client => client?.userId?.toString() === item?.userId?._id?.toString())?.clientId ?? null,
                        item.invoiceNumber,
                        item.clientDetails?.name,
                        item.clientBillingDetails?.name,
                        `"${[
                            item.clientBillingDetails?.addressLine1,
                            item.clientBillingDetails?.addressLine2,
                            item.clientBillingDetails?.cityName,
                            item.clientBillingDetails?.stateName,
                            item.clientBillingDetails?.country
                        ].filter(Boolean).join(', ')}"`,
                        item.facilityId.facilityName,
                        moment(item.invoiceDate).tz(userTimezone).format('DD/MM/YYYY'),
                        moment(item.invoiceDate).tz(userTimezone).format('HH:mm'),
                        item.subTotal,
                        item.totalGstValue,
                        item.grandTotal,
                        item.createdBy ? item.createdBy.name ? item.createdBy.name : item.createdBy.firstName + " " + item.createdBy.lastName : null,
                        `"${isArray(item.paymentDetails) ? item.paymentDetails.map(detail => detail.paymentMethod).join(', ') : item.paymentDetails.paymentMethod}"`,
                        item.paymentStatus,
                        item.clientBillingDetails?.utCode,
                        (isArray(item.paymentDetails) ? item.paymentDetails : [item.paymentDetails]).map(detail => detail?.transactionId).join(', ') ?? "",
                        item?.cancelledBy?.name ? item.cancelledBy.name : item?.cancelledBy?.firstName ? item.cancelledBy.firstName + " " + item.cancelledBy.lastName : null,
                        item.paymentReason ? item.paymentReason : " ",
                    ];
                    return row.join(',');
                }).join('\n')
        };
    }

    async getInvoiceDetails(organizationId: IDatabaseObjectId, invoiceId: string) {
        let invoice: any = await this.InvoiceModel.findOne({
            _id: invoiceId,
            organizationId: organizationId,
        }).populate([
            { path: "createdBy", select: "_id name firstName lastName email" },
            { path: "cancelledBy", select: "firstName lastName name" },
            {
                path: "facilityId",
                select: "_id facilityName paymentMethods name",
                populate: { path: "paymentMethods.paymentMethodId", model: "PaymentMethod", select: "_id name methodType" },
            }, // Nested population
        ]);
        if (invoice && invoice.facilityId && invoice.facilityId.paymentMethods) {
            const updatedPaymentDetails = invoice.paymentDetails.map((detail) => {
                const matchedMethod = invoice.facilityId.paymentMethods.find((pm) => pm.shortId.trim().toLowerCase() === detail.paymentMethod.trim().toLowerCase());
                const cleanDetail = JSON.parse(JSON.stringify(detail));
                return {
                    ...cleanDetail,
                    paymentMethodName: matchedMethod ? matchedMethod.name : "Unknown",
                };
            });
            invoice.updatedPaymentDetails = updatedPaymentDetails;
        }

        if (!invoice) {
            throw new NotFoundException("Invoice not found or you do not have access to it");
        }

        const purchaseItemsDetails = await Promise.all(
            invoice.purchaseItems.map(async (item: any) => {
                const purchaseData: any = await this.PurchaseModel.findOne({
                    $or: [{
                        userId: invoice.userId,

                    }, {
                        sponsorUser: invoice.userId,
                    }],
                    packageId: item?.packageId,
                    invoiceId: invoiceId,
                }).populate([
                    { path: 'packageId', select: '_id services ' },
                ]).lean();
                return {
                    _id: item?._id,
                    packageId: item?.packageId,
                    classType: purchaseData?.packageId?.services?.type || "",
                    isBundledPricing: item?.isBundledPricing,
                    sessionType: purchaseData?.sessionType,
                    name: item?.packageName,
                    price: item?.unitPrice,
                    tax: item?.tax,
                    hsnOrSacCode: item?.hsnOrSacCode || "",
                    expiredInDays: item?.expireIn,
                    durationUnit: item?.durationUnit,
                    discountType: item?.discountType || "",
                    discountValue: item?.discountExcludeCart || 0,
                    quantity: item?.quantity,
                    startDate: item?.startDate,
                    endDate: item?.endDate,
                };
            }));

        const customPackageItemDetails = invoice.customPackageItems.map((item: any) => {
            return {
                _id: item?._id,
                customPackageId: item?.customPackageId,
                name: item?.packageName,
                price: item?.unitPrice,
                tax: item?.tax,
                hsnOrSacCode: item?.hsnOrSacCode || "",
                discountType: item?.discountType || "",
                discountValue: item?.discountExcludeCart || 0,
                quantity: item?.quantity,
            };
        });

        const productItemDetails = invoice.productItem.map((item: any) => ({
            _id: item?._id,
            productId: item?.productId,
            productVariantId: item?.productVariantId || null,
            name: item?.productName || "",
            quantity: item?.quantity || 1,
            price: item?.salePrice || 0,
            mrp: item?.mrp || 0,
            discountType: item?.discountType || "percentage",
            discountValue: item?.discountExcludeCart || 0,
            discountIncludeCart: item?.discountIncludeCart || 0,
            hsnOrSacCode: item?.hsnOrSacCode || "",
            tax: item?.tax || 0,
            gstAmount: item?.gstAmount || 0,
        }));

        const clientBillingUTCode = invoice.clientBillingDetails.utCode;
        const billingUTCode = invoice.billingDetails.utCode;
        const totalGSTValue = invoice.totalGstValue;

        if (clientBillingUTCode === billingUTCode) {
            invoice.cgst = totalGSTValue / 2;
            invoice.sgst = totalGSTValue / 2;
            invoice.igst = 0;
        } else {
            invoice.igst = totalGSTValue;
            invoice.cgst = 0;
            invoice.sgst = 0;
        }
        return {
            _id: invoice._id,
            createdBy: invoice.createdBy ? invoice.createdBy._id : null,
            createdByName: invoice.createdBy ? invoice.createdBy.name : null,
            invoiceDate: invoice.invoiceDate,
            invoiceNumber: invoice.invoiceNumber,
            userId: invoice.userId,
            orderId: invoice?.orderId || "",
            organizationId: invoice.organizationId,
            facilityId: invoice?.facilityId,
            facilityName: invoice?.billingDetails?.facilityName,
            billingDetails: invoice?.billingDetails,
            clientDetails: invoice?.clientDetails,
            clientBillingDetails: invoice?.clientBillingDetails,
            totalItems: purchaseItemsDetails.length + productItemDetails.length + customPackageItemDetails.length,
            purchaseItems: purchaseItemsDetails,
            customPackageItems: customPackageItemDetails,
            productItem: productItemDetails,
            subTotal: invoice?.subTotal,
            discount: invoice?.discount || 0,
            cartDiscount: invoice?.cartDiscount || 0,
            cartDiscountType: invoice?.cartDiscountType || "",
            cartDiscountAmount: invoice?.cartDiscountAmount || 0,
            totalGstValue: invoice?.totalGstValue || 0,
            totalAmountAfterGst: invoice?.totalAmountAfterGst || 0,
            roundOff: invoice?.roundOff || 0,
            grandTotal: invoice?.grandTotal || 0,
            amountInWords: invoice?.amountInWords ? invoice?.amountInWords : "",
            paymentStatus: invoice.paymentStatus,
            paymentDetails: invoice.paymentDetails,
            invoiceStatus: invoice.invoiceStatus,
            refundStatus: invoice.refundStatus,
            refundAmount: invoice.refundAmount,
            platform: invoice.platform,
            cgst: invoice.cgst,
            sgst: invoice.sgst,
            igst: invoice.igst,
            updatedPaymentDetails: invoice.updatedPaymentDetails,
            paymentReason: invoice.paymentReason,
            cancelledBy: invoice?.cancelledBy?.name
                ? invoice.cancelledBy?.name
                : invoice.cancelledBy?.firstName
                    ? invoice.cancelledBy?.firstName + " " + invoice.cancelledBy?.lastName
                    : null,
        };
    }

    async getDownloadInvoice(user: IUserDocument, invoiceId: string) {
        const organizationId = await this.getOrganizationId(user);

        const invoice: any = await this.InvoiceModel.findOne({
            _id: invoiceId,
            organizationId: organizationId,
        });

        if (!invoice) {
            throw new NotFoundException("Invoice not found or you do not have access to it");
        }

        // if (invoice.paymentStatus !== PaymentStatus.COMPLETED) {
        //     throw new BadRequestException("Invoice is not paid");
        // }

        if (!invoice.invoiceFilePath) {
            throw new BadRequestException("Invoice file not found");
        }
        return invoice.invoiceFilePath;
    }
    catch(error) {
        throw error.message;
    }

    async changePaymentStatus(organizationId: IDatabaseObjectId, body: UpdatePaymentStatusDto) {
        // const organizationId = await this.getOrganizationId(user);
        if (!organizationId) {
            throw new HttpException("Access denied", HttpStatus.FORBIDDEN);
        }
        const invoiceStatus = body.status == PaymentStatus.COMPLETED ? InvoiceStatus.COMPLETED : InvoiceStatus.PENDING;
        let paymentDetailsData = undefined;
        if (body.status == PaymentStatus.COMPLETED) {
            paymentDetailsData = body.paymentDetails?.map((detail) => ({
                paymentMethod: detail.paymentMethod,
                paymentMethodId: detail.paymentMethodId,
                transactionId: detail.transactionId || "",
                amount: detail.amount,
                paymentDate: detail.paymentDate,
                paymentStatus: detail.paymentStatus,
                paymentGateway: detail.paymentGateway || "",
                description: detail.description,
                denominations: detail.paymentMethod === PaymentMethod.CASH ? detail.denominations || {} : {},
            }));
        }
        const invoice = await this.InvoiceModel.findOne({ _id: body.invoiceId, organizationId: organizationId });
        if (!invoice) {
            throw new NotFoundException("Order not found");
        }
        if (invoice.paymentStatus === PaymentStatus.COMPLETED) {
            throw new BadRequestException("Order already paid");
        }
        invoice.paymentStatus = body.status;
        invoice.isSplittedPayment = body.isSplittedPayment;
        invoice.amountPaid = body.amountPaid;
        invoice.invoiceStatus = invoiceStatus;
        invoice.paymentDetails = paymentDetailsData;
        await invoice.save();
        if (invoice.paymentStatus !== PaymentStatus.PENDING) {
            const organization = await this.UserModel.findById(organizationId).select("email");
            const organizationEmail = organization?.email || "";
            await this.invoiceService.generateInvoice(invoice, organizationEmail);
        }
        return this.getInvoiceDetails(organizationId, body.invoiceId);
    }

    async cancelOrder(organizationId: IDatabaseObjectId, user: IUserDocument, body: CancelOrder) {
        // const organizationId = await this.getOrganizationId(user);
        if (!organizationId) {
            throw new HttpException("Access denied", HttpStatus.FORBIDDEN);
        }
        const invoiceDetails = await this.InvoiceModel.findOne({ _id: body.invoiceId, organizationId: organizationId });
        const packageDetails = invoiceDetails.purchaseItems;
        let totalSession = 0;
        let packageName = "";
        if (packageDetails.length > 0 && body?.paymentStatus === PaymentStatus.CANCELED) {
            for (let index = 0; index < packageDetails.length; index++) {
                const element: any = packageDetails[index];
                const purchaseDetail = await this.PurchaseModel.findOne({
                    $and: [
                        {
                            $or: [
                                { packageId: element.packageId, },
                                { bundledPricingId: element.packageId, }
                            ]
                        },
                        {
                            $or: [
                                { userId: invoiceDetails.userId, },
                                { sponsorUser: invoiceDetails.userId, },
                            ]
                        },
                    ],
                    invoiceId: body.invoiceId
                });
                const schedulingDetails = await this.SchedulingModel.find({ purchaseId: purchaseDetail._id, scheduleStatus: { $ne: ScheduleStatusType.CANCELED } });
                for (let i = 0; i < schedulingDetails.length; i++) {
                    const schedule = schedulingDetails[i];
                    totalSession = totalSession + schedule.sessions;
                }
                packageName = element.packageName;
            }
        }
        if (totalSession > 0) {
            throw new BadRequestException(`Order cannot be canceled as there are ${totalSession} scheduled sessions with the ${packageName} `);
        }

        const invoice = await this.InvoiceModel.findOneAndUpdate(
            { _id: body.invoiceId, organizationId: organizationId },
            {
                $set: {
                    paymentStatus: body?.paymentStatus,
                    paymentReason: body?.reason,
                    cancelledBy: user._id,
                },
            },
        );
        if (!invoice) {
            throw new NotFoundException("Order not found");
        }

        await this.PurchaseModel.updateMany(
            { invoiceId: invoice._id },
            {
                $set: {
                    isActive: false,
                    isExpired: true,
                    paymentStatus: body?.paymentStatus,
                },
            },
        );

        return this.getInvoiceDetails(organizationId, body.invoiceId);
    }

    async getServiceByPurchaseId(purchaseId: string) {
        return null;
    }

    async suspendMembership(user: IUserDocument, body: SuspendMembershipDto) {
        const { purchaseId, fromDate, endDate, notes } = body;

        // Find active purchase
        const purchase = await this.PurchaseModel.findOne({
            _id: new Types.ObjectId(purchaseId),
            $expr: {
                $and: [
                    {
                        $lte: [{ $dateToString: { format: "%Y-%m-%d", date: "$startDate" } }, fromDate.toISOString().split("T")[0]],
                    },
                    {
                        $gte: [{ $dateToString: { format: "%Y-%m-%d", date: "$endDate" } }, endDate.toISOString().split("T")[0]],
                    },
                ],
            },
        });

        if (!purchase) {
            throw new NotFoundException("No active membership found for the given dates");
        }

        // Check for upcoming suspensions
        const now = new Date();
        const upcomingSuspensions = purchase.suspensions?.filter((suspension: Suspensions) => suspension.endDate > now && !suspension.isResumed);

        if (upcomingSuspensions?.length > 0) {
            // Check if any upcoming suspension overlaps with requested dates
            if (upcomingSuspensions.some((suspension: Suspensions) => suspension.fromDate <= endDate && suspension.endDate >= fromDate)) {
                const suspendedDates = upcomingSuspensions.map(
                    (suspension) => `${moment(suspension.fromDate).format("DD/MM/YYYY")} to ${moment(suspension.endDate).format("DD/MM/YYYY")}`,
                );
                throw new BadRequestException(`Membership is already suspended for dates: ${suspendedDates.join(", ")}`);
            }

            // Only allow one upcoming suspension
            throw new BadRequestException("Only one upcoming suspension is allowed at a time");
        }
        // Validate pricing
        // const pricing = await this.PricingModel.findOne({ _id: purchase.packageId });
        // if (!pricing?.membershipId) {
        //     throw new NotFoundException("This package is not part of any membership");
        // }

        // Validate dates
        const timeDifference = endDate.getTime() - fromDate.getTime();
        if (timeDifference < 0) {
            throw new BadRequestException("From date must be less than end date");
        }

        // Check if the requested dates are already scheduled
        const scheduledSessions = await this.SchedulingModel.findOne({
            clientId: purchase.userId,
            purchaseId: new mongoose.Types.ObjectId(purchaseId),
            scheduleStatus: { $ne: ScheduleStatusType.CANCELED },
            date: { $gte: fromDate, $lte: endDate },
        });
        if (scheduledSessions) {
            throw new BadRequestException(`Selected dates already have a scheduled session on ${moment(scheduledSessions.date).format("DD/MM/YYYY")}`);
        }

        // Calculate new expiry date
        const updatedExpiryDate = new Date(purchase.endDate.getTime() + timeDifference);

        // Update purchase with new suspension
        await this.PurchaseModel.updateOne(
            { _id: purchaseId },
            {
                $set: { endDate: updatedExpiryDate },
                $push: {
                    suspensions: {
                        fromDate: fromDate,
                        endDate: endDate,
                        notes: notes,
                    },
                },
            },
        );

        return true;
    }

    async resumeMembership(user: IUserDocument, body: ResumeMembershipDto) {
        const { purchaseId, suspensionId, fromDate } = body;

        // Find purchase with specific suspension
        const purchase = await this.PurchaseModel.findOne({
            _id: purchaseId,
            suspensions: {
                $elemMatch: {
                    _id: suspensionId,
                    // isAtivated: false // Only get non-activated suspensions
                },
            },
        });

        if (!purchase) {
            throw new NotFoundException("No active suspended membership found");
        }

        const suspension = purchase.suspensions.find((s: any) => s._id.toString() === suspensionId);
        if (!suspension) {
            throw new NotFoundException("Suspension period not found");
        }

        // Validate suspension dates
        if (fromDate > suspension.endDate) {
            throw new BadRequestException("Suspension period has already ended");
        }

        if (fromDate < suspension.fromDate) {
            throw new BadRequestException("From date must be greater than suspension start date");
        }

        // Calculate actual suspension duration until current date
        const suspensionDuration = suspension.endDate.getTime() - suspension.fromDate.getTime();
        const actualSuspensionDuration = fromDate.getTime() - suspension.fromDate.getTime();
        const remainingDuration = suspension.endDate.getTime() - fromDate.getTime();
        const totalDuration = actualSuspensionDuration <= 0 ? suspensionDuration : remainingDuration;
        const timeStamp = purchase.endDate.getTime() - totalDuration;
        const updatedExpiryDate = new Date(timeStamp);

        // check if the new expiry date is covering all the scheduled sessions else send error
        const scheduledSessions = await this.SchedulingModel.findOne({
            userId: purchase.userId,
            purchaseId: purchaseId,
            scheduleStatus: { $ne: ScheduleStatusType.CANCELED },
            date: { $gt: updatedExpiryDate, $lte: purchase.endDate },
        });

        if (scheduledSessions) {
            throw new BadRequestException(`New expiry date is not covering all the scheduled sessions of ${moment(scheduledSessions.date).format("DD/MM/YYYY")} date`);
        }

        const updatePayload: UpdateQuery<PurchaseDocument> = {
            $set: {
                endDate: updatedExpiryDate,
            },
        };

        if (suspension.fromDate >= fromDate) {
            updatePayload.$pull = { suspensions: { _id: suspensionId } };
        } else {
            updatePayload.$set["suspensions.$.isResumed"] = true;
            updatePayload.$set["suspensions.$.endDate"] = fromDate;
        }

        // Update purchase
        await this.PurchaseModel.updateOne({ _id: purchaseId, "suspensions._id": suspensionId }, updatePayload);

        return true;
    }

    /**
     * @deprecated This method is deprecated and will be removed in future versions.
     * Please use alternative methods for managing membership suspensions.
     */
    async updateSuspension(user: IUserDocument, body: UpdateSuspensionDto) {
        const { purchaseId, suspensionId, fromDate, endDate, notes } = body;

        // Find purchase with specific suspension
        const purchase = await this.PurchaseModel.findOne({
            _id: purchaseId,
            suspensions: {
                $elemMatch: {
                    _id: suspensionId,
                },
            },
        });

        if (!purchase) {
            throw new NotFoundException("No membership or suspension found");
        }

        const existingSuspension = purchase.suspensions.find((s) => s._id.toString() === suspensionId);
        if (!existingSuspension) {
            throw new NotFoundException("Suspension period not found");
        }

        // If suspension is already activated, don't allow updates
        if (existingSuspension.isResumed) {
            throw new BadRequestException("Cannot update an re-activated suspension");
        }

        // Check if suspension period has already started
        const currentDate = new Date();
        if (currentDate > existingSuspension.endDate) {
            throw new BadRequestException("Cannot update suspension after end date has passed");
        }

        // Validate new dates
        if (fromDate.getTime() >= endDate.getTime()) {
            throw new BadRequestException("From date must be less than end date");
        }

        // Check if the new dates overlap with other suspensions
        const hasOverlap = purchase.suspensions.some(
            (suspension) =>
                suspension._id.toString() !== suspensionId && // Skip current suspension
                suspension.fromDate <= endDate &&
                suspension.endDate >= fromDate,
        );

        if (hasOverlap) {
            throw new BadRequestException("New suspension dates overlap with existing suspension periods");
        }

        // Calculate the difference in suspension duration
        const oldDuration = existingSuspension.endDate.getTime() - existingSuspension.fromDate.getTime();
        const newDuration = endDate.getTime() - fromDate.getTime();
        const durationDifference = newDuration - oldDuration;

        // Calculate new expiry date for the purchase
        const updatedExpiryDate = new Date(purchase.endDate.getTime() + durationDifference);

        // Validate that the new dates fall within the purchase period
        if (fromDate < purchase.startDate || new Date() > fromDate) {
            throw new BadRequestException("Suspension dates must fall within the membership period");
        }

        // Update the suspension
        await this.PurchaseModel.updateOne(
            {
                _id: purchaseId,
                "suspensions._id": suspensionId,
            },
            {
                $set: {
                    endDate: updatedExpiryDate,
                    "suspensions.$.fromDate": fromDate,
                    "suspensions.$.endDate": endDate,
                    "suspensions.$.notes": notes,
                },
            },
        );

        return true;
    }

    private async getOrganizationandFacility(user: IUserDocument) {
        const { role } = user;

        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                return { organizationId: user._id };

            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.TRAINER:
                const staffDetails = await this.StaffDetailsModel.findOne({ userId: user._id }, { organizationId: 1, facilityId: 1 });

                if (!staffDetails) {
                    throw new Error("Staff details not found for the user.");
                }

                if (!staffDetails.facilityId) {
                    throw new Error("Facility not assigned to this staff user.");
                }

                return {
                    organizationId: staffDetails.organizationId,
                    facilityId: staffDetails.facilityId,
                };

            default:
                throw new Error("Unauthorized role or insufficient permissions.");
        }
    }

    async zOutReportByEmp(user: any, body: ExportZOutReportDto, userTimezone: string) {
        const facilityDetails = await this.getOrganizationandFacility(user);
        const { facilityIds, responseType, zOutId } = body;
        let data = {};
        const objectId = new Types.ObjectId(zOutId);
        const facilityName = await this.FacilityModel.findOne({ _id: { $in: facilityIds.map(_id => new Types.ObjectId(_id)) } }, { facilityName: 1, paymentMethods: 1 })
        const activePaymentMethods = facilityName?.paymentMethods?.filter((method: any) => method.isActive === true) || [];
        const latestReconciliation = await this.ReconciliationModel.findOne({
            _id: objectId,
            facilityId: { $in: facilityIds.map((_id) => new Types.ObjectId(_id)) },
            organizationId: new Types.ObjectId(facilityDetails.organizationId),
        });

        if (!latestReconciliation) {
            throw new Error("Reconciliation document not found.");
        }

        const secondLastReconciliation = await this.ReconciliationModel.findOne({
            facilityId: latestReconciliation.facilityId,
            organizationId: new Types.ObjectId(facilityDetails.organizationId),
            createdAt: { $lt: latestReconciliation.createdAt },
        }).sort({ createdAt: -1 });

        let startDate: Date = new Date();

        if (latestReconciliation) {
            startDate = latestReconciliation?.createdAt;
        }
        if (secondLastReconciliation) {
            startDate = secondLastReconciliation?.createdAt;
        }

        const currentDate = new Date();
        const dayOfWeek = currentDate.toLocaleString("en-US", { weekday: "short" }).toLowerCase();
        const startISTTime = moment(startDate).tz(userTimezone).format("HH:mm");
        const currentISTTime = moment(currentDate).tz(userTimezone).format("HH:mm");

        (data["day"] = dayOfWeek), (data["startDate"] = `${moment(startDate).tz(userTimezone).format("DD/MM/YYYY")}`);
        data["currentDate"] = `${moment(currentDate).tz(userTimezone).format("DD/MM/YYYY")}`;
        (data["role"] = user.role ? user.role.type : ""),
            (data["name"] = user?.name);
        data["firstName"] = user?.firstName;
        data["lastName"] = user?.lastName;
        data["startTime"] = startISTTime;
        data["currentTime"] = currentISTTime;
        data["facilityName"] = facilityName?.facilityName;

        const filter: any = {
            organizationId: new Types.ObjectId(facilityDetails.organizationId),
            paymentStatus: PaymentStatus.COMPLETED,
            updatedAt: { $gte: secondLastReconciliation?.createdAt || Date.now() }
        };
        if (Array.isArray(facilityIds) && facilityIds.length > 0) {
            filter["facilityId"] = { $in: facilityIds.map((_id) => new Types.ObjectId(_id)) };
        }

        let pipeline: any = [
            { $match: filter },
            {
                $addFields: {
                    purchaseItemsSafe: { $ifNull: ["$purchaseItems", []] },
                    productItemSafe: { $ifNull: ["$productItem", []] },
                },
            },
            {
                $addFields: {
                    serviceItems: {
                        $map: {
                            input: "$purchaseItemsSafe",
                            as: "item",
                            in: { $mergeObjects: ["$$item", { itemType: "service" }] },
                        },
                    },
                    productItems: {
                        $map: {
                            input: "$productItemSafe",
                            as: "item",
                            in: { $mergeObjects: ["$$item", { itemType: "product" }] },
                        },
                    },
                    completedPayments: {
                        $let: {
                            vars: {
                                paymentsArray: {
                                    $cond: [{ $isArray: "$paymentDetails" }, "$paymentDetails", { $ifNull: [["$paymentDetails"], []] }],
                                },
                            },
                            in: {
                                $filter: {
                                    input: "$$paymentsArray",
                                    as: "payment",
                                    cond: { $eq: ["$$payment.paymentStatus", PaymentStatus.COMPLETED] },
                                },
                            },
                        },
                    },
                },
            },
            {
                $facet: {
                    payments: [
                        { $unwind: "$completedPayments" },
                        {
                            $group: {
                                _id: {
                                    facilityId: "$facilityId",
                                    paymentMethod: "$completedPayments.paymentMethod",
                                },
                                totalPaid: { $sum: "$completedPayments.amount" },
                                totalSales: { $sum: "$grandTotal" },
                            },
                        },
                        {
                            $group: {
                                _id: "$_id.facilityId",
                                totalSales: { $sum: "$totalSales" },
                                paymentMethodSummary: {
                                    $push: {
                                        paymentMethod: "$_id.paymentMethod",
                                        totalAmount: "$totalPaid",
                                    },
                                },
                            },
                        },
                    ],
                    invoices: [
                        {
                            $project: {
                                facilityId: 1,
                                allItems: { $concatArrays: ["$serviceItems", "$productItems"] },
                            },
                        },
                        {
                            $group: {
                                _id: "$facilityId",
                                invoices: { $push: "$allItems" },
                            },
                        },
                        {
                            $addFields: {
                                flattenedItems: {
                                    $reduce: {
                                        input: "$invoices",
                                        initialValue: [],
                                        in: { $concatArrays: ["$$value", "$$this"] },
                                    },
                                },
                            },
                        },
                    ],
                },
            },
            {
                $project: {
                    data: {
                        $map: {
                            input: "$payments",
                            as: "payment",
                            in: {
                                _id: "$$payment._id",
                                totalSales: "$$payment.totalSales",
                                paymentMethodSummary: "$$payment.paymentMethodSummary",
                                invoices: {
                                    $let: {
                                        vars: {
                                            inv: {
                                                $arrayElemAt: [
                                                    {
                                                        $filter: {
                                                            input: "$invoices",
                                                            as: "i",
                                                            cond: { $eq: ["$$i._id", "$$payment._id"] },
                                                        },
                                                    },
                                                    0,
                                                ],
                                            },
                                        },
                                        in: "$$inv.flattenedItems",
                                    },
                                },
                            },
                        },
                    },
                },
            },
            { $unwind: "$data" },
            {
                $replaceRoot: {
                    newRoot: "$data",
                },
            },
            {
                $lookup: {
                    from: "facilities",
                    localField: "_id",
                    foreignField: "_id",
                    as: "facility",
                },
            },
            {
                $project: {
                    _id: 1,
                    facilityName: { $arrayElemAt: ["$facility.facilityName", 0] },
                    totalSales: 1,
                    invoiceCount: { $size: "$invoices" },
                    invoices: 1,
                    paymentMethodSummary: 1,
                },
            },
        ];
        const salesData = await this.InvoiceModel.aggregate(pipeline).exec();
        if (responseType === 'stream') {
            const workbook = salesData.length
                ? await this.generateExcelReport(salesData, latestReconciliation, data, activePaymentMethods)
                : await this.generateEmptyExcel(data, latestReconciliation, activePaymentMethods);

            const excelBuffer = write(workbook, { type: "buffer", bookType: "xlsx" });

            return new StreamableFile(excelBuffer, {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                disposition: `attachment; filename=Z-Out_Report_${moment().format("YYYY-MM-DD")}.xlsx`,
            });
        }
        if (responseType === "pdf") {
            const pdfBuffer = salesData.length
                ? await this.generateZOutReportPDF(data, salesData[0], latestReconciliation, activePaymentMethods)
                : await this.generateZOutReportPDF(data, [], latestReconciliation, activePaymentMethods); // empty case also handled

            return new StreamableFile(new Uint8Array(pdfBuffer), {
                type: "application/pdf",
                disposition: `attachment; filename=Z-Out_Report_${moment().format("YYYY-MM-DD")}.pdf`,
            });
        }
        return this.formatReportData(salesData);
    }

    private async generateExcelReport(facilities, latestReconciliation, data, activePaymentMethods) {
        const workbook = utils.book_new();

        const paymentMethodDocs = await this.paymentMethodModel.find({}).sort({ name: 1 });
        const shortIdToName = new Map(paymentMethodDocs.map((pm: any) => [pm.shortId, pm.name]));

        facilities?.forEach((facility) => {
            const paymentSummary = facility?.paymentMethodSummary || [];

            let cashSales = 0;
            let otherPaymentsTotalAmount = 0;
            let otherPaymentsTotalCollected = 0;
            let otherPaymentsTotalOverUnder = 0;

            const otherPayments = [["--- Other Payments ---"], ["Label", "Amount (INR)", "Collected", "Over/Under"]];

            const paymentRows = paymentSummary.map((payment) => {
                const readableName = shortIdToName.get(payment?.paymentMethod) || payment?.paymentMethod;
                const method = payment?.paymentMethod;
                const reconciliation = latestReconciliation?.otherPayments.find((e) => e?.method === method) || {};

                if (payment?.paymentMethod === PaymentMethod.CASH) {
                    cashSales += payment?.totalAmount || 0;
                } else {
                    const amount = Number(payment?.totalAmount || 0);
                    const collected = Number(reconciliation?.collected || 0);
                    const overUnder = Number(reconciliation?.overUnder || 0);
                    otherPaymentsTotalAmount += amount;
                    otherPaymentsTotalCollected += collected;
                    otherPaymentsTotalOverUnder += overUnder;

                    otherPayments.push([
                        readableName,
                        `₹ ${amount.toFixed(2)}`,
                        `₹ ${collected.toFixed(2)}`,
                        `₹ ${overUnder.toFixed(2)}`
                    ]);
                }

                return [
                    'POS',
                    readableName,
                    `₹ ${(payment?.totalAmount || 0).toFixed(2)}`
                ];
            });
            const existingLabels = new Set(otherPayments.slice(2).map(row => row[0]));
            const ignoredShortIds = ['cash', 'splitPayment'];
            activePaymentMethods?.forEach((method) => {
                const methodName = method.name;
                const methodShortId = method.shortId;

                if (!ignoredShortIds.includes(methodShortId) && !existingLabels.has(methodName)) {
                    otherPayments.push([
                        methodName,
                        '₹ 0.00',
                        '₹ 0.00',
                        '₹ 0.00'
                    ]);
                }
            });
            otherPayments.push(
                ["", "- - - - - - - - -", "- - - - - - - - -", "- - - - - - - - -"],
                [
                    "Total",
                    `₹ ${(otherPaymentsTotalAmount || 0).toFixed(2)}`,
                    `₹ ${(otherPaymentsTotalCollected || 0).toFixed(2)}`,
                    `₹ ${(latestReconciliation?.onlineOverUnder || 0).toFixed(2)}`,
                ],
                [],
                [],
            );


            let totalAmount = 0;
            const invoiceRows = (facility?.invoices || []).map((invoice) => {
                const itemName = invoice?.itemType === "service" ? invoice?.packageName : invoice?.productName;
                const quantity = Number(invoice?.quantity || 0);
                const unitPrice = invoice?.itemType === "service" ? Number(invoice?.unitPrice || 0) : Number(invoice?.salePrice || 0);
                const subTotal = unitPrice * quantity;
                const discountExcludeCart = Number(invoice?.discountExcludeCart || 0);
                const discountIncludeCart = Number(invoice?.discountIncludeCart || 0);
                const totalDiscount = discountExcludeCart + discountIncludeCart;
                const amountBeforeGst = subTotal - totalDiscount;
                const gstAmount = Number(invoice?.gstAmount || 0);
                const itemTotal = subTotal + gstAmount - totalDiscount;
                totalAmount += itemTotal;

                return [
                    itemName,
                    quantity,
                    `₹ ${unitPrice.toFixed(2)}`,
                    `₹ ${totalDiscount.toFixed(2)}`,
                    `₹ ${amountBeforeGst.toFixed(2)}`,
                    `₹ ${gstAmount.toFixed(2)}`,
                    `₹ ${itemTotal.toFixed(2)}`,
                ];
            });

            const totalPayments = paymentSummary.reduce((sum, p) => sum + (p?.totalAmount || 0), 0);

            const totals = [
                ["--- Totals ---"],
                ["Label", "Amount (INR)"],
                ["All Over/Under (Cash+Other)", `₹ ${((latestReconciliation?.overUnder || 0) + (latestReconciliation?.onlineOverUnder || 0)).toFixed(2)}`],
                ["All Payments", `₹ ${(totalPayments || 0).toFixed(2)}`],
                [],
                [],
            ];
            let totalCash = latestReconciliation?.startingAmount + cashSales - latestReconciliation?.pettyAmount;
            const cashSection = [
                ["--- Cash Payments ---"],
                ["Label", "Amount (INR)"],
                ["Starting Cash", `₹ ${(latestReconciliation?.startingAmount || 0).toFixed(2)}`],
                ["Cash Sales", `₹ ${cashSales.toFixed(2)}`],
                ["Petty Cash Out(-)", `₹ ${(latestReconciliation?.pettyAmount || 0).toFixed(2)}`],
                ["", "- - - - - - - - -"],
                ["Total", `₹ ${(totalCash || 0).toFixed(2)}`],
                [],
                ["Drawer Cash", `₹ ${(latestReconciliation?.drawerAmount || 0).toFixed(2)}`],
                ["Leave Cash", `₹ ${(latestReconciliation?.leaveAmount || 0).toFixed(2)}`],
                ["Cash Over/Under", `₹ ${(latestReconciliation?.overUnder || 0).toFixed(2)}`],
                ["", `- - - - - - - - -`],
                ["Cash Deposit Amount", `₹ ${(latestReconciliation?.depositAmount || 0).toFixed(2)}`],
                [],
                [],
            ];

            const combinedData = [
                ["Z-Out Summary Report -", data?.facilityName || "Unnamed Facility"],
                [`Report Generated By - ${data?.name}(${data.role}) on ${data?.day?.toUpperCase()}, ${data?.currentDate}`],
                ["From", `${data?.startDate} @ ${data?.startTime}`],
                ["To", `${data?.currentDate} @ ${data?.currentTime}`],
                [],
                [],
                ...cashSection,
                ...otherPayments,
                ...totals,
                ["--- All Payments ---"],
                ["Source", "Method", "Sales"],
                ...paymentRows,
                ["", "", "- - - - - - - - -"],
                ["Total", "", `₹ ${totalPayments.toFixed(2)}`],
                [],
                [],
                ["--- Accounts ---"],
                ["Item", "Qty", "Unit Price", "Discount", "Amount Before GST", "GST", "Total"],
                ...invoiceRows,
                ["", "", "", "", "", "", "- - - - - - - - -"],
                ["Grand Total", "", "", "", "", "", `₹ ${totalAmount.toFixed(2)}`],
                [],
                [],
                [`Report Prepared By ${data.name} on ${data?.day?.toUpperCase()}, ${data?.currentDate} ,${data?.currentTime}`],
            ];

            const sheet = utils.aoa_to_sheet(combinedData);
            utils.book_append_sheet(workbook, sheet, data?.facilityName?.substring(0, 30) || "Report");
        });

        return workbook;
    }

    private async generateEmptyExcel(data, latestReconciliation, activePaymentMethods) {
        const workbook = utils.book_new();

        const paymentMethodDocs = await this.paymentMethodModel.find({}).sort({ name: 1 });
        const shortIdToName = new Map(paymentMethodDocs.map((pm: any) => [pm.shortId, pm.name]));

        let cashSales = 0;
        let otherPaymentsTotalAmount = 0;
        let otherPaymentsTotalCollected = 0;
        let otherPaymentsTotalOverUnder = 0;

        const otherPayments = [["--- Other Payments ---"], ["Label", "Amount (INR)", "Collected", "Over/Under"]];

        (latestReconciliation?.otherPayments || [])?.forEach((payment) => {
            const label = shortIdToName?.get(payment?.method) || payment?.method;
            const amount = Number(payment?.amount || 0);
            const collected = Number(payment?.collected || 0);
            const overUnder = Number(payment?.overUnder || 0);

            otherPaymentsTotalAmount += amount;
            otherPaymentsTotalCollected += collected;
            otherPaymentsTotalOverUnder += overUnder;

            otherPayments.push([label, `₹ ${amount.toFixed(2)}`, `₹ ${collected.toFixed(2)}`, `₹ ${overUnder.toFixed(2)}`]);
        });
        const existingLabels = new Set(otherPayments.slice(2).map(row => row[0]));
        const ignoredShortIds = ['cash', 'splitPayment'];
        activePaymentMethods?.forEach((method) => {
            const methodName = method.name;
            const methodShortId = method.shortId;

            if (!ignoredShortIds.includes(methodShortId) && !existingLabels.has(methodName)) {
                otherPayments.push([
                    methodName,
                    '₹ 0.00',
                    '₹ 0.00',
                    '₹ 0.00'
                ]);
            }
        });
        otherPayments.push(
            ["", "- - - - - - - - -", "- - - - - - - - -", "- - - - - - - - -"],
            ["Total", `₹ ${otherPaymentsTotalAmount?.toFixed(2)}`, `₹ ${otherPaymentsTotalCollected?.toFixed(2)}`, `₹ ${otherPaymentsTotalOverUnder?.toFixed(2)}`],
            [],
            [],
        );

        let totalCash = latestReconciliation?.startingAmount + cashSales - latestReconciliation?.pettyAmount;

        const cashSection = [
            ["--- Cash Payments ---"],
            ["Label", "Amount (INR)"],
            ["Starting Cash", `₹ ${(latestReconciliation?.startingAmount || 0).toFixed(2)}`],
            ["Cash Sales", `₹ ${cashSales.toFixed(2)}`],
            ["Petty Cash Out(-)", `₹ ${(latestReconciliation?.pettyAmount || 0).toFixed(2)}`],
            ["", "- - - - - - - - -"],
            ["Total", `₹ ${(totalCash || 0).toFixed(2)}`],
            [],
            ["Drawer Cash", `₹ ${(latestReconciliation?.drawerAmount || 0).toFixed(2)}`],
            ["Leave Cash", `₹ ${(latestReconciliation?.leaveAmount || 0).toFixed(2)}`],
            ["Cash Over/Under", `₹ ${(latestReconciliation?.overUnder || 0).toFixed(2)}`],
            ["", `- - - - - - - - -`],
            ["Cash Deposit Amount", `₹ ${(latestReconciliation?.depositAmount || 0).toFixed(2)}`],
            [],
            [],
        ];

        const totals = [
            ["--- Totals ---"],
            ["Label", "Amount (INR)"],
            ["All Over/Under (Cash+Other)", `₹ ${((latestReconciliation?.overUnder || 0) + (latestReconciliation?.onlineOverUnder || 0)).toFixed(2)}`],
            ["All Payments", `₹ ${(latestReconciliation?.totalAmount || 0).toFixed(2)}`],
            [],
            [],
        ];

        const combinedData = [
            ["Z-Out Summary Report -", data?.facilityName || "Unnamed Facility"],
            [`Report Generated By - ${data?.name} (${data.role}) on ${data?.day?.toUpperCase()}, ${data?.currentDate}`],
            ["From", `${data?.startDate} @ ${data?.startTime}`],
            ["To", `${data?.currentDate} @ ${data?.currentTime}`],
            [],
            [],
            ...cashSection,
            ...otherPayments,
            ...totals,
            [`Report Prepared By ${data.name} on ${data?.day?.toUpperCase()}, ${data?.currentDate}, ${data?.currentTime}`],
        ];

        const sheet = utils.aoa_to_sheet(combinedData);
        utils.book_append_sheet(workbook, sheet, data?.facilityName?.substring(0, 30) || "Report");

        return workbook;
    }

    private formatReportData(facilities) {
        return {
            message: "Z-Out Report fetched successfully",
            data: facilities.map((facility) => ({
                facilityId: facility._id,
                facilityName: facility.facilityName,
                totalSales: facility.totalSales,
                paymentMethods: facility.paymentMethodSummary,
                items: facility.invoices,
            })),
        };
    }

    async scheduleAtGlance(user: IUserDocument, body: ExportScheduleReportDto, userTimezone: string) {
        const facilityDetails = await this.getOrganizationandFacility(user);
        const { facilityIds, startDate, endDate, staffIds, classType, scheduleStatus } = body;

        const filter: any = {
            organizationId: new Types.ObjectId(facilityDetails.organizationId),
            // serviceCategoryId: { $in: serviceCategoryIds.map(id => new Types.ObjectId(id)) },
        };

        if (Array.isArray(facilityIds) && facilityIds.length > 0) {
            filter["facilityId"] = { $in: facilityIds.map((_id) => new Types.ObjectId(_id)) };
        } else if (user.role.type !== ENUM_ROLE_TYPE.ORGANIZATION && facilityDetails.facilityId) {
            const facilityArray = Array.isArray(facilityDetails.facilityId) ? facilityDetails.facilityId : [facilityDetails.facilityId];

            filter["facilityId"] = {
                $in: facilityArray.map((id) => new Types.ObjectId(id)),
            };
        }
        if (scheduleStatus && scheduleStatus.length > 0) {
            filter['scheduleStatus'] = { $in: scheduleStatus };
        }
        if (startDate && endDate) {
            const startOfDay = new Date(new Date(startDate).setHours(0, 0, 0, 0));
            const endOfDay = new Date(new Date(endDate).setHours(23, 59, 59, 999));
            filter["date"] = {
                $gte: startOfDay,
                $lte: endOfDay,
            };
        }

        const hasBooking = classType.includes(ClassType.BOOKINGS);
        const hasNonBooking = classType.filter((type) => type !== ClassType.BOOKINGS);
        const orConditions: any[] = [];

        if (hasBooking && hasNonBooking.length > 0) {
            // If staffIds exist, filter by them
            if (staffIds && staffIds.length > 0) {
                orConditions.push({
                    classType: { $in: hasNonBooking },
                    trainerId: { $in: staffIds.map((id) => new Types.ObjectId(id)) },
                });
            } else {
                orConditions.push({
                    classType: { $in: hasNonBooking },
                });
            }

            orConditions.push({
                classType: ClassType.BOOKINGS,
            });
        } else if (hasBooking && hasNonBooking.length === 0) {
            orConditions.push({
                classType: ClassType.BOOKINGS,
            });
        } else if (!hasBooking && hasNonBooking.length > 0) {
            if (staffIds && staffIds.length > 0) {
                orConditions.push({
                    classType: { $in: hasNonBooking },
                    trainerId: { $in: staffIds.map((id) => new Types.ObjectId(id)) },
                });
            } else {
                orConditions.push({
                    classType: { $in: hasNonBooking },
                });
            }
        }

        if (orConditions.length > 0) {
            filter["$or"] = orConditions;
        }

        let schedulingDetails: any = await this.SchedulingModel.aggregate([
            { $match: filter },
            {
                $lookup: {
                    from: "schedulings",
                    let: { clientId: "$clientId", currentId: "$_id" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [{ $eq: ["$clientId", "$$clientId"] }, { $lt: ["$_id", "$$currentId"] }],
                                },
                            },
                        },
                        { $limit: 1 },
                    ],
                    as: "visitHistory",
                },
            },
            {
                $addFields: {
                    firstVisit: { $eq: [{ $size: "$visitHistory" }, 0] },
                },
            },
            {
                $lookup: {
                    from: "services",
                    localField: "serviceCategoryId",
                    foreignField: "_id",
                    as: "services",
                },
            },
            {
                $addFields: {
                    serviceCategoryName: { $arrayElemAt: ["$services.name", 0] },
                    subtypeName: {
                        $first: {
                            $map: {
                                input: {
                                    $filter: {
                                        input: { $arrayElemAt: ["$services.appointmentType", 0] },
                                        as: "item",
                                        cond: { $eq: ["$$item._id", "$subTypeId"] },
                                    },
                                },
                                as: "match",
                                in: "$$match.name",
                            },
                        },
                    },
                },
            },
            {
                $lookup: {
                    from: "clients",
                    localField: "clientId",
                    foreignField: "userId",
                    as: "clientInfo",
                },
            },
            {
                $addFields: {
                    clientDob: { $arrayElemAt: ["$clientInfo.dob", 0] },
                    clientIdentity: { $arrayElemAt: ["$clientInfo.clientId", 0] },
                },
            },
            {
                $lookup: {
                    from: "facilities",
                    localField: "facilityId",
                    foreignField: "_id",
                    as: "facilityInfo",
                },
            },
            {
                $addFields: {
                    facilityName: { $arrayElemAt: ["$facilityInfo.facilityName", 0] },
                    cityId: { $arrayElemAt: ["$facilityInfo.address.city", 0] },
                },
            },
            {
                $lookup: {
                    from: "cities",
                    localField: "cityId",
                    foreignField: "_id",
                    as: "cityInfo",
                },
            },
            {
                $addFields: {
                    facilityLocation: { $arrayElemAt: ["$cityInfo.name", 0] },
                },
            },
            {
                $lookup: {
                    from: "users",
                    let: { clientId: "$clientId", trainerId: "$trainerId" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $or: [{ $eq: ["$_id", "$$clientId"] }, { $eq: ["$_id", "$$trainerId"] }],
                                },
                            },
                        },
                        {
                            $project: {
                                _id: 1,
                                firstName: 1,
                                lastName: 1,
                            },
                        },
                    ],
                    as: "userNames",
                },
            },
            {
                $addFields: {
                    clientName: {
                        $let: {
                            vars: {
                                user: {
                                    $first: {
                                        $filter: {
                                            input: "$userNames",
                                            as: "u",
                                            cond: { $eq: ["$$u._id", "$clientId"] },
                                        },
                                    },
                                },
                            },
                            in: { $concat: ["$$user.firstName", " ", "$$user.lastName"] },
                        },
                    },
                    trainerName: {
                        $let: {
                            vars: {
                                user: {
                                    $first: {
                                        $filter: {
                                            input: "$userNames",
                                            as: "u",
                                            cond: { $eq: ["$$u._id", "$trainerId"] },
                                        },
                                    },
                                },
                            },
                            in: { $concat: ["$$user.firstName", " ", "$$user.lastName"] },
                        },
                    },
                },
            },
            {
                $project: {
                    date: 1,
                    from: 1,
                    to: 1,
                    serviceCategoryName: 1,
                    subtypeName: 1,
                    trainerName: 1,
                    facilityName: 1,
                    facilityLocation: 1,
                    notes: 1,
                    clientName: 1,
                    clientIdentity: 1,
                    scheduleStatus: 1,
                    clientDob: 1,
                    classType: 1,
                    firstVisit: 1,
                },
            },
        ]).exec();

        return {
            data: `Date,Start Time,End Time,Service Type,Description,Staff Name,Facility Name,Facility Location,Notes,Client Name,ClientId,Status,First Visit,Birthday\n` +
                schedulingDetails?.map((item: any) => {
                    const date = item?.date ? `${moment(item?.date).tz(userTimezone).format('DD/MM/YYYY')}` : item?.date
                    const startTime = item?.from
                    const endTime = item?.to
                    const classType = item?.classType
                    const description = `${item?.serviceCategoryName + "/" + item?.subtypeName}`
                    const staffName = item?.trainerName
                    const facilityName = item?.facilityName
                    const facilityLocation = item?.facilityLocation
                    const clientName = item?.clientName
                    const clientId = item?.clientIdentity
                    const status = item?.scheduleStatus
                    const firstVisit = item?.firstVisit
                    const birthday = item?.clientDob ? `${moment(item?.clientDob).tz(userTimezone).format('DD/MM/YYYY')}` : item?.clientDob
                    const notes = item?.notes

                    const row = [
                        date,
                        startTime,
                        endTime,
                        classType,
                        description,
                        staffName,
                        facilityName,
                        facilityLocation,
                        notes,
                        clientName,
                        clientId,
                        status,
                        firstVisit,
                        birthday,
                    ];
                    return row.join(",");
                })
                    .join("\n"),
        };
    }

    async generateZOutReportPDF(data: any, facility: any, latestReconciliation: any, activePaymentMethods: any) {
        const templatePath = path.join(process.cwd(), 'templates', 'zout-report.hbs');
        const templateHtml = fs.readFileSync(templatePath, 'utf-8');

        const paymentMethodDocs = await this.paymentMethodModel.find({}).sort({ name: 1 });
        const shortIdToName = new Map(paymentMethodDocs.map((pm: any) => [pm.shortId, pm.name]));

        const compiledTemplate = Handlebars.compile(templateHtml);

        const cashSales = facility?.paymentMethodSummary?.filter((pm) => pm?.paymentMethod === PaymentMethod.CASH).reduce((sum, pm) => sum + (pm?.totalAmount || 0), 0) || 0;

        const { startingAmount = 0, pettyAmount = 0, drawerAmount = 0, leaveAmount = 0, overUnder = 0, depositAmount = 0, onlineOverUnder = 0 } = latestReconciliation || {};

        const cashSectionData = {
            facilityName: facility?.facilityName,
            startingAmount: startingAmount.toFixed(2),
            cashSales: cashSales.toFixed(2),
            pettyAmount: pettyAmount.toFixed(2),
            totalCash: (startingAmount + cashSales - pettyAmount).toFixed(2),
            drawerAmount: drawerAmount.toFixed(2),
            leaveAmount: leaveAmount.toFixed(2),
            overUnder: overUnder.toFixed(2),
            depositAmount: depositAmount.toFixed(2),
            overUnderPositive: overUnder.toFixed(2) >= 0 ? true : false,
        };
        let otherPaymentsTotalAmount = 0;
        let otherPaymentsTotalCollected = 0;
        let otherPaymentsTotalOverUnder = 0;
        const otherPaymentsData = (facility?.paymentMethodSummary || [])
            ?.filter(pm => pm?.paymentMethod !== PaymentMethod.CASH)
            .map(payment => {
                const method = payment?.paymentMethod;
                const reconciliation = latestReconciliation?.otherPayments.find(e => e?.method === method) || {};

                const amount = Number(payment?.totalAmount || 0);
                const collected = Number(reconciliation?.collected || 0);
                const overUnder = Number(reconciliation?.overUnder || 0);

                // Accumulate totals
                otherPaymentsTotalAmount += amount;
                otherPaymentsTotalCollected += collected;
                otherPaymentsTotalOverUnder += overUnder;

                return {
                    label: shortIdToName.get(method) || method,
                    amount: amount.toFixed(2),
                    collected: collected.toFixed(2),
                    overUnder: overUnder.toFixed(2),
                    overUnderPositive: overUnder >= 0
                };
            }) || [];
        const existingLabels = otherPaymentsData.map(item => item.label);
        activePaymentMethods.forEach(method => {
            if (
                !existingLabels.includes(method.name) &&
                method.name !== 'Split Payment' &&
                method.name !== 'Cash'
            ) {
                otherPaymentsData.push({
                    label: method.name,
                    amount: '0.00',
                    collected: '0.00',
                    overUnder: '0.00',
                    overUnderPositive: false
                });
            }
        });
        // otherPaymentsData.push({
        //     label: 'Other Over/Under',
        //     amount: (onlineOverUnder || 0).toFixed(2)
        // });

        const otherPaymentsTotals = {
            amount: otherPaymentsTotalAmount.toFixed(2),
            collected: otherPaymentsTotalCollected.toFixed(2),
            overUnder: otherPaymentsTotalOverUnder.toFixed(2),
            overUnderPositive: otherPaymentsTotalOverUnder >= 0,
        };
        const totalPayments = (facility?.paymentMethodSummary || [])?.reduce((sum, p) => sum + (p?.totalAmount || 0), 0) || 0;

        const totalsData = {
            allOverUnder: (overUnder + onlineOverUnder).toFixed(2),
            allPayments: totalPayments.toFixed(2),
            allOverUnderPositive: (overUnder + onlineOverUnder).toFixed(2) >= 0 ? true : false,
        };

        let totalAmount = 0;
        let totalSalesAmount = 0;

        const paymentRows = (facility?.paymentMethodSummary || []).map((payment) => {
            const paymentTotal = Number(payment?.totalAmount || 0);
            totalSalesAmount += paymentTotal;
            return {
                source: "POS",
                method: shortIdToName.get(payment?.paymentMethod) || payment?.paymentMethod,
                returns: "0.00",
                sales: paymentTotal.toFixed(2),
            };
        });

        const invoiceRows = (facility?.invoices || []).map((invoice) => {
            const itemName = invoice?.itemType === "service" ? invoice?.packageName : invoice?.productName;
            const quantity = Number(invoice?.quantity || 0);
            const unitPrice = invoice?.itemType === "service" ? Number(invoice?.unitPrice || 0) : Number(invoice?.salePrice || 0);
            const subTotal = unitPrice * quantity;
            const discountExcludeCart = Number(invoice?.discountExcludeCart || 0);
            const discountIncludeCart = Number(invoice?.discountIncludeCart || 0);
            const totalDiscount = discountExcludeCart + discountIncludeCart;
            const amountBeforeGst = subTotal - totalDiscount;
            const gstAmount = Number(invoice?.gstAmount || 0);
            const itemTotal = subTotal + gstAmount - totalDiscount;

            totalAmount += itemTotal;

            return {
                item: itemName,
                qty: quantity,
                unitPrice: unitPrice.toFixed(2),
                discount: totalDiscount.toFixed(2),
                amountBeforeGst: amountBeforeGst.toFixed(2),
                gst: gstAmount.toFixed(2),
                total: itemTotal.toFixed(2),
            };
        });

        const imagePath = path.resolve(__dirname, "../../../image/logo_svg.png");
        const imageFile = fs.readFileSync(imagePath);
        const base64Image = `data:image/png;base64,${imageFile.toString("base64")}`;

        const templateData = {
            ...data,
            cashSectionData,
            otherPaymentsData,
            totalsData,
            otherPaymentsTotals,
            startDate: data?.startDate,
            startTime: data?.startTime,
            currentDate: data?.currentDate,
            currentTime: data?.currentTime,
            day: data?.day?.toUpperCase(),
            generatedBy: `${data?.name} (${data?.role})`,
            salesData: {
                paymentRows,
                invoiceRows,
                invoiceTotalAmount: totalAmount.toFixed(2),
                facilitySalesAmount: totalSalesAmount.toFixed(2),
                facilityName: facility?.facilityName,
            },
            image: base64Image,
        };

        const html = compiledTemplate(templateData);

        const browser = await puppeteer.launch({
            args: ["--no-sandbox", "--disable-setuid-sandbox"],
        });

        const page = await browser.newPage();
        await page.setContent(html, { waitUntil: "networkidle0" });

        const pdfBuffer = Buffer.from(await page.pdf({ format: "A4" }));
        await browser.close();

        return pdfBuffer;
    }

    async salesByEmp(user: IUserDocument, body: ExportSalesByEmpDto, userTimezone: string) {
        const facilityDetails = await this.getOrganizationandFacility(user);
        const { facilityIds, startDate, endDate, staffIds } = body;

        const filter: any = {
            organizationId: new Types.ObjectId(facilityDetails.organizationId),
            PaymentStatus: { $ne: PaymentStatus.CANCELED },
        };

        if (Array.isArray(facilityIds) && facilityIds.length > 0) {
            filter["facilityId"] = { $in: facilityIds.map((_id) => new Types.ObjectId(_id)) };
        } else if (user.role.type !== ENUM_ROLE_TYPE.ORGANIZATION && facilityDetails.facilityId) {
            const facilityArray = Array.isArray(facilityDetails.facilityId) ? facilityDetails.facilityId : [facilityDetails.facilityId];

            filter["facilityId"] = {
                $in: facilityArray.map((id) => new Types.ObjectId(id)),
            };
        }

        if (startDate && endDate) {
            const startOfDay = new Date(new Date(startDate).setHours(0, 0, 0, 0));
            const endOfDay = new Date(new Date(endDate).setHours(23, 59, 59, 999));
            filter["invoiceDate"] = {
                $gte: startOfDay,
                $lte: endOfDay,
            };
        }

        if (staffIds?.length) {
            filter['createdBy'] = { $in: staffIds.map(_id => new Types.ObjectId(_id)) }
        }

        const salesData = await this.InvoiceModel.aggregate([
            { $match: filter },
            {
                $lookup: {
                    from: "users",
                    localField: "createdBy",
                    foreignField: "_id",
                    as: "creator",
                },
            },
            { $unwind: "$creator" },
            {
                $match: {
                    "creator.role": { $ne: ENUM_ROLE_TYPE.USER },
                },
            },
            {
                $addFields: {
                    purchaseItems: { $ifNull: ["$purchaseItems", []] },
                    productItem: { $ifNull: ["$productItem", []] }
                }
            },
            {
                $addFields: {
                    purchaseItems: { $ifNull: ["$purchaseItems", []] },
                    productItem: { $ifNull: ["$productItem", []] }
                }
            },
            {
                $addFields: {
                    serviceItems: {
                        $cond: [
                            { $gt: [{ $size: "$purchaseItems" }, 0] },
                            {
                                $map: {
                                    input: "$purchaseItems",
                                    as: "item",
                                    in: {
                                        $mergeObjects: [
                                            "$$item",
                                            {
                                                itemType: "service",
                                                invoiceDate: "$invoiceDate",
                                                orderId: "$orderId"
                                            }
                                        ]
                                    }
                                }
                            },
                            []
                        ]
                    },
                    productItems: {
                        $cond: [
                            { $gt: [{ $size: "$productItem" }, 0] },
                            {
                                $map: {
                                    input: "$productItem",
                                    as: "item",
                                    in: {
                                        $mergeObjects: [
                                            "$$item",
                                            {
                                                itemType: "product",
                                                invoiceDate: "$invoiceDate",
                                                invoiceNumber: "$invoiceNumber"
                                            }
                                        ]
                                    }
                                }
                            },
                            []
                        ]
                    }
                }
            },
            {
                $addFields: {
                    allItems: {
                        $concatArrays: [
                            { $ifNull: ["$serviceItems", []] },
                            { $ifNull: ["$productItems", []] }
                        ]
                    }
                }
            },
            {
                $group: {
                    _id: "$createdBy",
                    totalSales: { $sum: "$grandTotal" },
                    invoiceCount: { $sum: 1 },
                    creatorInfo: { $first: "$creator" },
                    invoices: { $push: "$allItems" },
                },
            },
            {
                $addFields: {
                    flattenedItems: {
                        $reduce: {
                            input: "$invoices",
                            initialValue: [],
                            in: { $concatArrays: ["$$value", "$$this"] },
                        },
                    },
                },
            },
            {
                $project: {
                    _id: 0,
                    createdBy: "$_id",
                    name: {
                        $ifNull: [
                            "$creatorInfo.name",
                            {
                                $concat: [{ $ifNull: ["$creatorInfo.firstName", ""] }, " ", { $ifNull: ["$creatorInfo.lastName", ""] }],
                            },
                        ],
                    },
                    email: "$creatorInfo.email",
                    role: "$creatorInfo.role",
                    totalSales: 1,
                    invoiceCount: 1,
                    invoices: "$flattenedItems"
                }
            },
            { $sort: { totalSales: -1 } }
        ]).exec();

        let csvContent = '';

        const headers = [
            'Name (Role)',
            'Order ID',
            'Item Name',
            'Unit Price',
            'Quantity',
            'Subtotal',
            'Total After Discount & GST',
            'Invoice Date',

        ];
        csvContent += headers.join(',') + '\n';

        salesData.forEach(staff => {
            csvContent += `"${staff?.name} (${staff?.role})",,,,,,\n`;
            let total = 0
            let totalOfSubtotal = 0

            // Process each item
            staff.invoices.forEach(item => {
                const unitPrice = item?.itemType === "service"
                    ? Number(item?.unitPrice || 0)
                    : Number(item?.salePrice || 0);
                const quantity = Number(item?.quantity || 0);
                const subtotal = unitPrice * quantity;
                const discountExcludeCart = Number(item?.discountExcludeCart || 0);
                const discountIncludeCart = Number(item?.discountIncludeCart || 0);
                const gstAmount = Number(item?.gstAmount || 0);
                const itemTotal = ((subtotal) + gstAmount - (discountExcludeCart + discountIncludeCart)).toFixed(2);
                const roundOff = Math.floor(Number(itemTotal))
                total = total + roundOff
                totalOfSubtotal = totalOfSubtotal + subtotal

                const row = [
                    '',
                    item.orderId,
                    item?.itemType === "service" ? item?.packageName : item?.productName,
                    `INR ${unitPrice.toFixed(2)}`,
                    quantity,
                    `INR ${subtotal.toFixed(2)}`,
                    `INR ${roundOff.toFixed(2)}`,
                    moment(item.invoiceDate).tz(userTimezone).format('DD/MM/YYYY HH:mm'),
                ];
                csvContent += row.join(',') + '\n';
            });
            csvContent += `,,,,,"- - - - - - - - -","- - - - - - - - -"\n`;
            csvContent += `"Grand Total",,,,,"INR ${totalOfSubtotal.toFixed(2)}","INR ${staff.totalSales.toFixed(2)}"\n\n`;

        });

        return {
            data: csvContent
        };
    }

    async salesByCategory(user: IUserDocument, body: ExportSalesReportDto, userTimezone: string) {
        const facilityDetails = await this.getOrganizationandFacility(user);
        const { facilityIds, startDate, endDate } = body;

        const filter: any = {
            organizationId: new Types.ObjectId(facilityDetails.organizationId),
        };

        if (Array.isArray(facilityIds) && facilityIds.length > 0) {
            filter["facilityId"] = { $in: facilityIds.map((_id) => new Types.ObjectId(_id)) };
        } else if (user.role.type !== ENUM_ROLE_TYPE.ORGANIZATION && facilityDetails.facilityId) {
            const facilityArray = Array.isArray(facilityDetails.facilityId) ? facilityDetails.facilityId : [facilityDetails.facilityId];

            filter["facilityId"] = {
                $in: facilityArray.map((id) => new Types.ObjectId(id)),
            };
        }

        if (startDate && endDate) {
            const startOfDay = new Date(new Date(startDate).setHours(0, 0, 0, 0));
            const endOfDay = new Date(new Date(endDate).setHours(23, 59, 59, 999));
            filter["invoiceDate"] = {
                $gte: startOfDay,
                $lte: endOfDay,
            };
        }

        let invoiceDetails: any = await this.InvoiceModel.aggregate([
            { $match: { ...filter, paymentStatus: PaymentStatus.COMPLETED } },
            {
                $unwind: "$purchaseItems",
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "purchaseItems.packageId",
                    foreignField: "_id",
                    as: "pricing",
                },
            },
            { $unwind: "$pricing" },
            {
                $unwind: {
                    path: "$pricing.services",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $lookup: {
                    from: "services",
                    localField: "pricing.services.serviceCategory",
                    foreignField: "_id",
                    as: "serviceCategory",
                },
            },
            {
                $addFields: {
                    serviceCategoryId: {
                        $arrayElemAt: ["$serviceCategory._id", 0],
                    },
                    serviceCategoryName: {
                        $arrayElemAt: ["$serviceCategory.name", 0],
                    },
                    unitPrice: { $toDouble: "$purchaseItems.unitPrice" },
                    quantity: { $toInt: "$purchaseItems.quantity" },
                    discountExcludeCart: { $toDouble: "$purchaseItems.discountExcludeCart" },
                    discountIncludeCart: { $toDouble: "$purchaseItems.discountIncludeCart" },
                    gstAmount: { $toDouble: "$purchaseItems.gstAmount" },
                    itemType: { $literal: "service" },
                },
            },
            {
                $addFields: {
                    subtotal: { $multiply: ["$unitPrice", "$quantity"] },
                    totalDiscount: {
                        $add: ["$discountExcludeCart", "$discountIncludeCart"],
                    },
                    totalAmount: {
                        $add: [
                            {
                                $subtract: ["$unitPrice", { $add: ["$discountExcludeCart", "$discountIncludeCart"] }],
                            },
                            "$gstAmount",
                        ],
                    },
                },
            },
            {
                $project: {
                    serviceCategoryId: 1,
                    serviceCategoryName: 1,
                    itemType: 1,
                    unitPrice: { $round: ["$unitPrice", 2] },
                    subtotal: { $round: ["$subtotal", 2] },
                    quantity: 1,
                    totalDiscount: { $round: ["$totalDiscount", 2] },
                    gstAmount: { $round: ["$gstAmount", 2] },
                    totalAmount: { $round: ["$totalAmount", 2] },
                },
            },
            {
                $group: {
                    _id: "$serviceCategoryId",
                    serviceCategoryName: { $first: "$serviceCategoryName" },
                    itemType: { $first: "$itemType" },
                    totalUnitPrice: { $sum: "$unitPrice" },
                    totalSubtotal: { $sum: "$subtotal" },
                    totalDiscount: { $sum: "$totalDiscount" },
                    totalGstAmount: { $sum: "$gstAmount" },
                    totalAmount: { $sum: "$totalAmount" },
                },
            },
            {
                $project: {
                    serviceCategoryName: 1,
                    itemType: 1,
                    totalUnitPrice: { $round: ["$totalUnitPrice", 2] },
                    totalSubtotal: { $round: ["$totalSubtotal", 2] },
                    totalDiscount: { $round: ["$totalDiscount", 2] },
                    totalGstAmount: { $round: ["$totalGstAmount", 2] },
                    totalAmount: { $round: ["$totalAmount", 2] },
                },
            },
            {
                $group: {
                    _id: null,
                    categories: { $push: "$$ROOT" },
                    grandTotal: { $sum: "$totalAmount" },
                },
            },
            { $unwind: "$categories" },
            {
                $addFields: {
                    "categories.revenuePercent": {
                        $round: [
                            {
                                $multiply: [{ $divide: ["$categories.totalAmount", "$grandTotal"] }, 100],
                            },
                            2,
                        ],
                    },
                },
            },
            {
                $replaceRoot: {
                    newRoot: "$categories",
                },
            },
        ]).exec();

        return {
            data:
                `Category,Subtotal (excluding tax),Total Discount,Tax,Total Amount After Tax,% of Revenue,Grouping\n` +
                invoiceDetails
                    ?.map((item: any) => {
                        const grouping = item?.itemType;
                        const category = item?.serviceCategoryName;
                        const subTotal = item?.totalSubtotal;
                        const totalDiscount = item?.totalDiscount;
                        const tax = item?.totalGstAmount;
                        const totalAmountAfterTax = item?.totalAmount;
                        const revenuePercent = item?.revenuePercent;

                        const row = [category, subTotal, totalDiscount, tax, totalAmountAfterTax, revenuePercent, grouping];
                        return row.join(",");
                    })
                    .join("\n"),
        };
    }

    async exportSalesList(user: IUserDocument, body: ExportSalesReportDto, userTimezone: string) {
        const facilityDetails = await this.getOrganizationandFacility(user);
        const { facilityIds, startDate, endDate } = body;

        const filter: any = {
            organizationId: new Types.ObjectId(facilityDetails.organizationId),
        };

        if (Array.isArray(facilityIds) && facilityIds.length > 0) {
            filter["facilityId"] = { $in: facilityIds.map((_id) => new Types.ObjectId(_id)) };
        } else if (user.role.type !== ENUM_ROLE_TYPE.ORGANIZATION && facilityDetails.facilityId) {
            const facilityArray = Array.isArray(facilityDetails.facilityId) ? facilityDetails.facilityId : [facilityDetails.facilityId];

            filter["facilityId"] = {
                $in: facilityArray.map((id) => new Types.ObjectId(id)),
            };
        }

        if (startDate && endDate) {
            const startOfDay = new Date(new Date(startDate).setHours(0, 0, 0, 0));
            const endOfDay = new Date(new Date(endDate).setHours(23, 59, 59, 999));
            filter["invoiceDate"] = {
                $gte: startOfDay,
                $lte: endOfDay,
            };
        }
        let invoiceDetails: any = await this.InvoiceModel.find(filter)
            .populate([
                { path: "createdBy", select: "_id name firstName lastName email" },
                { path: "cancelledBy", select: "firstName lastName name" },
                {
                    path: "facilityId",
                    select: "_id facilityName paymentMethods name",
                    populate: { path: "paymentMethods.paymentMethodId", model: "PaymentMethod", select: "_id name methodType" },
                }, // Nested population
            ])
            .sort({ orderId: 1 });
        let allSalesData = [];

        invoiceDetails.map((invoice) => {
            if (invoice && invoice.facilityId && invoice.facilityId.paymentMethods) {
                const updatedPaymentDetails = invoice.paymentDetails.map((detail) => {
                    const matchedMethod = invoice.facilityId.paymentMethods.find((pm) => pm.shortId.trim().toLowerCase() === detail.paymentMethod.trim().toLowerCase());
                    const cleanDetail = JSON.parse(JSON.stringify(detail));
                    return {
                        ...cleanDetail,
                        paymentMethodName: matchedMethod ? matchedMethod.name : "Unknown",
                    };
                });
                invoice.updatedPaymentDetails = updatedPaymentDetails;
            }

            if (!invoice) {
                throw new NotFoundException("Invoice not found or you do not have access to it");
            }

            const clientBillingUTCode = invoice.clientBillingDetails?.utCode;
            const billingUTCode = invoice.billingDetails?.utCode;
            const totalGSTValue = invoice?.totalGstValue;

            if (clientBillingUTCode === billingUTCode) {
                invoice.cgst = totalGSTValue / 2;
                invoice.sgst = totalGSTValue / 2;
                invoice.igst = 0;
            } else {
                invoice.igst = totalGSTValue;
                invoice.cgst = 0;
                invoice.sgst = 0;
            }
            const baseInvoiceData = {
                _id: invoice._id,
                createdBy: invoice.createdBy ? invoice.createdBy._id : null,
                createdByName: invoice.createdBy ? invoice.createdBy.name : null,
                invoiceDate: invoice.invoiceDate,
                invoiceNumber: invoice.invoiceNumber,
                orderId: invoice?.orderId || "",
                organizationId: invoice.organizationId,
                facilityId: invoice?.facilityId,
                facilityName: invoice?.billingDetails?.facilityName,
                billingDetails: invoice?.billingDetails,
                clientDetails: invoice?.clientDetails,
                clientBillingDetails: invoice?.clientBillingDetails,
                totalItems: invoice?.productItem?.length + invoice?.purchaseItems?.length,
                subTotal: invoice?.subTotal,
                discount: invoice?.discount || 0,
                cartDiscount: invoice?.cartDiscount || 0,
                cartDiscountType: invoice?.cartDiscountType || "",
                cartDiscountAmount: invoice?.cartDiscountAmount || 0,
                totalGstValue: invoice?.totalGstValue || 0,
                totalAmountAfterGst: invoice?.totalAmountAfterGst || 0,
                roundOff: invoice?.roundOff || 0,
                grandTotal: invoice?.grandTotal || 0,
                amountInWords: invoice?.amountInWords ? invoice?.amountInWords : "",
                amountPaid: invoice?.amountPaid,
                paymentStatus: invoice.paymentStatus,
                paymentDetails: invoice.paymentDetails,
                invoiceStatus: invoice.invoiceStatus,
                refundStatus: invoice.refundStatus,
                refundAmount: invoice.refundAmount,
                platform: invoice.platform,
                cgst: invoice.cgst,
                sgst: invoice.sgst,
                igst: invoice.igst,
                updatedPaymentDetails: invoice.updatedPaymentDetails,
                paymentReason: invoice.paymentReason,
                cancelledBy: invoice?.cancelledBy?.name
                    ? invoice.cancelledBy?.name
                    : invoice.cancelledBy?.firstName
                        ? invoice.cancelledBy?.firstName + " " + invoice.cancelledBy?.lastName
                        : null,
            };
            invoice?.purchaseItems?.forEach((item: any) => {
                allSalesData.push({
                    ...baseInvoiceData,
                    itemType: "service",
                    itemDetail: item,
                });
            });

            invoice?.productItem?.forEach((item: any) => {
                allSalesData.push({
                    ...baseInvoiceData,
                    itemType: "product",
                    itemDetail: item,
                });
            });
        });
        return {
            data:
                `Sale Date (${userTimezone}),Client Id,Client Name,Order ID,Item Type,Item Name,Location,Item Price (excluding Tax),Quantity,Sub Total (excluding Tax),Discount Amount (INR),Cart Discount Amount (INR),Amount Before GST (INR),HSN/SAC Code,TAX(%),CGST,SGST,IGST,Item Total (Including Tax),Invoice Total,Amount Paid,Payment Method,Payment Status,Cancelled By,Reason\n` +
                allSalesData
                    .map((item: any) => {
                        const quantity = Number(item?.itemDetail?.quantity || 0);
                        const unitPrice = item?.itemType === "service" ? Number(item?.itemDetail?.unitPrice || 0) : Number(item?.itemDetail?.salePrice || 0);
                        const subTotal = (unitPrice * quantity).toFixed(2);
                        const discountExcludeCart = Number(item?.itemDetail?.discountExcludeCart || 0);
                        const discountIncludeCart = Number(item?.itemDetail?.discountIncludeCart || 0);
                        const totalDiscount = (discountExcludeCart + discountIncludeCart).toFixed(2);
                        const amountBeforeGst = (unitPrice * quantity - (discountExcludeCart + discountIncludeCart)).toFixed(2);
                        const tax = Number(item?.itemDetail?.tax || 0).toFixed(2);
                        const gstAmount = Number(item?.itemDetail?.gstAmount || 0);
                        const cgst = (item?.clientBillingDetails?.utCode === item?.billingDetails?.utCode ? gstAmount / 2 : 0).toFixed(2);
                        const sgst = (item?.clientBillingDetails?.utCode === item?.billingDetails?.utCode ? gstAmount / 2 : 0).toFixed(2);
                        const igst = (item?.clientBillingDetails?.utCode !== item?.billingDetails?.utCode ? gstAmount : 0).toFixed(2);
                        const itemTotal = (unitPrice * quantity + gstAmount - (discountExcludeCart + discountIncludeCart)).toFixed(2);
                        const roundOff = Math.floor(Number(itemTotal)).toFixed(2);
                        const amountPaid = item?.amountPaid ? `INR ${item?.amountPaid.toFixed(2)}` : ``;
                        const cancelledBy = item?.cancelledBy;

                        const row = [
                            `${moment(item.invoiceDate).tz(userTimezone).format('DD/MM/YYYY')}-${moment(item.invoiceDate).tz(userTimezone).format('HH:mm')}`,
                            item?.clientDetails?.customerId,
                            item?.clientDetails?.name,
                            item.orderId,
                            item?.itemType,
                            item?.itemType === "service" ? item?.itemDetail?.packageName : item?.itemDetail?.productName,
                            item?.billingDetails?.facilityName,
                            `INR ${unitPrice.toFixed(2)}`,
                            quantity,
                            `INR ${subTotal}`,
                            `INR ${discountExcludeCart.toFixed(2)}`,
                            `INR ${discountIncludeCart.toFixed(2)}`,
                            `INR ${amountBeforeGst}`,
                            item?.itemDetail?.hsnOrSacCode,
                            `${tax} (%)`,
                            `INR ${cgst}`,
                            `INR ${sgst}`,
                            `INR ${igst}`,
                            `INR ${roundOff}`,
                            `INR ${item.grandTotal}`,
                            amountPaid,
                            item.paymentStatus !== PaymentStatus.PENDING ? `"${Array.isArray(item.paymentDetails) ? item.paymentDetails.map(detail => detail.paymentMethod).join(', ') : item.paymentDetails?.paymentMethod || ""}"` : "",
                            item.paymentStatus,
                            cancelledBy,
                            item?.paymentReason,
                        ];
                        return row.join(',');
                    }).join('\n')
        };
    }



    async getQrCodesForUser(
        userId: string,
        page = 1,
        limit = 10,
    ): Promise<{
        results: {
            qrCodeUrl: string | null;
            packageId: string;
            packageName: string;
        }[];
        total: number;
    }> {
        const skip = (page - 1) * limit;

        // Fetch all purchases for user, including those without QR codes
        const [purchases, total] = await Promise.all([
            this.PurchaseModel
                .find({ userId })
                .select('qrCodeUrl packageId')
                .skip(skip)
                .limit(limit)
                .lean(),
            this.PurchaseModel.countDocuments({ userId }),
        ]);

        // Fetch corresponding packages
        const packageIds = purchases.map(p => p.packageId?.toString()).filter(Boolean);

        const packages = await this.PricingModel
            .find({ _id: { $in: packageIds } })
            .select('_id name')
            .lean();

        const packageMap = new Map(packages.map(pkg => [pkg._id.toString(), pkg.name]));

        const results = purchases.map(p => ({
            qrCodeUrl: p.qrCodeUrl ? p.qrCodeUrl.toString() : null,
            packageId: p.packageId?.toString(),
            packageName: packageMap.get(p.packageId?.toString()) || 'N/A',
        }));

        return {
            results,
            total,
        };
    }

    async getEligibleReturnPricingByUserId(userId: string, body: PaginationDto, user: any): Promise<any> {
        let { page = 1, pageSize = 10, search } = body;
        if (isNaN(page) || page < 1) page = 1;
        if (isNaN(pageSize) || pageSize < 1) pageSize = 10;
        const skip = (page - 1) * pageSize;

        const query: any = {};
        if (user.role === ENUM_ROLE_TYPE.ORGANIZATION) {
            query.organizationId = new Types.ObjectId(user._id);
            query.userId = new Types.ObjectId(userId);
        } else if ([ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER].includes(user.role)) {
            const staffDetails = await this.StaffDetailsModel.findOne(
                { userId: user._id },
                { organizationId: 1, facilityId: 1 }
            ).exec();
            query.organizationId = new Types.ObjectId(staffDetails.organizationId);
            query.userId = new Types.ObjectId(userId);
        } else {
            throw new BadRequestException('Access Denied');
        }

        Object.assign(query, {
            isExpired: false,
            endDate: { $gte: new Date() },
            startDate: { $lte: new Date() },
            isActive: { $ne: false },
            bundledPricingId: { $exists: false },
            isExchanged: { $ne: true },
            exchangedInvoiceId: { $exists: false },
            sharePass: { $ne: true }
        });

        const pipeline: any[] = [
            { $match: query },
            {
                $lookup: {
                    from: "invoices",
                    localField: "invoiceId",
                    foreignField: "_id",
                    as: "invoiceDetails"
                }
            },
            { $unwind: { path: "$invoiceDetails", preserveNullAndEmptyArrays: false } },
            {
                $addFields: {
                    matchedPurchaseItem: {
                        $arrayElemAt: [
                            {
                                $filter: {
                                    input: "$invoiceDetails.purchaseItems",
                                    as: "item",
                                    cond: { $eq: ["$$item.packageId", "$packageId"] }
                                }
                            },
                            0
                        ]
                    }
                }
            },
            {
                $addFields: {
                    packageName: "$matchedPurchaseItem.packageName"
                }
            },
            {
                $project: {
                    _id: 0,
                    purchaseId: "$_id",
                    invoiceId: 1,
                    sessionType: 1,
                    packageName: 1,
                    packageId: "$matchedPurchaseItem.packageId",
                    packageStartDate: "$startDate",
                    packageEndDate: "$endDate",
                    unitPrice: "$matchedPurchaseItem.unitPrice",
                    discountExcludeCart: {
                        $cond: [
                            { $gt: ["$matchedPurchaseItem.quantity", 0] },
                            { $divide: ["$matchedPurchaseItem.discountExcludeCart", "$matchedPurchaseItem.quantity"] },
                            0
                        ]
                    },
                    discountIncludeCart: {
                        $cond: [
                            { $gt: ["$matchedPurchaseItem.quantity", 0] },
                            { $divide: ["$matchedPurchaseItem.discountIncludeCart", "$matchedPurchaseItem.quantity"] },
                            0
                        ]
                    },
                    updatedAt: 1
                }
            }
        ];

        if (search && search.trim().length > 0) {
            pipeline.push({
                $match: {
                    packageName: { $regex: search.trim(), $options: "i" }
                }
            });
        }

        pipeline.push(
            {
                $facet: {
                    total: [{ $count: "count" }],
                    data: [
                        { $sort: { updatedAt: -1 } },
                        { $skip: skip },
                        { $limit: pageSize }
                    ]
                }
            },
            {
                $project: {
                    total: { $ifNull: [{ $arrayElemAt: ["$total.count", 0] }, 0] },
                    data: 1
                }
            }
        );

        const result = await this.PurchaseModel.aggregate(pipeline);
        return {
            data: result.length ? result[0].data : [],
            count: result.length ? result[0].total : 0
        };
    }

    async updatePurchaseSession(dto: UpdatePurchaseSessionDto, user: any): Promise<any> {
        const { purchaseId, totalSessions, startDate, endDate, notes } = dto;

        const purchase = await this.PurchaseModel.findById(purchaseId);
        if (!purchase) {
            throw new NotFoundException('Purchase not found');
        }

        const previousValues: any = {
            startDate: purchase.startDate,
            endDate: purchase.endDate,
            totalSessions: purchase?.sessionType === SessionType.DAY_PASS ? purchase.dayPassLimit : purchase.totalSessions,
        };

        const updatedFields: any = {};

        const newStartDate = new Date(startDate);
        if (endDate && newStartDate > new Date(endDate)) {
            throw new BadRequestException('Start date cannot be after end date');
        }
        updatedFields.startDate = newStartDate
        purchase.startDate = newStartDate;

        const newEndDate = new Date(endDate);
        if (startDate && newStartDate > newEndDate) {
            throw new BadRequestException('Start date cannot be after end date');
        }
        updatedFields.endDate = newEndDate;
        purchase.endDate = newEndDate;


        switch (purchase.sessionType) {
            case SessionType.SINGLE:
                if (totalSessions !== 1) {
                    throw new BadRequestException(`Total Sessions count for ${purchase.sessionType} session type must be 1`);
                }
                if (totalSessions < purchase.sessionConsumed) {
                    throw new BadRequestException('Total sessions cannot be less than consumed sessions');
                }
                updatedFields.totalSessions = totalSessions;
                purchase.totalSessions = totalSessions;
                break;

            case SessionType.MULTIPLE:
                if (totalSessions < 1) {
                    throw new BadRequestException(`Total sessions count must be at least 1 for ${purchase.sessionType} session type`);
                }
                if (totalSessions < purchase.sessionConsumed) {
                    throw new BadRequestException('Total sessions cannot be less than consumed sessions');
                }
                updatedFields.totalSessions = totalSessions;
                purchase.totalSessions = totalSessions;
                break;

            case SessionType.DAY_PASS:
                if (totalSessions < 1) {
                    throw new BadRequestException(`Total sessions count must be at least 1 for ${purchase.sessionType} session type`);
                }
                const sessionsCount = await this.SchedulingModel.aggregate([
                    {
                        $match: {
                            purchaseId: new Types.ObjectId(purchaseId),
                            clientId: new Types.ObjectId(purchase.userId),
                            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                        },
                    },
                    {
                        $group: {
                            _id: "$date",
                        },
                    },
                ]);
                const bookedDates = sessionsCount.map((item) => item._id.toISOString().split("T")[0]);
                const consumedDayPassLimit = bookedDates.length;

                if (totalSessions < consumedDayPassLimit) {

                    throw new BadRequestException(`Total sessions count cannot be less than consumed sessions for ${purchase.sessionType} session type`);
                }

                updatedFields.totalSessions = totalSessions;
                purchase.totalSessions = Number.POSITIVE_INFINITY;
                purchase.dayPassLimit = totalSessions;
                break;

            case SessionType.UNLIMITED:
                updatedFields.totalSessions = purchase.totalSessions;
                break;
        }

        await purchase.save();
        const normalizeValue = (val: any) => {
            return val === Number.POSITIVE_INFINITY ? 'unlimited' : val;
        };
        let logsData = await this.PricingSessionLogsModel.create({
            userId: purchase.userId,
            organizationId: purchase.organizationId,
            facilityId: purchase.facilityId,
            purchaseId: purchase._id,
            packageId: purchase.packageId,
            modifiedBy: user._id,
            sessionType: purchase.sessionType,
            startDate: purchase.startDate,
            endDate: purchase.endDate,
            totalSessions: purchase.totalSessions,
            sessionConsumed: purchase.sessionConsumed,
            notes: notes || '',
            previousValues: {
                ...previousValues,
                totalSessions: normalizeValue(previousValues.totalSessions),
            },
            updatedValues: {
                ...updatedFields,
                totalSessions: normalizeValue(updatedFields.totalSessions),
            },
        });

        return logsData;
    }

    async getSessionLogsByPurchaseId(purchaseId: string, user: any, pagination: PaginationDto): Promise<{ totalCount: number; logs: any[] }> {
        const { page = 1, pageSize = 10 } = pagination;
        const skip = (page - 1) * pageSize;

        const query: any = {
            purchaseId: new Types.ObjectId(purchaseId)
        };

        if (user.role === ENUM_ROLE_TYPE.ORGANIZATION) {
            query.organizationId = new Types.ObjectId(user._id);
        } else if ([ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER].includes(user.role)) {
            const staffDetails = await this.StaffDetailsModel.findOne(
                { userId: user._id },
                { organizationId: 1, facilityId: 1 }
            ).lean();
            if (!staffDetails) throw new BadRequestException('Staff details not found');
            query.organizationId = new Types.ObjectId(staffDetails.organizationId);
            if (staffDetails.facilityId) {
                query.facilityId = { $in: (Array.isArray(staffDetails.facilityId) ? staffDetails.facilityId : [staffDetails.facilityId]).map(id => new Types.ObjectId(id)) };
            }
        } else if (user.role === ENUM_ROLE_TYPE.USER) {
            query.userId = new Types.ObjectId(user._id);
        } else {
            throw new BadRequestException('Access Denied');
        }

        const pipeline: any[] = [
            { $match: query },
            {
                $lookup: {
                    from: "users",
                    localField: "modifiedBy",
                    foreignField: "_id",
                    as: "userDetails",
                },
            },
            { $unwind: { path: "$userDetails", preserveNullAndEmptyArrays: true } },
            {
                $project: {
                    _id: 1,
                    userId: 1,
                    originizationId: 1,
                    facilityId: 1,
                    purchaseId: 1,
                    packageId: 1,
                    sessionType: 1,
                    notes: 1,
                    modifiedBy: 1,
                    modifiedByName: "$userDetails.name",
                    previousValues: 1,
                    updatedValues: 1,
                    createdAt: 1,
                    updatedAt: 1,
                },
            },
            { $sort: { createdAt: -1 } },
            {
                $facet: {
                    logs: [{ $skip: skip }, { $limit: pageSize }],
                    total: [{ $count: "count" }],
                },
            },
            {
                $project: {
                    logs: 1,
                    totalCount: { $ifNull: [{ $arrayElemAt: ["$total.count", 0] }, 0] },
                },
            },
        ];

        const result = await this.PricingSessionLogsModel.aggregate(pipeline);
        return result[0] || { totalCount: 0, logs: [] };
    }
}



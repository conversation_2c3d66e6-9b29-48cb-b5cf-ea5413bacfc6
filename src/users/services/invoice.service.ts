import { Injectable } from '@nestjs/common';
import { InjectModel } from "@nestjs/mongoose";
import { Model, PipelineStage, Types } from "mongoose";

import { MailService } from "src/mail/services/mail.service";
import { UploadService } from "src/utils/services/upload.service";
import * as puppeteer from 'puppeteer';
import * as fs from 'fs';
import * as path from 'path';
import { Invoice } from "../schemas/invoice.schema";
import { DiscountType } from 'src/utils/enums/discount.enum';
import { User } from "src/users/schemas/user.schema";
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
const numberToWords = require('number-to-words');

@Injectable()
export class InvoiceService {
    constructor(
        @InjectModel(Invoice.name) private InvoiceModel: Model<Invoice>,
        @InjectModel(User.name) private UserModel: Model<User>,

        private readonly mailerService: MailService,
        private UploadService: UploadService,

    ) { }
    convertAmountToWords(amount) {
        const amountInWords = numberToWords.toWords(amount);
        return amountInWords.charAt(0).toUpperCase() + amountInWords.slice(1) + " Rupees Only";
    }

    async generateInvoice(invoiceData: any, orgEmail: String) {
        try {
            let data = invoiceData.toObject ? invoiceData.toObject() : invoiceData;
            // Step 1: Process the invoice data
            const formattedDate = new Date(data.invoiceDate).toDateString();
            data.invoiceDate = formattedDate.split(' ').slice(1).join(' ');
            data.purchaseItems = data.purchaseItems.map((item, index) => ({
                ...item,
                index: index + 1, // Start index from 1
            }));
            data.productItem = data.productItem.map((item, index) => ({
                ...item,
                index: data.purchaseItems.length + index + 1, // Continue index after purchaseItems
            }));
            data.customPackageItems = data.customPackageItems.map((item, index) => ({
                ...item,
                index: data.customPackageItems.length + index + 1,
            }));
            const clientBillingUTCode = data?.clientBillingDetails?.utCode;
            const billingUTCode = data?.billingDetails?.utCode;
            const totalGSTValue = data?.totalGstValue;

            if (clientBillingUTCode === billingUTCode) {
                data.cgst = totalGSTValue / 2;
                data.sgst = totalGSTValue / 2;
                data.igst = 0;
            } else {
                data.igst = totalGSTValue;
                data.cgst = 0;
                data.sgst = 0;
            }
            // Step 2: Load and compile the Handlebars template
            const paths = path.join(process.cwd(), "templates", "invoices.hbs");
            const template = fs.readFileSync(paths, "utf-8");
            const Handlebars = require("handlebars");
            const options = {
                allowProtoPropertiesByDefault: true,
                allowProtoMethodsByDefault: true
            };
            Handlebars.registerHelper("eq", (a, b) => a === b);
            Handlebars.registerHelper("inc", function (value) {
                return parseInt(value) + 1;
            });
            Handlebars.registerHelper('eq', function (a, b) {
                return a === b;
            });
            Handlebars.registerHelper('showZero', function (value) {
                return value !== undefined && value !== null ? value : 0;
            });
            // Register a helper for "not equal"
            Handlebars.registerHelper('neq', function (a, b) {
                return a !== b;
            });
            const compiledTemplate = Handlebars.compile(template, options);
            // Step 3: Generate HTML and PDF
            const html = compiledTemplate(data);
            const browser = await puppeteer.launch({
                args: ['--no-sandbox', '--disable-setuid-sandbox'],
            });
            const page = await browser.newPage();
            await page.setContent(html);
            const pdfBuffer = Buffer.from(await page.pdf({ format: "A4" }));
            await browser.close();

            // // Save PDF buffer to local file
            // const localFilePath = path.join(process.cwd(), 'temp', `invoice-${data.invoiceNumber}.pdf`);
            // // Ensure temp directory exists
            // if (!fs.existsSync(path.join(process.cwd(), 'temp'))) {
            //     fs.mkdirSync(path.join(process.cwd(), 'temp'));
            // }
            // fs.writeFileSync(localFilePath, pdfBuffer);

            // Step 4: Upload the PDF to S3
            const s3Path = "invoices/";
            const fileName = `invoice-${data.orderId}.pdf`;
            const s3UploadResponse = await this.UploadService.uploadPdf(pdfBuffer, s3Path, fileName, "application/pdf");

            // Step 5: Update invoice record with file path
            await this.InvoiceModel.findOneAndUpdate(
                { _id: data._id },
                { invoiceFilePath: s3UploadResponse.Location },
                { new: true, upsert: true }
            );
            const staffsList = await this.getALltheStaffs(data?.facilityId);
            //Step 6: Send email with invoice attachment
            await this.mailerService.sendMail({
                to: data?.clientDetails?.email.toString(),
                subject: `Invoice #${data.invoiceNumber}`,
                template: "invoice-body",
                context: data,
                attachments: [
                    {
                        filename: `invoice-${data.invoiceNumber}.pdf`,
                        path: s3UploadResponse.Location,
                    },
                ],
            });

            /* Send mail to the organization about the package sold to which client */
            await this.mailerService.sendMail({
                to: orgEmail?.toString(),
                subject: `Package Sold`,
                template: "package-sold",
                context: data,
                attachments: [
                    {
                        filename: `invoice-${data.invoiceNumber}.pdf`,
                        path: s3UploadResponse.Location,
                    },
                ],
            });
            for (const staff of staffsList) {
                await this.mailerService.sendMail({
                    to: staff.email.toString(),
                    subject: `Package Sold`,
                    template: "package-sold",
                    context: data,
                    attachments: [
                        {
                            filename: `invoice-${data.invoiceNumber}.pdf`,
                            path: s3UploadResponse.Location,
                        },
                    ],
                })
            }
            return { message: "Invoice email sent successfully!" };
        } catch (error) {
            console.error("Error in generateInvoice function:", error);
            return {
                message: "Error generating invoice",
                details: error.message
            };
        }
    }
    private async getALltheStaffs(facilityId: Types.ObjectId): Promise<any[]> {
        const commonPipeline: any[] = [
            {
                $lookup: {
                    from: "staffprofiledetails",
                    localField: "_id",
                    foreignField: "userId",
                    as: "staffDetails",
                    pipeline: [{ $match: { facilityId: { $in: [facilityId] } } }],
                },
            },
            { $unwind: { path: "$staffDetails", preserveNullAndEmptyArrays: false } },
            {
                $lookup: {
                    from: 'role',
                    localField: "role",
                    foreignField: "_id",
                    as: "roleDetails",
                    pipeline: [
                        {
                            $match: {
                                type: { $in: [ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER] },
                            },
                        },
                    ],
                }
            },
            { $unwind: { path: "$roleDetails", preserveNullAndEmptyArrays: false } },
            {
                $lookup: {
                    from: "facilities",
                    localField: "staffDetails.facilityId",
                    foreignField: "_id",
                    as: "facilityDetails",
                },
            },
            { $unwind: { path: "$facilityDetails", preserveNullAndEmptyArrays: true } },
        ];
        commonPipeline.push({
            $group: {
                _id: "$staffDetails._id",
                gender: { $first: "$staffDetails.gender" },
                profilePicture: { $first: "$staffDetails.profilePicture" },
                userId: { $first: "$_id" },
                firstName: { $first: "$firstName" },
                lastName: { $first: "$lastName" },
                mobile: { $first: "$mobile" },
                email: { $first: "$email" },
                role: { $first: "$role" },
                isActive: { $first: "$isActive" },
                createdAt: { $first: "$createdAt" },
                facilityNames: { $push: "$facilityDetails.facilityName" },
            },
        });
        commonPipeline.push(
            {
                $sort: {
                    isActive: -1,
                    updatedAt: -1,
                },
            },
            {
                $facet: {
                    metadata: [{ $count: "total" }],
                    data: [
                        {
                            $project: {
                                _id: 1,
                                userId: 1,
                                firstName: 1,
                                lastName: 1,
                                facilityNames: 1,
                                mobile: 1,
                                email: 1,
                                role: 1,
                                isActive: 1,
                                createdAt: 1,
                                gender: 1,
                                profilePicture: 1,
                            },
                        },

                    ],
                },
            },
        );

        // Execute aggregation
        const result = await this.UserModel.aggregate(commonPipeline);
        return result[0]?.data || []

    }
}

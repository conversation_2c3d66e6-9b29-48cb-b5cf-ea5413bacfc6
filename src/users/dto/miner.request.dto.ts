import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { Gender } from "src/utils/enums/gender.enum";
import { IsArray, IsEnum, IsMongoId, IsNotEmpty, IsOptional, ValidateNested } from "class-validator";
import { RELATION_ENUM } from "src/utils/enums/relation.enum";

export class MinerRequestDto {
    @ApiProperty({
        description: "First name of the miner.",
        example: "John",
        minLength: 2,
        maxLength: 50,
        required: true,
    })
    @IsNotEmpty({ message: "First Name is required" })
    @Type(() => String)
    firstName: string;

    @ApiProperty({
        description: "Last name of the miner.",
        example: "Wick",
        minLength: 2,
        maxLength: 50,
        required: false,
    })
    @IsOptional()
    @Type(() => String)
    lastName: string = '';

    @ApiProperty({
        description: "DOB of the miner.",
        example: "2025-01-01",
        required: true,
    })
    @IsNotEmpty({ message: "DOB is required" })
    @Type(() => Date)
    dob: Date;

    @ApiProperty({
        description: "Relation of the miner with the client.",
        example: "father",
        required: true,
    })
    @IsNotEmpty({ message: "Relation is required" })
    @Type(() => String)
    @IsEnum(RELATION_ENUM, { message: "Invalid relation" })
    relation: RELATION_ENUM;

    @ApiProperty({
        description: "Gender of the miner.",
        enum: Gender,
        example: Gender.MALE,
        required: true,
    })
    @IsNotEmpty({ message: "Gender is required" })
    @Type(() => String)
    gender: Gender
    @ApiProperty({
        description: "Id of the minor if coming from the wavieer form",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: " Invalid Minor Id" })
    minorId?: string;
}


export class CreateMinorRequestDto {
    @ApiProperty({
        description: "Id of the client",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "Client id is required" })
    @Type(() => String)
    clientId: string;

    @ApiProperty({
        description: "Id of the waiver lead",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid Wavier Source Id" })
    wavierSourceId: string;

    @ApiProperty({
        description: "Minor request data.",
        type: [MinerRequestDto],
        example: MinerRequestDto,
        required: false,
    })
    @IsOptional()
    @ValidateNested({ each: true, message: "Miner request data is invalid" })
    @IsArray({ message: "Miner request data must be an array" })
    @Type(() => MinerRequestDto)
    minor?: MinerRequestDto[] = [];

}

import { IsString, IsDateString, IsInt, IsOptional, IsNotEmpty } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class UpdatePurchaseSessionDto {
    @ApiProperty({
        description: 'ID of the purchase',
        example: '659d268dee4b6081dacd41fd',
        required: true
    })
    @IsNotEmpty({ message: 'Purchase ID is required' })
    @IsString()
    purchaseId: string;

    @ApiProperty({
        description: 'Number of Total sessions',
        example: 2,
        required: true
    })
    @IsInt()
    totalSessions: number;

    @ApiProperty({
        description: "Start date of pricing",
        example: "2024-09-15T00:00:00Z",
        required: true
    })
    @IsNotEmpty({ message: 'Start date is required' })
    startDate: Date;

    @ApiProperty({
        description: "End date of pricing",
        example: "2024-09-20T00:00:00Z",
        required: true
    })
    @IsNotEmpty({ message: 'End date is required' })
    endDate: Date;

    @ApiProperty({
        description: "Optional notes for the update",
        example: "Adjusted due to rescheduling request",
        required: false
    })
    @IsString()
    @IsOptional()
    notes?: string;
}



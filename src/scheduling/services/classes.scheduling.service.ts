import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, PipelineStage, Types } from "mongoose";
import moment from "moment";
import { TransactionService } from "src/utils/services/transaction.service";
import { AppointmentTypeDocument, Services } from "src/organization/schemas/services.schema";
import { Scheduling } from "src/scheduling/schemas/scheduling.schema";
import { ClassType } from "src/utils/enums/class-type.enum";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { User, UserDocument } from "src/users/schemas/user.schema";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { ClassScheduleCreateDto } from "../dto/class.schedule.create.dto";
import { SchedulingService } from "./scheduling.service";
import { Facility } from "src/facility/schemas/facility.schema";
import { Purchase, PurchaseDocument } from "src/users/schemas/purchased-packages.schema";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { Pricing } from "src/organization/schemas/pricing.schema";
import { IDatabaseFindAllOptions } from "src/common/database/interfaces/database.interface";
import { Enrollment } from "src/courses/schemas/enrollment.schema";
import { ClassCancelEnrollDto, ClassScheduleEnrollDto } from "../dto/class.schedule.enroll.dto";
import { Clients } from "src/users/schemas/clients.schema";
import { ScheduleStatusType } from "../enums/schedule-status.enum";
import { ClassScheduleGetUserForEnrollmentDto } from "../dto/class.schedule.get.dto";
import { CheckedInDto } from "src/courses/dto/courses.dto";
import { ClassScheduleUpdateDto } from "../dto/class.schedule.update.dto";
import { MailService } from "src/mail/services/mail.service";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";

@Injectable()
export class ClassesSchedulingService {
    constructor(
        @InjectModel(Scheduling.name) private SchedulingModel: Model<Scheduling>,
        @InjectModel(Services.name) private ServiceModel: Model<Services>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(Purchase.name) private PurchaseModel: Model<Purchase>,
        @InjectModel(Pricing.name) private PricingModel: Model<Pricing>,
        @InjectModel(Enrollment.name) private EnrollmentModel: Model<Enrollment>,
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(Clients.name) private ClientsModel: Model<Clients>,
        private readonly schedulingService: SchedulingService,
        private readonly transactionService: TransactionService,
        private readonly paginationService: PaginationService,
        private readonly mailService: MailService,

    ) { }

    async getEnrolledCount(schedulingIds: Types.ObjectId[]): Promise<{ schedulingId: number; count: number }[]> {
        return this.EnrollmentModel.aggregate([
            { $match: { schedulingId: { $in: schedulingIds } } },
            { $group: { _id: "$schedulingId", count: { $sum: 1 } } },
            { $project: { _id: 0, schedulingId: "$_id", count: 1 } },
        ]);
    }

    async getClassesList(filter: any, options: any, isClient = false): Promise<{
        data: any[];
        count: number;
    }> {
        let list = [];
        const schedulingData = await this.SchedulingModel.find(
            filter,
        ).sort(this.paginationService
            .orderFormat(options.order))
            .skip(options.paging.offset)
            .limit(options.paging.limit);

        const count = await this.SchedulingModel.countDocuments(filter);

        const scheduleIds = schedulingData.map((item) => item._id);
        const enrolledCount = await this.getEnrolledCount(scheduleIds);

        list = await Promise.all(schedulingData.map(async (item: any) => {
            const populatedSchedule = await item.populate([
                { path: "facilityId", select: "_id facilityName address profilePicture" },
                { path: "trainerId", select: "_id name firstName lastName email" },

                { path: "serviceCategoryId", select: "_id name attributeType appointmentType" },
                { path: "roomId", select: "_id roomName capacity description" },
            ]);
            const appointmentTypes = populatedSchedule?.serviceCategoryId?.appointmentType || [];
            const matchedAppointment = appointmentTypes.find((apt: any) => apt._id.toString() === populatedSchedule.subTypeId?.toString());
            const enrolled = enrolledCount.find((item) => item.schedulingId.toString() === populatedSchedule._id.toString())?.count || 0;

            return {
                _id: populatedSchedule._id,
                organizationId: item.organizationId,
                facility: {
                    _id: populatedSchedule.facilityId._id,
                    facilityName: populatedSchedule.facilityId.facilityName,
                },
                trainer: {
                    _id: populatedSchedule.trainerId._id,
                    name: `${populatedSchedule.trainerId.firstName || ""} ${populatedSchedule.trainerId.lastName || ""}`,
                },
                serviceCategory: {
                    _id: populatedSchedule.serviceCategoryId._id,
                    name: populatedSchedule.serviceCategoryId.name,
                },
                subType: {
                    _id: matchedAppointment?._id,
                    name: matchedAppointment?.name,
                },
                room: {
                    _id: populatedSchedule.roomId._id,
                    name: populatedSchedule.roomId.roomName,
                    capacity: populatedSchedule.roomId.capacity,
                },
                classType: populatedSchedule.classType,
                scheduleStatus: populatedSchedule.scheduleStatus,
                capacity: populatedSchedule.classCapacity,
                enrolled: enrolled,
                date: populatedSchedule.date,
                from: populatedSchedule.from,
                to: populatedSchedule.to,
                duration: populatedSchedule.duration,
                sessions: populatedSchedule.sessions,
            };
        }));

        return {
            data: list,
            count: count
        };
    }

    async getScheduleDetails(scheduleId: IDatabaseObjectId, organizationId?: IDatabaseObjectId, userId?: IDatabaseObjectId): Promise<any> {
        try {
            const find = {
                _id: scheduleId,
            }
            if (organizationId) {
                find['organizationId'] = organizationId
            }
            const [schedule, enrolledCount] = await Promise.all([this.SchedulingModel.findOne(find), this.getEnrolledCount([new Types.ObjectId(scheduleId)])]);
            if (!schedule) throw new NotFoundException("Schedule not found");
            const populatedSchedule: any = await schedule.populate([
                { path: "facilityId", select: "_id facilityName address profilePicture" },
                { path: "trainerId", select: "_id name firstName lastName email" },
                { path: "serviceCategoryId", select: "_id name attributeType appointmentType" },
                { path: "roomId", select: "_id roomName capacity description" },
            ]);

            const appointmentTypes = populatedSchedule?.serviceCategoryId?.appointmentType || [];
            const matchedAppointment = appointmentTypes.find((apt: any) => apt._id.toString() === populatedSchedule.subTypeId?.toString());
            const isEnrolled = userId ? await this.EnrollmentModel.exists({ schedulingId: scheduleId, userId: userId }) : undefined;
            const checkIns = await this.EnrollmentModel.countDocuments({ schedulingId: scheduleId, isCheckedIn: true })

            const data = {
                _id: populatedSchedule._id,
                facility: {
                    _id: populatedSchedule.facilityId._id,
                    facilityName: populatedSchedule.facilityId.facilityName,
                },
                trainer: {
                    _id: populatedSchedule.trainerId._id,
                    name: `${populatedSchedule.trainerId.firstName || ""} ${populatedSchedule.trainerId.lastName || ""}`,
                },
                serviceCategory: {
                    _id: populatedSchedule.serviceCategoryId._id,
                    name: populatedSchedule.serviceCategoryId.name,
                },
                subType: {
                    _id: matchedAppointment?._id,
                    name: matchedAppointment?.name,
                },
                room: {
                    _id: populatedSchedule.roomId._id,
                    name: populatedSchedule.roomId.roomName,
                    capacity: populatedSchedule.roomId.capacity,
                },
                classType: populatedSchedule.classType,
                scheduleStatus: populatedSchedule.scheduleStatus,
                capacity: populatedSchedule.classCapacity,
                dateRange: schedule.dateRange,
                enrolled: enrolledCount[0]?.count || 0,
                date: populatedSchedule.date,
                from: populatedSchedule.from,
                to: populatedSchedule.to,
                checkIns: checkIns,
                duration: populatedSchedule.duration,
                sessions: populatedSchedule.sessions,
                notes: schedule.notes,
                isEnrolled: !!isEnrolled,
            }
            return data;
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async getCustomerList(organizationId: IDatabaseObjectId, find: ClassScheduleGetUserForEnrollmentDto & { [key: string]: any }, options: IDatabaseFindAllOptions): Promise<any> {
        try {
            const { scheduleId, _search } = find
            const schedule = await this.SchedulingModel.findById(scheduleId)
            if (!schedule) {
                throw new NotFoundException("Schedule not found")
            }
            const { facilityId, classType, serviceCategoryId, subTypeId, date, from } = schedule

            let userMatch: any = {}
            if (_search) {
                userMatch['$match'] = {
                    ...userMatch,
                    ..._search
                }
            }

            const usersPipe: PipelineStage[] = [
                {
                    $match: {
                        organizationId,
                        facilityId: facilityId,
                        isExpired: false,
                        startDate: { $lte: moment(date).startOf('day').add(moment.duration(from)).toDate() },
                        endDate: { $gte: moment(date).startOf('day').add(moment.duration(from)).toDate() },
                    }
                },
                {
                    $lookup: {
                        from: "pricings",
                        localField: "packageId",
                        foreignField: "_id",
                        as: "packageData",
                        pipeline: [
                            {
                                $match: {
                                    "services.type": classType,
                                    $or: [
                                        {
                                            $and: [
                                                { "services.serviceCategory": serviceCategoryId },
                                                { "services.appointmentType": subTypeId }
                                            ]
                                        },
                                        {
                                            $and: [
                                                { "services.relationShip.serviceCategory": serviceCategoryId },
                                                { "services.relationShip.subTypeIds": subTypeId }
                                            ]
                                        },
                                    ]
                                },
                            },
                            { $project: { _id: 1 } },
                        ],
                    }
                },
                { $unwind: { path: "$packageData", preserveNullAndEmptyArrays: false } },
                {
                    $group: {
                        _id: "$userId",
                        userId: { $first: "$userId" }
                    },
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "userId",
                        foreignField: "_id",
                        as: "userData",
                        pipeline: [
                            ..._search ? [
                                userMatch
                            ] : [],
                            {
                                $lookup: {
                                    from: "clients",
                                    localField: "_id",
                                    foreignField: "userId",
                                    as: "clientData",
                                    pipeline: [
                                        { $project: { clientId: 1 } },
                                    ],
                                }
                            },
                            { $unwind: { path: "$clientData", preserveNullAndEmptyArrays: false } },
                            {
                                $project: {
                                    clientId: "$clientData.clientId",
                                    name: 1,
                                    firstName: 1,
                                    lastName: 1,
                                    email: 1,
                                    countryCode: 1,
                                    mobile: 1,
                                },
                            },
                        ],
                    }
                },
                { $unwind: { path: "$userData", preserveNullAndEmptyArrays: false } },
                {
                    $lookup: {
                        from: "enrollments",
                        let: {
                            userId: "$userId",
                            schedulingId: new Types.ObjectId(scheduleId)
                        },
                        pipeline: [{
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$userId", "$$userId"] },
                                        { $eq: ["$schedulingId", "$$schedulingId"] }
                                    ]
                                }
                            }
                        }],
                        as: "enrollmentData",
                    },
                },
                { $unwind: { path: "$enrollmentData", preserveNullAndEmptyArrays: true } },
                { $match: { "enrollmentData._id": { $exists: false } } },
                {
                    $facet: {
                        total: [
                            { $count: "total" }
                        ],
                        list: [
                            { $sort: this.paginationService.orderFormat(options.order) },
                            { $skip: options.paging.offset },
                            { $limit: options.paging.limit },
                            {
                                $project: {
                                    _id: "$userId",
                                    clientId: "$userData.clientId",
                                    name: "$userData.name",
                                    firstName: "$userData.firstName",
                                    lastName: "$userData.lastName",
                                    email: { $cond: { if: "$userData.email", then: "$userData.email", else: "" } },
                                    countryCode: { $cond: { if: "$userData.countryCode", then: "$userData.countryCode", else: "" } },
                                    mobile: { $cond: { if: "$userData.mobile", then: "$userData.mobile", else: "" } },
                                    enrollmentId: { $cond: { if: "$enrollmentData._id", then: "$enrollmentData._id", else: null } },
                                    isEnrolled: { $cond: { if: "$enrollmentData._id", then: true, else: false } },
                                    isCheckedIn: { $cond: { if: "$enrollmentData.isCheckedIn", then: "$enrollmentData.isCheckedIn", else: false } },
                                    enrolledDate: { $cond: { if: "$enrollmentData.createdAt", then: "$enrollmentData.createdAt", else: null } },
                                },
                            },
                        ],
                    }
                }
            ]
            const result = await this.PurchaseModel.aggregate(usersPipe);
            return {
                list: result[0]?.list || [],
                count: result[0]?.total[0]?.total || 0,
            };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async getParticipantList(find: ClassScheduleGetUserForEnrollmentDto & { [key: string]: any }, options: IDatabaseFindAllOptions): Promise<any> {
        try {
            const { scheduleId, _search } = find
            const schedule = await this.SchedulingModel.exists({ _id: scheduleId })
            if (!schedule) {
                throw new NotFoundException("Schedule not found")
            }

            let userMatch: any = {}
            if (_search) {
                userMatch['$match'] = {
                    ...userMatch,
                    ..._search
                }
            }

            const pipeline: PipelineStage[] = [
                {
                    $match: {
                        schedulingId: new Types.ObjectId(scheduleId)
                    }
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "userId",
                        foreignField: "_id",
                        as: "userData",
                        pipeline: [
                            ..._search ? [
                                userMatch
                            ] : [],
                            {
                                $lookup: {
                                    from: "clients",
                                    localField: "_id",
                                    foreignField: "userId",
                                    as: "clientData",
                                    pipeline: [
                                        { $project: { clientId: 1 } },
                                        { $limit: 1 }
                                    ],
                                }
                            },
                            { $unwind: { path: "$clientData", preserveNullAndEmptyArrays: false } },
                            {
                                $project: {
                                    clientId: "$clientData.clientId",
                                    name: 1,
                                    firstName: 1,
                                    lastName: 1,
                                    email: 1,
                                    countryCode: 1,
                                    mobile: 1,
                                },
                            },
                            { $limit: 1 }
                        ],
                    }
                },
                { $unwind: { path: "$userData", preserveNullAndEmptyArrays: false } },
                {
                    $facet: {
                        total: [
                            { $count: "total" }
                        ],
                        list: [
                            { $sort: this.paginationService.orderFormat(options.order) },
                            { $skip: options.paging.offset },
                            { $limit: options.paging.limit },
                            {
                                $project: {
                                    _id: "$userId",
                                    clientId: "$userData.clientId",
                                    name: "$userData.name",
                                    firstName: "$userData.firstName",
                                    lastName: "$userData.lastName",
                                    email: { $cond: { if: "$userData.email", then: "$userData.email", else: "" } },
                                    countryCode: { $cond: { if: "$userData.countryCode", then: "$userData.countryCode", else: "" } },
                                    mobile: { $cond: { if: "$userData.mobile", then: "$userData.mobile", else: "" } },
                                    enrollmentId: { $cond: { if: "$_id", then: "$_id", else: null } },
                                    isEnrolled: { $cond: { if: "$_id", then: true, else: false } },
                                    isCheckedIn: { $cond: { if: "$isCheckedIn", then: "$isCheckedIn", else: false } },
                                    enrolledDate: { $cond: { if: "$createdAt", then: "$createdAt", else: null } },
                                },
                            },
                        ],
                    }
                }
            ]

            const result = await this.EnrollmentModel.aggregate(pipeline);
            return {
                list: result[0]?.list || [],
                count: result[0]?.total[0]?.total || 0,
            };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async cancelSchedule(userId: IDatabaseObjectId, scheduleId: IDatabaseObjectId): Promise<boolean> {
        const session = await this.transactionService.startTransaction();
        try {
            const schedule = await this.SchedulingModel.findOne({ _id: scheduleId, organizationId: userId });
            if (!schedule) throw new NotFoundException("Schedule not found");

            const now = moment()
            const scheduleDate = moment(`${schedule.date.toISOString().split("T")[0]}T${schedule.from}:00`);
            if (scheduleDate < now) throw new BadRequestException("Schedule already passed");
            if (schedule.scheduleStatus === ScheduleStatusType.CANCELED) throw new BadRequestException("Scheduled class is already cancelled");

            schedule.scheduleStatus = ScheduleStatusType.CANCELED;
            schedule.canceledBy = userId;
            schedule.canceledAt = new Date();

            await Promise.all([
                schedule.save({ session }),
                this.EnrollmentModel.updateMany({ schedulingId: scheduleId }, { $set: { isCheckedIn: false, enrollmentStatus: ScheduleStatusType.CANCELED } }, { session }),
                this.PurchaseModel.updateMany({ schedulingId: scheduleId }, { $inc: { sessionConsumed: -1 * schedule.sessions } }, { session }),
            ])

            await this.transactionService.commitTransaction(session);
            return true
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw new BadRequestException(error.message);
        } finally {
            session.endSession();
        }
    }

    async checkForExistingScheduleConflict(date: Date, from: string, to: string, scheduleId?: string) {
        const existingBooking = await this.SchedulingModel.find({
            _id: { $ne: scheduleId },
            classType: { $in: [ClassType.CLASSES] },
            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] }, // skip canceled
            date,
        });

        for (let booking of existingBooking) {
            // Check for overlapping time slots
            const existingFrom = booking.from;
            const existingTo = booking?.to;

            if (
                existingBooking &&
                ((from >= existingFrom && from < existingTo) || // New start time falls within existing booking
                    (to > existingFrom && to <= existingTo) || // New end time falls within existing booking
                    (from <= existingFrom && to >= existingTo))
            ) {
                // New booking completely overlaps existing booking
                throw new BadRequestException(`Schedule already exists from ${existingFrom} to ${existingTo}`);
            }
        }
        return true;
    }

    private isFromLessThanTo({ from, to }: { from: string; to: string }): boolean {
        return new Date(`1970-01-01T${from}:00`) < new Date(`1970-01-01T${to}:00`);
    }

    /**
     * Schedule a class
     * @param organizationId 
     * @param user 
     * @param body 
     * @returns 
     */
    async createClassScheduling(organizationId: IDatabaseObjectId, user: IUserDocument, body: ClassScheduleCreateDto,): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const { facilityId, trainerId, classType, subType, serviceCategory, duration, roomId, dateRange, date, from, to, notes, capacity } = body;
            if (!this.isFromLessThanTo({ from: body.from, to: body.to })) throw new BadRequestException("Invalid time range.");
            if (new Date(`${date.toISOString().split("T")[0]}T${from}:00`) < new Date()) throw new BadRequestException("Class cannot start in the past.");

            const facility = await this.FacilityModel.findOne({ _id: facilityId, organizationId }).exec();
            await this.schedulingService.validateFacilityOwnership(facility, user);
            await this.schedulingService.validateFacilityAvailability(date, { from, to }, facilityId);
            await this.checkForExistingScheduleConflict(date, from, to);
            await this.schedulingService.validateStaffSpecialization(trainerId, serviceCategory, subType);
            await this.schedulingService.validateIsStaffBusy(trainerId, date, from, to);
            if (roomId) await this.schedulingService.validateRoomAvailability(roomId, date, from, to);

            const service = await this.ServiceModel.findOne({ _id: serviceCategory, organizationId, classType, "appointmentType._id": subType });

            if (!service) throw new BadRequestException("Invalid service selected.");
            const appointmentType = service.appointmentType.find((item: AppointmentTypeDocument) => item._id.toString() === subType);
            if (!appointmentType) throw new BadRequestException("Invalid service selected.");
            const appointmentDuration = appointmentType.durationInMinutes as number;
            const requestedSessions = duration / (appointmentDuration || 1);
            const remainder = duration % (appointmentDuration || 1);
            if (remainder !== 0) {
                throw new BadRequestException("Invalid duration.");
            };

            const newSchedule = new this.SchedulingModel({
                organizationId,
                scheduledBy: user._id,
                facilityId: facilityId,
                classType: classType,
                subTypeId: subType,
                trainerId: trainerId,
                serviceCategoryId: serviceCategory,
                roomId,
                dateRange: dateRange,
                date: new Date(date),
                from: from,
                to: to,
                duration: duration,
                sessions: requestedSessions,
                notes: notes,
                classCapacity: capacity,
            });

            await newSchedule.save({ session });

            await this.transactionService.commitTransaction(session);
            await this.sendClassScheduleCreationEmail(newSchedule, user);
            return newSchedule;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async updateClassScheduling(organizationId: any, user: any, body: ClassScheduleUpdateDto): Promise<any> {
        const { scheduleId, facilityId, trainerId, classType, subType, serviceCategory, duration, roomId, dateRange, date, from, to, notes, capacity } = body;
        const schedule = await this.SchedulingModel.findOne({ _id: scheduleId, organizationId }).exec();
        if (!schedule) throw new NotFoundException("Schedule not found");
        if (schedule.scheduleStatus === ScheduleStatusType.CANCELED) throw new BadRequestException("Scheduled class is already cancelled");

        const sessionStartDate = new Date(schedule.date).setHours(0, 0, 0, 0);
        const currentDate = new Date().setHours(0, 0, 0, 0);
        const timeString = new Date().toLocaleTimeString("en-GB", { hour: "2-digit", minute: "2-digit" });
        if (currentDate > sessionStartDate || (currentDate == sessionStartDate && timeString > schedule.to)) {
            throw new BadRequestException("You can only update it before or at the scheduled start time");
        }

        if (!this.isFromLessThanTo({ from: body.from, to: body.to })) throw new BadRequestException("Invalid time range.");
        if (new Date(`${date.toISOString().split("T")[0]}T${from}:00`) < new Date()) throw new BadRequestException("Class cannot start in the past.");
        const facility = await this.FacilityModel.findOne({ _id: facilityId, organizationId }).exec();
        await this.schedulingService.validateFacilityOwnership(facility, user);
        await this.schedulingService.validateFacilityAvailability(date, { from, to }, facilityId);
        await this.checkForExistingScheduleConflict(date, from, to, scheduleId);
        await this.schedulingService.validateStaffSpecialization(trainerId, serviceCategory, subType);
        await this.schedulingService.validateIsStaffBusy(trainerId, date, from, to, scheduleId);
        if (roomId) await this.schedulingService.validateRoomAvailability(roomId, date, from, to, scheduleId);

        const service = await this.ServiceModel.findOne({ _id: serviceCategory, organizationId, classType, "appointmentType._id": subType });
        if (!service) throw new BadRequestException("Invalid service selected.");
        const appointmentType = service.appointmentType.find((item: AppointmentTypeDocument) => item._id.toString() === subType);
        if (!appointmentType) throw new BadRequestException("Invalid service selected.");
        const appointmentDuration = appointmentType.durationInMinutes as number;
        const requestedSessions = duration / (appointmentDuration || 1);
        const remainder = duration % (appointmentDuration || 1);
        if (remainder !== 0) {
            throw new BadRequestException("Invalid duration.");
        };

        schedule.facilityId = facilityId;
        schedule.classType = classType;
        schedule.subTypeId = new Types.ObjectId(subType);
        schedule.trainerId = trainerId;
        schedule.serviceCategoryId = new Types.ObjectId(serviceCategory);
        schedule.roomId = new Types.ObjectId(roomId);
        schedule.roomId = new Types.ObjectId(roomId);
        schedule.roomId = new Types.ObjectId(roomId);
        schedule.dateRange = dateRange;
        schedule.date = new Date(date);
        schedule.from = from;
        schedule.to = to;
        schedule.duration = duration;
        schedule.sessions = requestedSessions;
        schedule.notes = notes;
        schedule.classCapacity = capacity;

        await schedule.save();

        return schedule;
    }

    async deleteSchedule(organizationId: IDatabaseObjectId, schedulingId: IDatabaseObjectId): Promise<any> {
        try {
            const schedule = await this.SchedulingModel.findOne({ _id: schedulingId });
            if (!schedule) throw new NotFoundException(`Schedule not found`);
            if (schedule.organizationId?.toString() !== organizationId.toString()) throw new NotFoundException(`Schedule does not belong to the current organization`);

            const sessionStartDateTime = moment(schedule.date).format("YYYY-MM-DD") + " " + schedule.from;
            const currentDateTime = moment();

            if (currentDateTime.isAfter(moment(sessionStartDateTime, "YYYY-MM-DD HH:mm"))) throw new BadRequestException("You can only delete the session before it starts");

            const isEnrolled = await this.EnrollmentModel.exists({ schedulingId });
            if (isEnrolled) throw new BadRequestException("Schedule cannot be deleted because clients are already enrolled.");

            await this.SchedulingModel.deleteOne({ _id: schedulingId, organizationId });

            return true
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async enrollScheduling(organizationId: IDatabaseObjectId, enrollSchedulingDto: ClassScheduleEnrollDto, isClient?: boolean): Promise<any> {
        const session = await this.transactionService.startTransaction();
        console.log("enrollSchedulingDto", organizationId);
        try {
            const { scheduleId, users } = enrollSchedulingDto;

            const [schedule, clients, existingEnrollments, totalEnrollments]: [Scheduling, (Omit<Clients, 'userId'> & { userId: UserDocument })[], Enrollment[], number] = await Promise.all([
                this.SchedulingModel.findOne({ _id: scheduleId, organizationId }).lean(),
                this.ClientsModel.find({ userId: { $in: users }, organizationId })
                    .populate<Omit<Clients, 'userId'> & { userId: UserDocument }>("userId")
                    .lean(),
                this.EnrollmentModel.find({ schedulingId: scheduleId, userId: { $in: users } }).lean(),
                this.EnrollmentModel.countDocuments({ schedulingId: scheduleId })
            ]);

            if (!schedule) throw new NotFoundException(`Scheduled class not found`);
            const { date, from, to, sessions: scheduleSession, scheduleStatus, classCapacity, serviceCategoryId, subTypeId } = schedule;

            const now = moment();
            const scheduleDate = date ? moment(date).format("DD/MM/YYYY") : "Unknown Date";
            const scheduleTime = from && to ? `Time: ${from} to ${to}` : "Unknown Time";
            const sessionEnd = date && to ? moment(`${moment(date).format("YYYY-MM-DD")} ${to}`, "YYYY-MM-DD HH:mm") : null;

            // Schedule validation
            if (scheduleStatus === ScheduleStatusType.CANCELED) {
                throw new BadRequestException(`${scheduleDate} ${scheduleTime} is canceled.`);
            }

            if (sessionEnd && now.isAfter(sessionEnd)) {
                throw new BadRequestException(`${scheduleDate} ${scheduleTime} has already ended.`);
            }

            if ((classCapacity - totalEnrollments) < clients.length) {
                throw new BadRequestException(`${scheduleDate} ${scheduleTime} does not have enough spots.${(classCapacity - totalEnrollments) > 0 ? `\nOnly ${(classCapacity - totalEnrollments)} spots are available` : ""}`);;
            }

            const errorMessages: string[] = [];
            const clientMap = new Map(clients.map((c) => [c.userId?._id.toString(), c]));
            const existingEnrollmentSet = new Set(existingEnrollments.map((e) => `${e.schedulingId}-${e.userId}`));
            const missingClients = users.filter((id) => !clientMap.has(id.toString()));
            if (missingClients.length) errorMessages.push(`Clients not found: ${missingClients.join(", ")}`);

            const purchasePipe: PipelineStage[] = [
                {
                    $match: {
                        userId: { $in: clients.map((client) => client.userId?._id) },
                        isExpired: false,
                        isActive: true,
                        startDate: { $lte: moment(`${date.toISOString().split("T")[0]}T${from}:00`).tz("Asia/Kolkata").toDate() },
                        endDate: { $gte: moment(`${date.toISOString().split("T")[0]}T${from}:00`).tz("Asia/Kolkata").toDate() },
                    }
                },
                {
                    $lookup: {
                        from: "pricings",
                        localField: "packageId",
                        foreignField: "_id",
                        as: "packageData",
                        pipeline: [
                            {
                                $match: {
                                    "services.type": ClassType.CLASSES,
                                    $or: [
                                        {
                                            $and: [
                                                { "services.serviceCategory": serviceCategoryId },
                                                { "services.appointmentType": subTypeId }
                                            ]
                                        },
                                        {
                                            $and: [
                                                { "services.relationShip.serviceCategory": serviceCategoryId },
                                                { "services.relationShip.subTypeIds": subTypeId }
                                            ]
                                        },
                                    ]
                                },
                            },
                        ],
                    },
                },
                {
                    $match: {
                        packageData: { $ne: [] }
                    }
                },
            ];

            const packageData = await this.PurchaseModel.aggregate<Purchase & { packageData: Pricing[] }>(purchasePipe);
            const packageMap: Map<string, any[]> = new Map();
            packageData.forEach((c: Purchase & { packageData: Pricing[] }) => {
                packageMap.set(c.userId.toString(), [c, ...packageMap.get(c.userId.toString()) ?? []]);
            });

            const promises: Promise<any>[] = [];
            for (const client of clients) {
                if (!client) continue;
                const clientName = client.userId?.name || "Unknown Client";
                const userId = client.userId._id;

                if (existingEnrollmentSet.has(`${schedule._id.toString()}-${userId.toString()}`)) {
                    errorMessages.push(`${clientName} is already enrolled in ${moment(schedule.date).format("DD/MM/YYYY")} (${schedule.from} - ${schedule.to}).`);
                    continue;
                }

                const purchaseData = packageMap.get(userId.toString());
                if (!purchaseData || !purchaseData?.length) {
                    errorMessages.push(isClient ? `You don't have an active package for this class` : `${clientName} has no active package for this class`);
                    continue;
                }
                let validPurchase: PurchaseDocument = null;
                let errorMessage = "";
                for (const purchase of purchaseData) {
                    try {
                        const pricing = purchase.packageData[0];
                        validPurchase = await this.schedulingService.validatePackageEligibility(
                            userId.toString(),
                            date,
                            from,
                            to,
                            pricing,
                            purchase,
                            1,
                        )
                    } catch (error) {
                        errorMessage = error.message;
                        continue;
                    }
                    break;
                }
                if (!validPurchase) {
                    errorMessages.push(!isClient ?
                        `${clientName} has no access to this course. ${errorMessage}`
                        : `You don't have access to this class.`
                    );
                    continue;
                };

                const enrollment = new this.EnrollmentModel({
                    schedulingId: schedule._id,
                    userId: client.userId?._id,
                    packageId: validPurchase.packageId,
                    purchaseId: validPurchase._id,
                });
                await enrollment.save({ session });
                await this.PurchaseModel.updateOne({ _id: validPurchase._id }, { $inc: { sessionConsumed: scheduleSession } }, { session });
            }

            if (errorMessages.length) throw new BadRequestException(errorMessages.join("\n"));
            // await Promise.all(promises);
            await session.commitTransaction();
            await this.sendClassEnrollmentConfirmationEmail(clients, schedule);

            return true;
        } catch (error) {
            await session.abortTransaction();
            throw new BadRequestException(error.message);
        } finally {
            session.endSession();
        }
    }

    async cancelEnrollScheduling(organizationId: IDatabaseObjectId, enrollSchedulingDto: ClassCancelEnrollDto): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const { scheduleId, users } = enrollSchedulingDto;

            const [schedule, enrollments]: [Scheduling, Enrollment[]] = await Promise.all([
                this.SchedulingModel.findOne({ _id: scheduleId, organizationId }).lean(),
                this.EnrollmentModel.find({ schedulingId: scheduleId, userId: { $in: users } }).lean(),
            ]);

            if (!schedule) throw new NotFoundException(`Scheduled class not found`);

            const promises: Promise<any>[] = [];
            for (const enrollment of enrollments) {
                await this.EnrollmentModel.deleteOne({ _id: enrollment._id }, { session });
                await this.PurchaseModel.updateOne({ _id: enrollment.purchaseId }, { $inc: { sessionConsumed: -1 * schedule.sessions } }, { session });
            }

            // await Promise.all(promises);
            await this.transactionService.commitTransaction(session);
            return true;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw new BadRequestException(error.message);
        } finally {
            session.endSession();
        }
    }

    async checkIn(organizationId: Types.ObjectId, checkInDto: CheckedInDto, userId?: Types.ObjectId): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const { enrollmentId, isCheckedIn } = checkInDto;
            const enrollment = await this.EnrollmentModel.findOne({ _id: enrollmentId });
            if (!enrollment) throw new BadRequestException("Enrollment not found.");

            if (isCheckedIn && enrollment.isCheckedIn) throw new BadRequestException(`${userId ? "User has" : "You have"} already checked in.`);
            else if (!isCheckedIn && !enrollment.isCheckedIn) throw new BadRequestException(`${userId ? "User has" : "You have"} not arrived.`);

            const schedule = await this.SchedulingModel.findOne({ _id: enrollment.schedulingId, organizationId });
            if (!schedule) throw new NotFoundException(`Schedule not found`);

            const purchase = await this.PurchaseModel.findOne({ _id: enrollment.purchaseId }).session(session);
            if (!purchase) throw new BadRequestException("Purchase not found.");

            if (schedule.scheduleStatus === ScheduleStatusType.CANCELED) throw new BadRequestException("Scheduled class is already cancelled");

            enrollment.isCheckedIn = isCheckedIn;
            enrollment.checkedInDate = isCheckedIn ? new Date() : null;
            await enrollment.save({ session });

            await session.commitTransaction();
            return true;
        } catch (error) {
            await session.abortTransaction();
            throw new BadRequestException(error.message);
        } finally {
            session.endSession();
        }
    }
    private async sendClassScheduleCreationEmail(schedule: any, scheduledBy: any): Promise<void> {
        const serviceData = await this.ServiceModel.findOne({ _id: schedule.serviceCategoryId }).lean();
        const subTypeData = serviceData?.appointmentType?.find((t: any) => t._id.toString() === schedule.subTypeId.toString());
        console.log(serviceData, "service Data")
        const trainerData = await this.UserModel.findOne({ _id: schedule.trainerId }).lean();
        const organizationDetail = await this.UserModel.findOne({ _id: schedule.organizationId }).lean();
        const staffList = await this.getALltheStaffs(new Types.ObjectId(schedule.facilityId));

        const context = {
            clientName: scheduledBy?.name || "Admin",
            className: serviceData?.name || "Class",
            date: schedule.date,
            from: schedule.from,
            to: schedule.to,
            trainerName: trainerData ? `${trainerData.firstName} ${trainerData.lastName}` : "Unknown Trainer",
        };

        // 📧 Trainer
        if (trainerData?.email) {
            await this.mailService.sendMail({
                to: trainerData.email.toString(),
                subject: `New Class Scheduled: ${context.className}`,
                template: 'class-schedule-created-notification',
                context,
            });
        }

        // 📧 Organization
        if (organizationDetail?.email) {
            await this.mailService.sendMail({
                to: organizationDetail.email.toString(),
                subject: `New Class Scheduled: ${context.className}`,
                template: 'class-schedule-created-notification',
                context,
            });
        }

        // 📧 Staff
        for (const staff of staffList) {
            if (staff.email) {
                await this.mailService.sendMail({
                    to: staff.email.toString(),
                    subject: `New Class Scheduled: ${context.className}`,
                    template: 'class-schedule-created-notification',
                    context,
                });
            }
        }

        console.log(`Class schedule notification emails sent.`);
    }

    private async getALltheStaffs(facilityId: Types.ObjectId): Promise<any[]> {
        const commonPipeline: any[] = [
            {
                $lookup: {
                    from: "staffprofiledetails",
                    localField: "_id",
                    foreignField: "userId",
                    as: "staffDetails",
                    pipeline: [{ $match: { facilityId: { $in: [facilityId] } } }],
                },
            },
            { $unwind: { path: "$staffDetails", preserveNullAndEmptyArrays: false } },
            {
                $lookup: {
                    from: 'role',
                    localField: "role",
                    foreignField: "_id",
                    as: "roleDetails",
                    pipeline: [
                        {
                            $match: {
                                type: { $in: [ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER] },
                            },
                        },
                    ],
                }
            },
            { $unwind: { path: "$roleDetails", preserveNullAndEmptyArrays: false } },
            {
                $lookup: {
                    from: "facilities",
                    localField: "staffDetails.facilityId",
                    foreignField: "_id",
                    as: "facilityDetails",
                },
            },
            { $unwind: { path: "$facilityDetails", preserveNullAndEmptyArrays: true } },
        ];
        commonPipeline.push({
            $group: {
                _id: "$staffDetails._id",
                gender: { $first: "$staffDetails.gender" },
                profilePicture: { $first: "$staffDetails.profilePicture" },
                userId: { $first: "$_id" },
                firstName: { $first: "$firstName" },
                lastName: { $first: "$lastName" },
                mobile: { $first: "$mobile" },
                email: { $first: "$email" },
                role: { $first: "$role" },
                isActive: { $first: "$isActive" },
                createdAt: { $first: "$createdAt" },
                facilityNames: { $push: "$facilityDetails.facilityName" },
            },
        });
        commonPipeline.push(
            {
                $sort: {
                    isActive: -1,
                    updatedAt: -1,
                },
            },
            {
                $facet: {
                    metadata: [{ $count: "total" }],
                    data: [
                        {
                            $project: {
                                _id: 1,
                                userId: 1,
                                firstName: 1,
                                lastName: 1,
                                facilityNames: 1,
                                mobile: 1,
                                email: 1,
                                role: 1,
                                isActive: 1,
                                createdAt: 1,
                                gender: 1,
                                profilePicture: 1,
                            },
                        },

                    ],
                },
            },
        );

        // Execute aggregation
        const result = await this.UserModel.aggregate(commonPipeline);
        return result[0]?.data || []

    }
    private async sendClassEnrollmentConfirmationEmail(
        clients: (Omit<Clients, 'userId'> & { userId: UserDocument })[],
        schedule: Scheduling
    ): Promise<void> {
        try {
            const service = await this.ServiceModel.findOne({ _id: schedule.serviceCategoryId }).lean();
            const subType = service?.appointmentType?.find(
                (item: any) => item._id.toString() === schedule.subTypeId?.toString()
            );

            const trainerData = await this.UserModel.findOne({ _id: schedule.trainerId }).lean();
            const organizationDetail = await this.UserModel.findOne({ _id: schedule.organizationId }).lean();
            const staffList = await this.getALltheStaffs(new Types.ObjectId(schedule.facilityId));

            for (const client of clients) {
                const context = {
                    clientName: client.userId?.name || "Client",
                    className: subType?.name || service?.name || "Class",
                    date: schedule.date,
                    from: schedule.from,
                    to: schedule.to,
                    trainerName: trainerData
                        ? `${trainerData.firstName} ${trainerData.lastName}`
                        : "Unknown Trainer",
                };

                if (client.userId?.email) {
                    await this.mailService.sendMail({
                        to: client.userId.email.toString(),
                        subject: `You're enrolled in ${context.className}`,
                        template: 'class-enrolled-confirmation',
                        context,
                    });
                }

                if (organizationDetail?.email) {
                    await this.mailService.sendMail({
                        to: organizationDetail.email.toString(),
                        subject: `New Enrollment in ${context.className}`,
                        template: 'class-enrollment-notification',
                        context,
                    });
                }

                for (const staff of staffList) {
                    console.log(staff.email, "staff email")
                    if (staff.email) {
                        await this.mailService.sendMail({
                            to: staff.email.toString(),
                            subject: `New Enrollment in ${context.className}`,
                            template: 'class-enrollment-notification',
                            context,
                        });
                    }
                }
            }
        } catch (error) {
            console.error('Failed to send enrollment emails:', error);
        }
    }

}

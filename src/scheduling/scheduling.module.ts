import { Module } from "@nestjs/common";
import { SchedulingController } from "./controllers/scheduling.controller";
import { ClassSchedulingController } from "./controllers/class.scheduling.controller";
import { SchedulingService } from "./services/scheduling.service";
import { UtilsModule } from "src/utils/utils.module";
import { MongooseModule } from "@nestjs/mongoose";
import { User, UserSchema } from "src/users/schemas/user.schema";
import { Facility, FacilitySchema } from "src/facility/schemas/facility.schema";
import { FacilityAvailability, FacilityAvailabilitySchema } from "src/facility/schemas/facility-availability.schema";
import { StaffProfileDetails, StaffSchema } from "src/staff/schemas/staff.schema";
import { AuthModule } from "src/auth/auth.module";
import { SchedulingPipe } from "./pipes/scheduling.pipe";
import { Scheduling, SchedulingSchema } from "./schemas/scheduling.schema";
import { Pricing, PricingSchema } from "src/organization/schemas/pricing.schema";
import { Services, ServiceSchema } from "src/organization/schemas/services.schema";
import { Purchase, PurchaseSchema } from "src/users/schemas/purchased-packages.schema";
import { Room, RoomSchema } from "src/room/schema/room.schema";
import { StaffAvailability, StaffAvailabilitySchema } from "src/staff/schemas/staff-availability";
import { MailModule } from "src/mail/mail.module";
import { PayRate, PayRateSchema } from "src/staff/schemas/pay-rate.schema";
import { Clients, ClientSchema } from "src/users/schemas/clients.schema";
import { WaitTimeModule } from "src/wait-time/wait-time.module";
import { DATABASE_PRIMARY_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { FacilityModule } from "src/facility/facility.module";
import { ClassesSchedulingService } from "./services/classes.scheduling.service";
import { Enrollment, EnrollmentSchema } from "src/courses/schemas/enrollment.schema";
import { ClassSchedulingPublicController } from "./controllers/class.scheduling.public.controller";
import { SchedulingPublicController } from "./controllers/scheduling.public.controller";

@Module({
    imports: [
        WaitTimeModule,
        UtilsModule,
        AuthModule,
        MailModule,
        FacilityModule,
        MongooseModule.forFeature([
            { name: User.name, schema: UserSchema },
            { name: Facility.name, schema: FacilitySchema },
            { name: FacilityAvailability.name, schema: FacilityAvailabilitySchema },
            { name: StaffAvailability.name, schema: StaffAvailabilitySchema },
            { name: StaffProfileDetails.name, schema: StaffSchema },
            { name: Services.name, schema: ServiceSchema },
            { name: Pricing.name, schema: PricingSchema },
            { name: Purchase.name, schema: PurchaseSchema },
            { name: Room.name, schema: RoomSchema },
            { name: PayRate.name, schema: PayRateSchema },
            { name: Clients.name, schema: ClientSchema },
            { name: Scheduling.name, schema: SchedulingSchema },
            { name: Enrollment.name, schema: EnrollmentSchema },
        ], DATABASE_PRIMARY_CONNECTION_NAME),
    ],
    controllers: [SchedulingController, SchedulingPublicController, ClassSchedulingController, ClassSchedulingPublicController],
    providers: [SchedulingService, ClassesSchedulingService, SchedulingPipe],
    exports: [SchedulingService, ClassesSchedulingService]
})
export class SchedulingModule { }

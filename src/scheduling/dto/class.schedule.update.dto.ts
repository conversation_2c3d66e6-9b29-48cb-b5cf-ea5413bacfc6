import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";
import { ClassScheduleCreateDto } from "./class.schedule.create.dto";

export class ClassScheduleUpdateDto extends ClassScheduleCreateDto {

    @ApiProperty({
        description: "Scheduling ID for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    scheduleId: string;
}
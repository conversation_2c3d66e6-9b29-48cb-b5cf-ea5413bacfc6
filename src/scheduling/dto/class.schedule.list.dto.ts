import { ApiProperty, ApiHideProperty, OmitType } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsDate, IsEmpty, IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString, Matches, Min } from "class-validator";
import { PaginationListDto } from "src/common/pagination/dtos/pagination.list.dto";
import { ClassType } from "src/utils/enums/class-type.enum";
import { ScheduleStatusType } from "../enums/schedule-status.enum";

export class ClassScheduleListDto extends OmitType(PaginationListDto, ['search'] as const) {
    @ApiProperty({
        description: "Facility ID for the course",
        type: String,
        required: false,
        example: "615c2f8e2c1ae9123cbb3c1b,615c2f8e2c1ae9123cbb3c1b",
    })
    @IsOptional()
    @IsMongoId({ message: "FAcility details is Invalid" })
    facilityId: string;

    @ApiProperty({
        description: "Trainer ID assigned for the course",
        type: String,
        required: false,
        example: "615c2f8e2c1ae9123cbb3c1b,615c2f8e2c1ae9123cbb3c1b",
    })
    @IsOptional()
    @IsMongoId({ message: "Trainer details is Invalid" })
    trainerId: string;

    @ApiProperty({
        description: "Room ids",
        type: String,
        required: false,
        example: "615c2f8e2c1ae9123cbb3c1b,615c2f8e2c1ae9123cbb3c1b",
    })
    @IsOptional()
    @IsMongoId({ message: "Room details is Invalid" })
    roomId?: string;

    @ApiProperty({
        description: "Start date from when the scheduling will get created",
        example: "2024-10-01",
        required: false,
    })
    @IsOptional()
    @Transform(({ value }) => (value ? new Date(new Date(value).setHours(0, 0, 0, 0)) : null), { toClassOnly: true })
    @IsDate({ message: "Required valid start date" })
    @Type(() => Date)
    from?: Date;

    @ApiProperty({
        description: "End date from when the scheduling can be done",
        example: "2024-10-01",
        required: false,
    })
    @IsOptional()
    @Transform(({ value }) => (value ? new Date(new Date(value).setHours(0, 0, 0, 0)) : null), { toClassOnly: true })
    @IsDate({ message: "Required valid end date" })
    @Type(() => Date)
    to?: Date;

    @ApiHideProperty()
    @IsOptional()
    @IsNotEmpty({ message: "Class type is not required" })
    classType?: ClassType = ClassType.CLASSES;

    @ApiProperty({
        description: "Filter by schedule status",
        type: String,
        example: ScheduleStatusType.BOOKED,
        required: false,
    })
    @IsEnum(ScheduleStatusType, { message: "Schedule status must be a valid enum value" })
    @IsOptional()
    status?: string = ScheduleStatusType.BOOKED;

}


export class ClassScheduleListPublicDto extends ClassScheduleListDto {

    @ApiProperty({
        description: "Organization ID for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    @IsMongoId({ message: "Organization details is Invalid" })
    organizationId: string;


}
import { BadRequestException, Body, Controller, Get, Param, Patch, Post, HttpCode, Headers, StreamableFile } from "@nestjs/common";
import { SchedulingService } from "../services/scheduling.service";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { CreateSchedulingDto } from "../dto/create-scheduling.dto";
import { UpdateSchedulingDto } from "../dto/update-scheduling.dto";
import { ClassType } from "src/utils/enums/class-type.enum";
import { CheckStaffAvailabilityDto } from "../dto/check-staff-availability.dto";
import { GetSchedulesDto } from "../dto/get-scheduling.dto";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { Response } from "src/common/response/decorators/response.decorator";
import { PolicyAbilityProtected, PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";
import { GetSchedulesByUserDto } from "../dto/get-scheduling-user.dto";
import { InstantCheckinDto } from "../dto/instant-checkin.dto"; // import at top
import { InstantSingleCheckinDto } from "../dto/instant-single-checkin.dto";

@ApiTags("module.scheduling")
@ApiBearerAuth()
@Controller("scheduling")
export class SchedulingController {
    constructor(private schedulingService: SchedulingService) { }

    @Response("scheduling.create.personalAppointment")
    // @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Schedule Personal appointment" })
    @Post("/personal-appointment")
    async schedulingPersonalAppointment(
        @GetUser() user: any,
        @Body() createSchedulingDto: CreateSchedulingDto
    ): Promise<any> {
        let classType = createSchedulingDto.classType;
        let data = null;
        if (classType === ClassType.PERSONAL_APPOINTMENT) {
            // Doing this
            data = await this.schedulingService.schedulePersonalAppointment(createSchedulingDto, user)
        } else {
            throw new BadRequestException("Invalid class type");
        }

        return {
            data: data,
        };
    }

    @Response('scheduling.create.session')
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Schedule session/booking" })
    @Post("/booking")
    async scheduling(
        @GetUser() user: any,
        @Body() createSchedulingDto: CreateSchedulingDto
    ): Promise<any> {
        let classType = createSchedulingDto.classType;
        let data = null
        if (classType === ClassType.BOOKINGS) {
            // Doing this
            data = await this.schedulingService.scheduleSession(createSchedulingDto, user)
        } else {
            throw new BadRequestException("Invalid class type");
        }

        return {
            data: data,
        };
    }

    @Response('scheduling.update.personalAppointment')
    @Patch("/personal-appointment/update")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Edit or update personal appointment" })
    async editSchedulingPersonalAppointment(
        @GetUser() user: any,
        @Body() updateSchedulingDto: UpdateSchedulingDto
    ): Promise<any> {
        let classType = updateSchedulingDto.classType;
        let data = null;
        if (classType === ClassType.PERSONAL_APPOINTMENT) {
            // Doing this
            data = await this.schedulingService.updatePersonalAppointment(updateSchedulingDto, user)
        } else {
            throw new BadRequestException("Invalid class type");
        }
        return {
            data: data,
        };
    }

    @Response('scheduling.update.session')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.SCHEDULING_UPDATE_BOOKING)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Edit or update session/booking" })
    @Patch("/booking/update")
    async editSchedulingBooking(
        @GetUser() user: any,
        @Body() updateSchedulingDto: UpdateSchedulingDto
    ): Promise<any> {
        let classType = updateSchedulingDto.classType;
        let data = null

        if (classType === ClassType.BOOKINGS) {
            data = await this.schedulingService.updateSession(updateSchedulingDto, user)
        } else {
            throw new BadRequestException("Invalid class type");
        }
        return {
            message: "Scheduled updated successfully",
            data: data,
        };
    }

    @Post("/get/list")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.SCHEDULING_SCHEDULE_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get scheduling list" })
    async getSchedulingList(
        @GetUser() user: IUserDocument,
        @Body() body: GetSchedulesDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const schedules = await this.schedulingService.getSchedulesList(user, organizationId, body);
        const totalPages = Math.ceil(schedules.count / body.pageSize);

        return {
            message: "Schedule fetched successfully",
            totalCount: schedules.count,
            totalPages: totalPages,
            page: body.page,
            data: schedules.data
        }
    }

    @Post("/get/list/v1")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.SCHEDULING_SCHEDULE_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get scheduling" })
    async getSchedulesListV1(@GetUser() user: any, @Body() body: GetSchedulesDto): Promise<any> {
        const schedules = await this.schedulingService.getSchedulesListV1(user, body);
        const totalPages = Math.ceil(schedules.count / body.pageSize);

        return {
            message: "Schedule fetched successfully",
            totalCount: schedules.count,
            totalPages: totalPages,
            page: body.page,
            data: schedules.data,
        };
    }

    @Get("/get/:scheduleId")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.SCHEDULING_SCHEDULE_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Edit or update scheduling" })
    async getSchedulingDetails(
        @GetUser() user: any,
        @Param("scheduleId") scheduleId: string,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const data = await this.schedulingService.getScheduleDetails(user, organizationId, scheduleId);
        return {
            message: "Schedule fetched successfully",
            data,
        };
    }

    @Patch("/cancel/:scheduleId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Edit or update scheduling" })
    async cancelSchedule(@GetUser() user: any, @Param("scheduleId") scheduleId: string): Promise<any> {
        const data = await this.schedulingService.cancelSchedule(user, scheduleId);
        return {
            message: "Schedule canceled successfully",
            data,
        };
    }

    @Patch("/checkIn/:scheduleId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Edit or update scheduling" })
    async checkInSchedule(@GetUser() user: any, @Param("scheduleId") scheduleId: string): Promise<any> {
        const data = await this.schedulingService.checkInSchedule(user, scheduleId);
        return {
            message: "Checked-in successfully",
            data,
        };
    }

    // can be used anywhere for checking if staff is available or not in given date range and time and day
    @Post("/staff-availability")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Available Locations for class scheduling" })
    async checkStaffAvailability(@GetUser() user, @Body() checkStaffAvailabilityDto: CheckStaffAvailabilityDto): Promise<any> {
        let role = user.role;
        let organizationId;
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id;
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                organizationId = await this.schedulingService.findOrganization(checkStaffAvailabilityDto);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        let classType = checkStaffAvailabilityDto.classType;
        let data;
        switch (classType) {
            case ClassType.CLASSES:
                data = await this.schedulingService.availableStaff(checkStaffAvailabilityDto, organizationId);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Class scheduled successfully",
            data: data,
        };
    }

    @Post("/get/list/user")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.USER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Edit or update scheduling" })
    async getSchedulesListByUser(@GetUser() user: any, @Body() body: GetSchedulesByUserDto): Promise<any> {
        const schedules = await this.schedulingService.getSchedulesListByUser(user, body);
        const totalPages = Math.ceil(schedules.count / body.pageSize);

        return {
            message: "Schedule fetched successfully",
            totalCount: schedules.count,
            totalPages: totalPages,
            page: body.page,
            data: schedules.data,
        };
    }


    @Post("/export/user")
    @HttpCode(200)
    @PolicyAbilityRoleProtected(
        ENUM_ROLE_TYPE.ORGANIZATION,
        ENUM_ROLE_TYPE.WEB_MASTER,
        ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
        ENUM_ROLE_TYPE.TRAINER,
        ENUM_ROLE_TYPE.USER
    )
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Export schedules by user" })
    async exportSchedulesListByUser(
        @GetUser() user: IUserDocument,
        @Body() body: GetSchedulesByUserDto,
        @Headers("X-Timezone") userTimezone: string
    ): Promise<any> {
        userTimezone = userTimezone || Intl.DateTimeFormat().resolvedOptions().timeZone;
        const schedules = await this.schedulingService.exportSchedulesByUser(user, body, userTimezone);

        if (body.responseType === 'stream') {
            const buffer = Buffer.from(schedules.data, 'utf-8');
            return new StreamableFile(new Uint8Array(buffer), {
                type: 'text/csv',
                disposition: 'attachment; filename=schedules.csv'
            });
        }

        return {
            message: "Schedule exported successfully",
            totalCount: schedules.count,
            data: schedules.data,
        };
    }

    @Post("/instant-booked&checkin")
    @HttpCode(200)
    @PolicyAbilityRoleProtected(
        ENUM_ROLE_TYPE.ORGANIZATION,
        ENUM_ROLE_TYPE.WEB_MASTER,
        ENUM_ROLE_TYPE.FRONT_DESK_ADMIN
    )
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Instant check-in after purchases" })
    async instantCheckin(@GetUser() user: IUserDocument, @Body() body: InstantCheckinDto): Promise<any> {
        const allowedRoles = [ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN];
        if (!allowedRoles.includes(user.role.type)) {
            throw new BadRequestException("Access denied");
        }
        const data = await this.schedulingService.instantBookedAndCheckin(user, body);
        return {
            message: "Instant check-in successful",
            data: data,
        };
    }

    @Post("/instant-single-checkin")
    @HttpCode(200)
    @PolicyAbilityRoleProtected(
        ENUM_ROLE_TYPE.ORGANIZATION,
        ENUM_ROLE_TYPE.WEB_MASTER,
        ENUM_ROLE_TYPE.FRONT_DESK_ADMIN
    )
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Instant check-in for a single user by package/invoice" })
    async instantSingleCheckin(
        @GetUser() user: IUserDocument,
        @Body() body: InstantSingleCheckinDto
    ): Promise<any> {
        const allowedRoles = [ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN];
        if (!allowedRoles.includes(user.role.type)) {
            throw new BadRequestException("Access denied");
        }
        const data = await this.schedulingService.instantSingleBookedAndCheckin(user, body);
        return {
            message: "Instant single check-in successful",
            data: data,
        };
    }


}

import { BadRequestException, Body, Controller, Get, Param, Patch, Post, HttpCode, Headers, StreamableFile } from "@nestjs/common";
import { SchedulingService } from "../services/scheduling.service";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { CreateSchedulingDto } from "../dto/create-scheduling.dto";
import { UpdateSchedulingDto } from "../dto/update-scheduling.dto";
import { ClassType } from "src/utils/enums/class-type.enum";
import { CheckStaffAvailabilityDto } from "../dto/check-staff-availability.dto";
import { GetSchedulesDto } from "../dto/get-scheduling.dto";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { Response } from "src/common/response/decorators/response.decorator";
import { PolicyAbilityProtected, PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";
import { GetSchedulesByUserDto } from "../dto/get-scheduling-user.dto";

@ApiTags("module.scheduling")
@ApiBearerAuth()
@Controller("public/scheduling")
export class SchedulingPublicController {
    constructor(private schedulingService: SchedulingService) { }

    @Response("scheduling.create.personalAppointment")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.USER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Schedule Personal appointment" })
    @Post("/")
    async schedulingPersonalAppointment(
        @GetUser() user: any,
        @Body() createSchedulingDto: CreateSchedulingDto
    ): Promise<any> {
        let classType = createSchedulingDto.classType;
        let data = null;
        if (classType === ClassType.PERSONAL_APPOINTMENT) {
            // Doing this
            data = await this.schedulingService.schedulePersonalAppointment(createSchedulingDto, user)
        } else if (classType === ClassType.BOOKINGS) {
            // Doing this
            data = await this.schedulingService.scheduleSession(createSchedulingDto, user)
        } else {
            throw new BadRequestException("Invalid class type");
        }

        return {
            data: data,
        };
    }


    @Response('scheduling.update.personalAppointment')
    @Patch("/update")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.USER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Edit or update personal appointment" })
    async editSchedulingPersonalAppointment(
        @GetUser() user: any,
        @Body() updateSchedulingDto: UpdateSchedulingDto
    ): Promise<any> {
        let classType = updateSchedulingDto.classType;
        let data = null;
        if (classType === ClassType.PERSONAL_APPOINTMENT) {
            // Doing this
            data = await this.schedulingService.updatePersonalAppointment(updateSchedulingDto, user)
        } else if (classType === ClassType.BOOKINGS) {
            data = await this.schedulingService.updateSession(updateSchedulingDto, user)
        } else {
            throw new BadRequestException("Invalid class type");
        }
        return {
            data: data,
        };
    }

    @Post("/get/list")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.USER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get scheduling list" })
    async getSchedulingList(
        @GetUser() user: IUserDocument,
        @Body() body: GetSchedulesDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const schedules = await this.schedulingService.getSchedulesList(user, organizationId, body);
        const totalPages = Math.ceil(schedules.count / body.pageSize);

        return {
            message: "Schedule fetched successfully",
            totalCount: schedules.count,
            totalPages: totalPages,
            page: body.page,
            data: schedules.data
        }
    }
}

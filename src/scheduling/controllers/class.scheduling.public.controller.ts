import { BadRequestException, Body, Controller, Get, NotFoundException, Param, Patch, Post, Query, UseGuards } from "@nestjs/common";
import { Model, Types } from "mongoose";
import { ClassesSchedulingService } from "../services/classes.scheduling.service";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { ClassScheduleListPublicDto } from "../dto/class.schedule.list.dto";
import { ENUM_PAGINATION_ORDER_DIRECTION_TYPE, PaginationQuery } from "src/utils/decorators/pagination-query.decorator";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { SchedulingService } from "../services/scheduling.service";
import { ClassCancelEnrollPublicDto, ClassScheduleEnrollPublicDto } from "../dto/class.schedule.enroll.dto";
import { ScheduleStatusType } from "../enums/schedule-status.enum";
import { CheckedInPublicDto } from "src/courses/dto/courses.dto";
import { Enrollment } from "src/courses/schemas/enrollment.schema";
import { InjectModel } from "@nestjs/mongoose";
import { Response, ResponsePaging } from "src/common/response/decorators/response.decorator";
import { PaginationListDto } from "src/common/pagination/dtos/pagination.list.dto";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { MongoIdPipeTransform } from "src/common/database/pipes/mongo-id.pipe";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";

@ApiTags("modules.scheduling.class")
@ApiBearerAuth()
@Controller("public/scheduling/classes")
export class ClassSchedulingPublicController {
    constructor(
        private classesSchedulingService: ClassesSchedulingService,
        private readonly schedulingService: SchedulingService,
        private readonly paginationService: PaginationService,
        @InjectModel(Enrollment.name) private enrollmentModel: Model<Enrollment>
    ) { }

    @ApiOperation({ summary: "Class list" })
    @ResponsePaging('scheduling.list')
    @Get("/list")
    async getClassList(
        @PaginationQuery({
            defaultOrderBy: 'date',
            defaultOrderDirection: ENUM_PAGINATION_ORDER_DIRECTION_TYPE.DESC,
            availableOrderBy: ['date']
        }) { _limit, _offset, _order }: PaginationListDto,
        @Query() ClassListDto: ClassScheduleListPublicDto,

    ): Promise<any> {
        const { facilityId, trainerId, from, to, roomId, classType, page, organizationId } = ClassListDto;

        const find: Record<string, any> = {
            classType,
            organizationId,
            scheduleStatus: { $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN] },
        };

        if (facilityId) {
            find["facilityId"] = {
                $in: facilityId.split(',').map((id) => new Types.ObjectId(id))
            }
        };

        if (trainerId) {
            find["trainerId"] = {
                $in: [trainerId.split(',').map((id) => new Types.ObjectId(id))]
            };
        }

        if (from && to) {
            find["date"] = {
                $gte: new Date(new Date(from).setHours(0, 0, 0, 0)),
                $lte: new Date(new Date(to).setHours(23, 59, 59, 999)),
            };
        } else if (from) {
            find["date"] = {
                $gte: new Date(new Date(from).setHours(0, 0, 0, 0)),
            };
        } else if (to) {
            find["date"] = {
                $lte: new Date(new Date(to).setHours(23, 59, 59, 999)),
            };
        }

        if (roomId) {
            find["roomId"] = roomId.split(',').map((id) => new Types.ObjectId(id));
        }

        const { data, count } = await this.classesSchedulingService.getClassesList(find, {
            paging: {
                limit: _limit,
                offset: _offset,
            },
            order: _order,
        }, true);
        return {
            _pagination: {
                total: count,
                totalPage: this.paginationService.totalPage(count, _limit),
                page,
                pageSize: _limit,
            },
            data: data
        };
    }

    @ApiOperation({ summary: "Scheduling Details" })
    @Response('scheduling.get')
    @Get("/:scheduleId/get/:userId?")
    async getSchedulingDetails(
        @Param("scheduleId", MongoIdPipeTransform) scheduleId: IDatabaseObjectId,
        @Param("userId", MongoIdPipeTransform) userId: IDatabaseObjectId
    ): Promise<any> {

        const data = await this.classesSchedulingService.getScheduleDetails(scheduleId, null, userId);
        return {
            message: "Schedule fetched successfully",
            data,
        };
    }

    @ApiOperation({ summary: "Enroll Schedule" })
    @Response('scheduling.enroll')
    @Post("/enroll")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.USER)
    @AuthJwtAccessProtected()
    async enrollScheduling(
        @Body() body: ClassScheduleEnrollPublicDto,
        @GetUser() user: any,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const output = await this.classesSchedulingService.enrollScheduling(organizationId, {
            ...body,
            users: [user._id],
        }, true);
        return {
            message: "Enrolled successfully",
            data: output,
        };
    }

    @ApiOperation({ summary: "Cancel Enroll Schedule" })
    @Response('scheduling.cancel.enroll')
    @Post("/enroll/cancel")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.USER)
    @AuthJwtAccessProtected()
    async cancelEnrollScheduling(
        @Body() body: ClassCancelEnrollPublicDto,
        @GetUser() user: any,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const output = await this.classesSchedulingService.cancelEnrollScheduling(organizationId, {
            ...body,
            users: [user._id],
        });
        return {
            message: "Enrollment canceled successfully",
            data: output,
        };
    }

    @ApiOperation({ summary: "Check In" })
    @Response('scheduling.checkin')
    @Patch("/checkin")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.USER)
    @AuthJwtAccessProtected()
    async checkIn(
        @GetUser() user: any,
        @Body() body: CheckedInPublicDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const enrollment = await this.enrollmentModel.findOne({ schedulingId: body.schedulingId, userId: user._id });
        if (!enrollment) throw new NotFoundException("Enrollment not found.");
        const output = await this.classesSchedulingService.checkIn(organizationId, {
            enrollmentId: enrollment._id,
            isCheckedIn: true,
        });
        return {
            message: "Checked-in successfully",
            data: output,
        };
    }
}

import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from "@nestjs/common";
import { CourseService } from "../services/classes.service";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { Roles } from "src/auth/decorators/roles.decorator";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { CheckedInDto, CourseListDto, CustomerListDto, CustomerListForSchedulingDto, EnrollSchedulingDto, UpdateCourseStatusDto } from "src/courses/dto/courses.dto";
import { CreateSchedulingDataDto, SchedulingListDto, UpdateSchedulingDataDto } from "src/courses/dto/scheduling.dto";

@ApiTags("course")
@ApiBearerAuth()
@Controller("classes")
export class CourseController {
    constructor(private courseService: CourseService) { }
    @Post("/list")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Course list" })
    async getCourseList(@GetUser() user: any, @Body() courseListDto: CourseListDto): Promise<any> {
        const output = await this.courseService.getCourseList(user, courseListDto);
        return output;
    }

    @Get("/details/:courseId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Course Details" })
    async getCourseDetail(@GetUser() user: any, @Param("courseId") courseId: string): Promise<any> {
        let output = await this.courseService.getCourseDetail(courseId);
        return output;
    }

    @Post("/customer-list")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Customer list" })
    async getCustomerList(@GetUser() user: any, @Body() customerListDto: CustomerListDto): Promise<any> {
        const output = await this.courseService.getCustomerList(user, customerListDto);
        return output;
    }

    @Post("/scheduling-customer-list")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Customer list" })
    async getCustomerListForScheduling(@GetUser() user: any, @Body() customerListDto: CustomerListForSchedulingDto): Promise<any> {
        const output = await this.courseService.getCustomerListForScheduling(user, customerListDto);
        return output;
    }

    @Post("/scheduling-list")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Customer list" })
    async getSchedulingList(@GetUser() user: any, @Body() schedulingListDto: SchedulingListDto): Promise<any> {
        const output = await this.courseService.getSchedulingList(user, schedulingListDto);
        return output;
    }

    @Patch("/update-status")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Update  status" })
    async updateCourseStatus(@GetUser() user: any, @Body() updateCourseStatusDto: UpdateCourseStatusDto): Promise<any> {
        const output = await this.courseService.updateCourseStatus(updateCourseStatusDto, user);
        return output;
    }

    @Post("/create-schedule")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Create Course Schedule" })
    async createCourseScheduling(@GetUser() user: any, @Body() createSchedulingDto: CreateSchedulingDataDto): Promise<any> {
        const output = await this.courseService.createCourseScheduling(createSchedulingDto, user);
        return output;
    }

    @Patch("/update-schedule")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Update Course Schedule" })
    async updateCourseScheduling(@GetUser() user: any, @Body() updateSchedulingDto: UpdateSchedulingDataDto): Promise<any> {
        const output = await this.courseService.updateCourseScheduling(updateSchedulingDto, user);
        return output;
    }

    @Get("/scheduling-details/:schedulingId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Scheduling Details" })
    async getSchedulingDetails(@GetUser() user: any, @Param("schedulingId") schedulingId: string): Promise<any> {
        let output = await this.courseService.getSchedulingDetails(user, schedulingId);
        return output;
    }

    @Delete("/delete-schedule/:schedulingId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Delete Scheduling" })
    async deleteSchedule(@GetUser() user: any, @Param("schedulingId") schedulingId: string): Promise<any> {
        let output = await this.courseService.deleteSchedule(user, schedulingId);
        return output;
    }

    @Patch("/cancel-schedule/:schedulingId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Scheduling Details" })
    async cancelSchedule(@GetUser() user: any, @Param("schedulingId") schedulingId: string): Promise<any> {
        let output = await this.courseService.cancelSchedule(user, schedulingId);
        return output;
    }

    @Post("/enroll-schedule")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Enroll Schedule" })
    async enrollScheduling(@GetUser() user: any, @Body() enrollSchedulingDto: EnrollSchedulingDto): Promise<any> {
        const output = await this.courseService.enrollScheduling(enrollSchedulingDto, user);
        return output;
    }

    @Post("/check-in")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Check In" })
    async checkIn(@GetUser() user: any, @Body() checkedInDto: CheckedInDto): Promise<any> {
        const output = await this.courseService.checkIn(user, checkedInDto);
        return output;
    }

    @Delete("/delete-enrollment/:enrollmentId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Delete Enrollment" })
    async deleteEnrollment(@GetUser() user: any, @Param("enrollmentId") enrollmentId: string): Promise<any> {
        let output = await this.courseService.deleteEnrollment(user, enrollmentId);
        return output;
    }
}

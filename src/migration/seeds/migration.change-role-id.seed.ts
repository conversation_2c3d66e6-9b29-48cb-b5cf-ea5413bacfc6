import { Command } from 'nestjs-command';
import { Injectable } from '@nestjs/common';
import { Types } from 'mongoose';
import {
    ENUM_ROLE_TYPE,
} from 'src/role/enums/role.enum';
import { RoleService } from 'src/role/services/role.service';
import { RoleCreateRequestDto } from 'src/role/dtos/request/role.create.request.dto';
import { UserService } from 'src/users/services/user.service';
import { RoleDocument } from 'src/role/repository/entities/role.entity';

@Injectable()
export class MigrationChangeRoleId {
    constructor(
        private readonly roleService: RoleService,
        private readonly userService: UserService
    ) {}

    @Command({
        command: 'user:role',
        describe: "Change user's role id",
    })
    async seeds(): Promise<void> {
        try {
            const roles = await this.roleService.findAll({}, { select: '_id type' });

            const map = new Map<string, Types.ObjectId>();
            roles.forEach((role) => {
                map.set(role.type, role._id);
            });

            // await Promise.all(promise);
            const users = await (await this.userService.model()).collection.find({
                role: { $type: 'string' }
            }).toArray();

        
            // Step 3: Update each user's role to corresponding ObjectId
            for(const user of users){
                const roleId = map.get(user.role);
                if (!roleId) {
                    console.warn(`No matching role found for user ${user._id} with role type ${user.role}`);
                    return;
                }
                await this.userService.updateOne(
                    { _id: user._id },
                    { role: roleId }
                );
            };

        } catch (err: any) {
            throw new Error(err);
        }

        return;
    }

    @Command({
        command: 'remove:role',
        describe: 'remove roles',
    })
    async remove(): Promise<void> {
        try {
            await this.roleService.deleteMany({});
        } catch (err: any) {
            throw new Error(err);
        }

        return;
    }
}

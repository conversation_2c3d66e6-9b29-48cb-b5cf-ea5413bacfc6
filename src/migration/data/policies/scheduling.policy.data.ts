import { <PERSON><PERSON><PERSON>_POLICY_ACTION, ENUM_POLICY_MODULE, ENUM_POLICY_SUBJECT_NAME, ENUM_POLICY_TYPE } from 'src/policy/enums/policy.enum';
import { IDefaultPolicies } from 'src/policy/interfaces/policy.interface';

export const schedulingPolicies: IDefaultPolicies[] = [

    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SCHEDULING_BOOKING],
        type: ENUM_POLICY_TYPE.SCHEDULING_BOOKING,
        description: 'Grant view-only access to bookings',
        isActive: true,
        module: ENUM_POLICY_MODULE.SCHEDULING,
        action: ENUM_POLICY_ACTION.VIEW,
    },
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SCHEDULING_BOOKING],
        type: <PERSON>NU<PERSON>_POLICY_TYPE.SCHEDULING_BOOKING,
        description: 'Grant full access to bookings',
        isActive: true,
        module: E<PERSON><PERSON>_POLICY_MODULE.SCHEDULING,
        action: ENU<PERSON>_POLICY_ACTION.FULL_ACCESS,
    },
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SCHEDULING_AVAILABILITY],
        type: ENUM_POLICY_TYPE.SCHEDULING_AVAILABILITY,
        description: 'Grant view-only access to bookings',
        isActive: true,
        module: ENUM_POLICY_MODULE.SCHEDULING,
        action: ENUM_POLICY_ACTION.VIEW,
    },
    {
        subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SCHEDULING_AVAILABILITY],
        type: ENUM_POLICY_TYPE.SCHEDULING_AVAILABILITY,
        description: 'Grant full access to bookings',
        isActive: true,
        module: ENUM_POLICY_MODULE.SCHEDULING,
        action: ENUM_POLICY_ACTION.FULL_ACCESS,
    },
];
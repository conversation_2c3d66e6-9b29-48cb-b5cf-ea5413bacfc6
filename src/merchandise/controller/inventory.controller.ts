import { BadRequestException, Body, Controller, UseGuards, Get, HttpCode, Param, Patch, Post } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { Roles } from "src/auth/decorators/roles.decorator";
import { InventoryProductFilterDto } from "../dto/inventoryFilterProduct.dto";
import { InventoryService } from "../services/inventory.service";
import { ProductType } from "src/merchandise/schema/product.schema";
import { CreateInventoryDto } from "../dto/createInventory.dto";
import { FilterInventoryDto } from "../dto/filterInventory.dto";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
@ApiTags("inventory")
@ApiBearerAuth()
@Controller("inventory")
export class InventoryController {
    constructor(private inventoryService: InventoryService) { }

    @Post("/product/sku_search")
    @ApiOperation({ summary: "search Sku of the Product" })
    @UseGuards(AuthGuard(), RolesGuard) // Ensure only authenticated and authorized users can access
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER)
    async getProductBySku(@Body() FilterProductDto: InventoryProductFilterDto, @GetUser() user: any): Promise<{ message: string; data: any }> {
        let result;
        let productType = FilterProductDto.productType;
        switch (productType) {
            case ProductType.SIMPLE:
                result = await this.inventoryService.getSimpleProducts(FilterProductDto, user);
                break;
            case ProductType.VARIABLE:
                result = await this.inventoryService.getVariableProducts(FilterProductDto, user);
                break;
            default:
                throw new BadRequestException("Invalid product type");
        }
        return { message: "Product details fetched successfully", data: result };
    }

    @Post("/create")
    @ApiOperation({ summary: "Create a new Product" })
    @UseGuards(AuthGuard(), RolesGuard) // Ensure only authenticated and authorized users can access
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER)
    async createInventory(@Body() createInventoryDto: CreateInventoryDto, @GetUser() user: any): Promise<{ message: string; data: any }> {
        let result;
        result = await this.inventoryService.createInventory(createInventoryDto, user);
        return { message: "Product details fetched successfully", data: result };

    }
    
    @Post("/")
    @ApiOperation({ summary: "List of Inventory" })
    @UseGuards(AuthGuard(), RolesGuard) // Ensure only authenticated and authorized users can access
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER)
    async getInventoryList(@Body() FilterInventoryDto: FilterInventoryDto, @GetUser() user: any): Promise<{ message: string; data: any }> {
        return { message: "Inventory list fetched successfully", data: await this.inventoryService.getInventoryList(FilterInventoryDto, user) };
    }

    @Patch("/:id")
    @ApiOperation({ summary: "Update Inventory Details" })
    @UseGuards(AuthGuard(), RolesGuard) // Ensure only authenticated and authorized users can access
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER)
    async updateInventoryDetails(@Param("id") id: string, @Body() CreateInventoryDto: CreateInventoryDto): Promise<{ message: string; data: any }> {
        return { message: "Inventory details updated successfully", data: await this.inventoryService.updateInventoryDetails(id, CreateInventoryDto) };
    }

    @Post("/store-inventory")
    @ApiOperation({ summary: "List of Inventory on the basis of Store" })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async storeInventoryList(
        @Body() body: any,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ) {
        const result = await this.inventoryService.organizationInventoryList(body, organizationId);
        return {
            message: "Inventory of the Store  Fetched successfully!",
            data: result,
        };
    }
}

import { Injectable, BadRequestException, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { ProductVariant } from "src/merchandise/schema/product-variant.schema";
import { Product, ProductType } from "src/merchandise/schema/product.schema";
import { InventoryProductFilterDto } from "../dto/inventoryFilterProduct.dto";
import { TransactionService } from "src/utils/services/transaction.service";
import { Facility } from "src/facility/schemas/facility.schema";
import { Inventory } from "../schema/inventory.schema";
import { CreateInventoryDto } from "../dto/createInventory.dto";
import { FilterInventoryDto } from "../dto/filterInventory.dto";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { Clients } from "src/users/schemas/clients.schema";
import { ID } from "aws-sdk/clients/s3";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { PromotionService } from "src/promotions/services/promotion.service";
import { DiscountType } from "src/utils/enums/discount.enum";
import { ENUM_ITEM_TYPE } from "src/promotions/enums/item-type.enum";
import { PromotionItemService } from "src/promotions/services/promotion-items.service";

const mongoose = require("mongoose");
@Injectable()
export class InventoryService {
    constructor(
        @InjectModel(Product.name) private readonly productModel: Model<Product>,
        @InjectModel(ProductVariant.name) private readonly productVariantModel: Model<ProductVariant>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(Inventory.name) private inventoryModel: Model<Inventory>,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        @InjectModel(Clients.name) private ClientsModel: Model<Clients>,

        private readonly promotionItemService: PromotionItemService,
        private readonly promotionService: PromotionService,
        private readonly transactionService: TransactionService,
    ) { }

    async getSimpleProducts(filterProductDto: InventoryProductFilterDto, user) {
        let organizationId = await this.getOrganizationId(user)
        const pageSize = filterProductDto.pageSize ?? 30;
        const page = filterProductDto.page ?? 1;
        const skip = pageSize * (page - 1);

        let query: any = { status: true };
        query["organizationId"] = organizationId
        if (filterProductDto.productType) {
            query["type"] = filterProductDto.productType;
        }

        if (filterProductDto.search?.trim()) {
            const titleQueryString = filterProductDto.search.trim().split(" ").join("|");
            query["$or"] = [
                { sku: { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                { name: { $regex: `.*${titleQueryString}.*`, $options: "i" } } // Added name field for search
            ];
        }

        const data = await this.productModel.aggregate([
            { $match: query },
            { $skip: skip },
            { $limit: pageSize },
            {
                $project: {
                    name: 1,
                    sku: 1,
                    hsnCode: 1,
                    gst: 1,
                },
            },
        ]);

        return data;
    }

    async getVariableProducts(filterProductDto: InventoryProductFilterDto, user) {
        const organizationId = await this.getOrganizationId(user)
        const pageSize = filterProductDto.pageSize ?? 30;
        const page = filterProductDto.page ?? 1;
        const skip = pageSize * (page - 1);
        let query: any = { status: true };
        if (filterProductDto.search) {
            const titleQueryString = filterProductDto.search.trim().split(" ").join("|");
            query["$or"] = [{ sku: { $regex: `.*${titleQueryString}.*`, $options: "i" } }];
        }
        query["organizationId"] = organizationId
        // let data = await this.productVariantModel.find(query, { name: "$title", sku: 1, hsnCode: 1 }).skip(skip).limit(page).populate("productId", "id name gst").exec();
        let data = await this.productVariantModel.aggregate([
            {
                $match: query,
            },
            {
                $skip: skip,
            },
            {
                $limit: pageSize,
            },
            {
                $lookup: {
                    from: "products",
                    localField: "productId",
                    foreignField: "_id",
                    as: "productData",
                },
            },
            {
                $unwind: {
                    path: "$productData",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $project: {
                    name: "$title",
                    sku: 1,
                    hsnCode: 1,
                    gst: "$productData.gst",
                    productId: 1,
                    type: "$productData.type",
                },
            },
        ]);
        return data;
    }
    async createInventory(createInventoryDto: CreateInventoryDto, user: any) {
        const session = await this.transactionService.startTransaction();
        try {
            let organizationId = await this.getOrganizationId(user)
            const { storeId, promotionId, applyPromotion, ...inventoryDetails } = createInventoryDto;
            const store = await this.FacilityModel.findById(storeId).exec();
            let isProductExist
            if (!store) {
                throw new NotFoundException("Store not found");
            }
            if (inventoryDetails.productType === 'variable') {
                isProductExist = await this.inventoryModel.findOne({ storeId: storeId, organizationId: organizationId, productVariantId: inventoryDetails?.productVariantId })
            }
            if (inventoryDetails.productType === 'simple') {
                isProductExist = await this.inventoryModel.findOne({ storeId: storeId, organizationId: organizationId, productId: inventoryDetails?.productId })
            } if (isProductExist) {
                throw new NotFoundException("Product Already Exist in store Inventory");

            }
            if (inventoryDetails.productType === ProductType.SIMPLE) {
                const inventory = await this.inventoryModel.findOne({ productId: inventoryDetails?.productId, productType: ProductType.SIMPLE });

                const existProd = await this.productModel.findOne({ _id: inventoryDetails?.productId, type: ProductType.SIMPLE });
                if (!existProd) {
                    throw new BadRequestException("Invalid product id");
                }
                if (!inventory) {
                    await this.productModel.findByIdAndUpdate(inventoryDetails?.productId, { status: true }, { new: true, session });
                }
                delete inventoryDetails["productVariantId"];
            }

            if (inventoryDetails.productType === ProductType.VARIABLE) {
                const inventory = await this.inventoryModel.findOne({
                    productId: inventoryDetails.productId,
                    productVariantId: inventoryDetails.productVariantId,
                    productType: ProductType.VARIABLE,
                });

                const variantExist = await this.productVariantModel.findOne({ _id: inventoryDetails.productVariantId, productId: inventoryDetails?.productId });
                if (!variantExist) {
                    throw new BadRequestException("Invalid product and product variant id");
                }
                if (!inventory) {
                    const proInv = await this.productModel.findByIdAndUpdate(inventoryDetails?.productId, { status: true }, { new: true, session });
                    const proVarInv = await this.productVariantModel.findByIdAndUpdate(inventoryDetails.productVariantId, { status: true }, { new: true, session });
                }
            }

            if (promotionId) {
                const promotion = await this.promotionService.findOneById(new Types.ObjectId(promotionId));
                await this.promotionService.validateItemDiscount(promotion, inventoryDetails.mrp);
            }

            let data: Inventory = {
                ...inventoryDetails,
                promotion: promotionId ? new Types.ObjectId(promotionId) : null,
                storeId: store._id,
                organizationId
            };
            const saveData = await new this.inventoryModel(data)

            if (applyPromotion?.length > 0) {
                // Validate all promotions in bulk
                const promotionIds = applyPromotion.map(id => new Types.ObjectId(id));
                await Promise.all(promotionIds.map(promotionId =>
                    this.validateDiscount(promotionId, new Types.ObjectId(organizationId), inventoryDetails.mrp)
                ));
                await this.promotionService.applyPromotionsToItem(
                    new Types.ObjectId(organizationId),
                    {
                        applyPromotions: applyPromotion,
                    },
                    {
                        itemType: ENUM_ITEM_TYPE.SERVICE,
                        _id: new Types.ObjectId(saveData._id), // Use the newly created pricing's ID,
                        price: saveData.mrp as number
                    },
                    { session }
                );
            }


            await saveData.save({ session });
            await this.transactionService.commitTransaction(session);
            return saveData;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async getInventoryList(filterInventoryDto: FilterInventoryDto, user: any) {
        let organizationId = await this.getOrganizationId(user)
        const { search, page = 1, pageSize = 10, storeId, productType } = filterInventoryDto;
        const skip = pageSize * (page - 1);

        let query: any = { organizationId };

        if (storeId && mongoose.Types.ObjectId.isValid(storeId)) {
            query.storeId = new mongoose.Types.ObjectId(storeId);
        }
        if (productType) {
            query.productType = productType;
        }
        // Creating a search query string with regex pattern
        let searchQuery: any = {};
        if (search) {
            const titleQueryString = search.trim().split(" ").join("|");
            searchQuery = {
                $or: [
                    { "productDetails.name": { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                    { "productDetails.sku": { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                    { "productVariantDetails.name": { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                    { "productVariantDetails.sku": { $regex: `.*${titleQueryString}.*`, $options: "i" } },

                ]
            };
        }

        let countProm = this.inventoryModel.countDocuments({ ...query, ...searchQuery });

        let dataProm = this.inventoryModel
            .aggregate([
                { $match: query },
                {
                    $lookup: {
                        from: "products",
                        localField: "productId",
                        foreignField: "_id",
                        as: "productDetails",
                        pipeline: [
                            { $project: { name: 1, sku: 1, gst: 1, hsn: 1 } }
                        ],
                    },
                },
                {
                    $lookup: {
                        from: "productvariants",
                        localField: "productVariantId",
                        foreignField: "_id",
                        as: "productVariantDetails",
                        pipeline: [
                            { $project: { name: "$title", sku: 1, gst: 1, hsnCode: 1 } }
                        ],
                    },
                },
                { $unwind: { path: "$productDetails", preserveNullAndEmptyArrays: true } },
                { $unwind: { path: "$productVariantDetails", preserveNullAndEmptyArrays: true } },
                { $match: searchQuery },
                { $sort: { createdAt: -1 } },
                { $skip: skip },
                { $limit: pageSize }
            ]);

        let [count, list] = await Promise.all([countProm, dataProm.exec()]);
        return { count, list };
    }
    async updateInventoryDetails(id: string, organizationId: IDatabaseObjectId, createInventoryDto: CreateInventoryDto) {
        const session = await this.transactionService.startTransaction();
        try {
            const { storeId, productId, productVariantId, promotionId, applyPromotion, ...inventoryDetails } = createInventoryDto;
            const store = await this.FacilityModel.findById(storeId).exec();

            if (!store) {
                throw new NotFoundException("Store not found");
            }

            if (inventoryDetails.productType === ProductType.SIMPLE) {
                const existProd = await this.productModel.findOne({ _id: productId, type: ProductType.SIMPLE });
                if (!existProd) {
                    throw new NotFoundException("Product not found");
                }
                const inventory = await this.inventoryModel.findOne({ _id: id, productType: ProductType.SIMPLE });

                if (!inventory) {
                    throw new NotFoundException("Inventory not found");
                }
            }

            if (inventoryDetails.productType === ProductType.VARIABLE) {
                const variantExist = await this.productVariantModel.findOne({ _id: productVariantId, productId });
                if (!variantExist) {
                    throw new NotFoundException("Product Variant not found");
                }
                const inventory = await this.inventoryModel.findOne({ _id: id, productType: ProductType.VARIABLE });

                if (!inventory) {
                    throw new NotFoundException("Inventory not found");
                }
            }


            if (promotionId) {
                await this.validateDiscount(new Types.ObjectId(promotionId), organizationId, inventoryDetails.mrp as number);
                const isExist = await this.promotionItemService.getTotal({
                    promotion: promotionId,
                    item: new Types.ObjectId(id)
                });
                if (!isExist) {
                    await this.promotionItemService.create({
                        organizationId: new Types.ObjectId(organizationId),
                        promotion: promotionId,
                        itemType: ENUM_ITEM_TYPE.PRODUCT,
                        item: new Types.ObjectId(id)
                    }, { session });
                }
            }

            if (applyPromotion?.length > 0) {
                // Validate all promotions in bulk
                const promotionIds = applyPromotion.map(id => new Types.ObjectId(id));
                await Promise.all(promotionIds.map(promotionId =>
                    this.validateDiscount(promotionId, new Types.ObjectId(organizationId), inventoryDetails.mrp as number)
                ));
                await this.promotionService.applyPromotionsToItem(
                    new Types.ObjectId(organizationId),
                    {
                        applyPromotions: applyPromotion,
                    },
                    {
                        itemType: ENUM_ITEM_TYPE.PRODUCT,
                        _id: new Types.ObjectId(id),
                        price: inventoryDetails.mrp as number
                    },
                    { session }
                );
            }

            const data: Partial<Inventory> = {
                ...inventoryDetails,
                promotion: promotionId ? new Types.ObjectId(promotionId) : null,
                storeId: store._id
            }

            const saveData = await this.inventoryModel.findByIdAndUpdate(id, data, { session }).exec();

            await this.transactionService.commitTransaction(session);
            return saveData;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }
    private async getOrganizationId(user: any) {
        const { role } = user;
        let organizationId = null;
        switch (role.type) {
            case ENUM_ROLE_TYPE.USER:
                const client = await this.ClientsModel.findOne({ userId: user.id }).exec();
                organizationId = client.organizationId;
                break

            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id;
                break;

            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            case ENUM_ROLE_TYPE.TRAINER:
            case ENUM_ROLE_TYPE.WEB_MASTER:
                const staffDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                organizationId = staffDetails.organizationId
                break;

            default:
                throw new BadRequestException("Access denied");
        }
        return organizationId;
    }
    async organizationInventoryList(body: any, organizationId: IDatabaseObjectId) {
        const { search } = body;

        /* ****Later we want to add store id on the basis of store select */
        let query = {
            organizationId: organizationId
        };

        // Creating a search query for the aggregation pipeline
        let searchQuery: any = {};
        if (search) {
            const titleQueryString = search.trim().split(" ").join("|");
            searchQuery = {
                $or: [
                    { "productDetails.name": { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                    { "productDetails.sku": { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                    { "productVariantDetails.name": { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                    { "productVariantDetails.sku": { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                ]
            };
        }

        let countProm = this.inventoryModel.countDocuments({ ...query });

        let dataProm = this.inventoryModel
            .aggregate([
                { $match: query },
                {
                    $lookup: {
                        from: "products",
                        localField: "productId",
                        foreignField: "_id",
                        as: "productDetails",
                        pipeline: [
                            { $project: { name: 1, sku: 1, gst: 1, hsn: 1 } }
                        ],
                    },
                },
                {
                    $lookup: {
                        from: "productvariants",
                        localField: "productVariantId",
                        foreignField: "_id",
                        as: "productVariantDetails",
                        pipeline: [
                            { $project: { name: "$title", sku: 1, gst: 1, hsnCode: 1 } }
                        ],
                    },
                },
                { $unwind: { path: "$productDetails", preserveNullAndEmptyArrays: true } },
                { $unwind: { path: "$productVariantDetails", preserveNullAndEmptyArrays: true } },
                { $match: searchQuery },
                {
                    $sort: {
                        "quantity": -1,
                        createdAt: -1
                    }
                },


            ]);

        let [count, list] = await Promise.all([countProm, dataProm.exec()]);
        return { count, list };
    }

    async validateDiscount(promotionId: IDatabaseObjectId, organizationId: IDatabaseObjectId, itemValue: number) {
        const promotion = await this.promotionService.findOneById(new Types.ObjectId(promotionId));
        if (!promotion) {
            throw new BadRequestException("Invalid promotion id");
        }
        if (promotion.organizationId.toString() !== organizationId.toString()) {
            throw new BadRequestException("Invalid promotion id");
        }
        if (!promotion.isActive || (promotion.endDate && promotion.endDate < new Date())) {
            throw new BadRequestException("Promotion is not active or expired");
        }
        // Check for FLAT discount type
        if (promotion.type === DiscountType.FLAT && (promotion.value < 0 || promotion.value > itemValue)) {
            throw new BadRequestException("Invalid discount value: Flat discount must be between 0 and item value");
        }
        // Check for PERCENTAGE discount type
        if (promotion.type === DiscountType.PERCENTAGE && (promotion.value < 0 || promotion.value > 100)) {
            throw new BadRequestException("Invalid discount value: Percentage discount must be between 0% and 100%");
        }
        return promotion;
    }
}

import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { FacilityAvailability } from "src/facility/schemas/facility-availability.schema";
import { Facility } from "src/facility/schemas/facility.schema";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { TransactionService } from "src/utils/services/transaction.service";
import { Pricing } from "src/organization/schemas/pricing.schema";
import { Services } from "src/organization/schemas/services.schema";
import { Purchase } from "src/users/schemas/purchased-packages.schema";
import { Enrollment } from "../schemas/enrollment.schema";
import { Room } from "src/room/schema/room.schema";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { StaffAvailability } from "src/staff/schemas/staff-availability";
import { MailService } from "src/mail/services/mail.service";
import { Clients } from "src/users/schemas/clients.schema";
import { UpdateCourseStatusDto, CustomerListDto, CustomerListForSchedulingDto, EnrollSchedulingDto, CheckedInDto, CourseListDto } from "src/courses/dto/courses.dto";
import { CreateSchedulingDataDto, SchedulingListDto, UpdateSchedulingDataDto } from "src/courses/dto/scheduling.dto";
import { ScheduleStatusType } from "src/scheduling/enums/schedule-status.enum";
import { Scheduling } from "src/scheduling/schemas/scheduling.schema";
import { ClassType } from "src/utils/enums/class-type.enum";
import moment from "moment";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { DurationUnit } from "src/utils/enums/duration-unit.enum";
import { OrganizationSubSettings } from "src/organizationSettings/schemas/organization-sub-settings.schema";
import { PayRate } from "src/staff/schemas/pay-rate.schema";
import { User } from "src/users/schemas/user.schema";

@Injectable()
export class CourseService {
    constructor(
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(FacilityAvailability.name) private FacilityAvailabilityModel: Model<FacilityAvailability>,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        @InjectModel(StaffAvailability.name) private StaffAvailabilityModel: Model<StaffAvailability>,
        @InjectModel(Pricing.name) private PricingModel: Model<Pricing>,
        @InjectModel(Purchase.name) private PurchaseModel: Model<Purchase>,
        @InjectModel(Room.name) private RoomModel: Model<Room>,
        @InjectModel(Enrollment.name) private EnrollmentModel: Model<Enrollment>,
        @InjectModel(Scheduling.name) private SchedulingModel: Model<Scheduling>,
        @InjectModel(Clients.name) private ClientsModel: Model<Clients>,
        @InjectModel(Services.name) private ServiceModel: Model<Services>,
        @InjectModel(OrganizationSubSettings.name) private OrganizationSubSettingModel: Model<OrganizationSubSettings>,
        @InjectModel(PayRate.name) private PayRateModel: Model<PayRate>,
        @InjectModel(User.name) private UserModel: Model<User>,
        private readonly transactionService: TransactionService,
        private readonly mailService: MailService,
    ) { }

    private async getOrganizationId(user: any): Promise<any> {
        const { role } = user;
        let organizationId: any;
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id;
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.TRAINER:
                const staff = await this.StaffProfileModel.findOne({ userId: user.id }).exec();
                organizationId = staff.organizationId;
                break;
            case ENUM_ROLE_TYPE.USER:
                const client = await this.ClientsModel.findOne({ userId: user.id }).exec();
                organizationId = client.organizationId;
                break;
            default:
                throw new BadRequestException("Access Denied");
        }
        if (organizationId) {
            const organizationSubSettingData = await this.OrganizationSubSettingModel.findOne({ organizationId, key: "subsettings_class_setup_courses" });
            if (!(organizationSubSettingData && organizationSubSettingData?.isEnabled)) {
                throw new BadRequestException("policy.error.abilityForbidden");
            };
            return organizationId;
        }
        throw new BadRequestException("Access Denied");
    }

    async getCourseList(user, courseListDto: CourseListDto): Promise<any> {
        try {
            const pageSize = courseListDto.pageSize ?? 10;
            const page = courseListDto.page ?? 1;
            const skip = pageSize * (page - 1);
            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));

            const filter: any = {
                organizationId,
                isBundledPricing: { $ne: true },
                "services.type": "courses",
            };

            if (courseListDto.search) filter.name = { $regex: courseListDto.search, $options: "i" };
            if (courseListDto.isFeatured !== undefined) filter.isFeatured = courseListDto.isFeatured;

            const aggregationPipeline: any[] = [
                { $match: filter },
                {
                    $lookup: {
                        from: "services",
                        localField: "services.serviceCategory",
                        foreignField: "_id",
                        as: "serviceCategories",
                    },
                },
                {
                    $unwind: {
                        path: "$serviceCategories",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $addFields: {
                        expiredDate: {
                            $switch: {
                                branches: [
                                    {
                                        case: { $eq: ["$durationUnit", "days"] },
                                        then: { $add: ["$createdAt", { $multiply: ["$expiredInDays", 24 * 60 * 60 * 1000] }] },
                                    },
                                    {
                                        case: { $eq: ["$durationUnit", "months"] },
                                        then: { $add: ["$createdAt", { $multiply: ["$expiredInDays", 30 * 24 * 60 * 60 * 1000] }] },
                                    },
                                    {
                                        case: { $eq: ["$durationUnit", "years"] },
                                        then: { $add: ["$createdAt", { $multiply: ["$expiredInDays", 365 * 24 * 60 * 60 * 1000] }] },
                                    },
                                ],
                                default: "$createdAt",
                            },
                        },
                    },
                },
                {
                    $addFields: {
                        isExpired: { $lt: ["$expiredDate", new Date()] },
                    },
                },
                {
                    $addFields: {
                        sortOrder: {
                            $cond: {
                                if: { $eq: ["$isExpired", true] },
                                then: 2, // Active first
                                else: {
                                    $cond: {
                                        if: { $eq: ["$isActive", true] },
                                        then: 0,
                                        else: 1,
                                    },
                                },
                            },
                        },
                    },
                },
                {
                    $sort: { sortOrder: 1, createdAt: -1 },
                },
            ];

            const dateFilter: any = {};
            if (courseListDto.startDate) {
                dateFilter.expiredDate = { $gte: new Date(courseListDto.startDate) };
            }
            if (courseListDto.endDate) {
                dateFilter.createdAt = { $lte: new Date(courseListDto.endDate) };
            }
            if (Object.keys(dateFilter).length > 0) {
                aggregationPipeline.push({ $match: dateFilter });
            }

            aggregationPipeline.push({
                $facet: {
                    count: [{ $count: "total" }],
                    list: [
                        { $skip: skip },
                        { $limit: pageSize },
                        {
                            $project: {
                                name: 1,
                                appointmentTypes: {
                                    $filter: {
                                        input: "$serviceCategories.appointmentType",
                                        as: "appointment",
                                        cond: {
                                            $in: ["$$appointment._id", "$services.appointmentType"],
                                        },
                                    },
                                },
                                serviceCategoryId: "$serviceCategories._id",
                                serviceCategoryName: "$serviceCategories.name",
                                startDate: "$createdAt",
                                endDate: "$expiredDate",
                                isActive: 1,
                                isExpired: 1,
                            },
                        },
                    ],
                },
            });

            const result = await this.PricingModel.aggregate(aggregationPipeline);
            const count = result[0].count[0]?.total ?? 0;
            const list = result[0].list;

            return { list, count };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async getCourseDetail(courseId: string): Promise<any> {
        const pricingData: any = await this.PricingModel.findOne({ _id: courseId }).lean();
        if (!pricingData) throw new BadRequestException("Course not found");
        const { services } = pricingData;
        const serviceCategoryDetails = await this.ServiceModel.findOne({ _id: services.serviceCategory }, "name appointmentType");
        if (!serviceCategoryDetails) throw new BadRequestException("Service category details not found");
        const multiplierMap: Record<string, number> = {
            days: 24 * 60 * 60 * 1000,
            months: 30 * 24 * 60 * 60 * 1000,
            years: 365 * 24 * 60 * 60 * 1000,
        };

        const expiredDate = new Date(new Date(pricingData.createdAt).getTime() + pricingData.expiredInDays * multiplierMap[pricingData.durationUnit]);

        const data = {
            name: pricingData.name,
            image: pricingData.image,
            description: pricingData.description,
            isFeatured: pricingData.isFeatured,
            isActive: pricingData.isActive,
            price: pricingData.price,
            expiredDate,
            createdAt: pricingData.createdAt,
            serviceCategoryName: serviceCategoryDetails.name,
            serviceCategoryId: services.serviceCategory,
            appointmentTypes: [],
            sessionCount: services.sessionCount === Infinity ? "Infinity" : services.sessionCount,
            sessionType: services.sessionType,
            sessionPerDay: services.sessionPerDay,
        };
        for (const appointmentId of services.appointmentType || []) {
            const match = serviceCategoryDetails.appointmentType.find((a: any) => a._id.toString() === appointmentId.toString());
            if (match) data.appointmentTypes.push({ _id: appointmentId, name: match.name });
        }
        return data;
    }

    async getCustomerList(user: IUserDocument, customerListDto: CustomerListDto): Promise<any> {
        try {
            const { role } = user;
            const { pageSize = 10, page = 1, courseId, schedulingId, search } = customerListDto;
            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            const skip = pageSize * (page - 1);
            let facilityIds = [];
            if (role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
                const facilities = await this.FacilityModel.find({ organizationId: user._id }, { _id: 1 }).lean();
                facilityIds = facilities.map((facility) => facility._id);
            } else {
                const staff = await this.StaffProfileModel.findOne({ userId: user._id }, { facilityId: 1 }).lean();
                facilityIds = staff?.facilityId || [];
            }

            if (!facilityIds.length) return { list: [], count: 0 };
            const enrollmentMatchConditions: any = [{ $eq: ["$userId", "$$userId"] }, { $eq: ["$packageId", "$$packageId"] }];
            if (schedulingId) enrollmentMatchConditions.push({ $eq: ["$schedulingId", new Types.ObjectId(schedulingId)] });

            const searchQuery = search ? { name: { $regex: search, $options: "i" } } : {};

            const result = await this.PurchaseModel.aggregate([
                { $match: { facilityId: { $in: facilityIds }, packageId: new Types.ObjectId(courseId) } },
                { $sort: { createdAt: -1 } },
                {
                    $lookup: {
                        from: "users",
                        localField: "userId",
                        foreignField: "_id",
                        as: "userData",
                    },
                },
                { $unwind: "$userData" },
                {
                    $lookup: {
                        from: "clients",
                        localField: "userId",
                        foreignField: "userId",
                        as: "clientData",
                    },
                },
                { $unwind: { path: "$clientData", preserveNullAndEmptyArrays: true } },
                {
                    $lookup: {
                        from: "enrollments",
                        let: { userId: "$userId", packageId: "$packageId" },
                        pipeline: [{ $match: { $expr: { $and: enrollmentMatchConditions } } }],
                        as: "enrollmentData",
                    },
                },
                {
                    $addFields: {
                        totalEnrollments: { $size: "$enrollmentData" },
                        totalCheckedIns: {
                            $size: {
                                $filter: {
                                    input: "$enrollmentData",
                                    as: "enrollment",
                                    cond: { $eq: ["$$enrollment.isCheckedIn", true] },
                                },
                            },
                        },
                        ...(schedulingId && {
                            enrolled: { $gt: [{ $size: "$enrollmentData" }, 0] },
                            checkedIn: { $gt: ["$totalCheckedIns", 0] },
                        }),
                    },
                },
                {
                    $group: {
                        _id: "$userId",
                        name: { $first: { $concat: ["$userData.firstName", " ", "$userData.lastName"] } },
                        mobile: { $first: "$userData.mobile" },
                        date: { $first: "$createdAt" },
                        clientId: { $first: "$clientData.clientId" },
                        totalCheckedIns: { $first: "$totalCheckedIns" },
                        totalEnrollments: { $first: "$totalEnrollments" },
                        ...(schedulingId ? { enrolled: { $first: "$enrolled" }, checkedIn: { $first: "$checkedIn" } } : {}),
                    },
                },
                ...(schedulingId ? [{ $match: { totalEnrollments: { $gt: 0 } } }] : []),
                { $match: searchQuery },
                {
                    $facet: {
                        count: [{ $count: "total" }],
                        list: [{ $skip: skip }, { $limit: pageSize }],
                    },
                },
            ]);

            return {
                list: result[0]?.list || [],
                count: result[0]?.count?.[0]?.total || 0,
            };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async getCustomerListForScheduling(user, customerListForSchedulingDto: CustomerListForSchedulingDto): Promise<any> {
        try {
            const pageSize = customerListForSchedulingDto.pageSize ?? 10;
            const page = customerListForSchedulingDto.page ?? 1;
            const skip = pageSize * (page - 1);
            const searchQuery = customerListForSchedulingDto.search ? { name: { $regex: customerListForSchedulingDto.search, $options: "i" } } : {};

            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            const result = await this.EnrollmentModel.aggregate([
                {
                    $match: {
                        schedulingId: new Types.ObjectId(customerListForSchedulingDto.schedulingId),
                    },
                },
                { $sort: { createdAt: -1 } },
                {
                    $lookup: {
                        from: "users",
                        localField: "userId",
                        foreignField: "_id",
                        as: "userData",
                    },
                },
                { $unwind: { path: "$userData", preserveNullAndEmptyArrays: true } },
                {
                    $lookup: {
                        from: "clients",
                        let: { userId: "$userId" },
                        pipeline: [{ $match: { $expr: { $eq: ["$userId", "$$userId"] } } }],
                        as: "clientData",
                    },
                },
                { $unwind: { path: "$clientData", preserveNullAndEmptyArrays: true } },
                {
                    $project: {
                        _id: "$_id",
                        userId: "$userId",
                        name: "$userData.name",
                        enrollmentId: "$userData.name",
                        mobile: "$userData.mobile",
                        date: "$createdAt",
                        clientId: "$clientData.clientId",
                        isCheckedIn: "$isCheckedIn",
                        totalCheckedIns: { $sum: { $cond: [{ $eq: ["$isCheckedIn", true] }, 1, 0] } },
                        totalEnrollments: { $sum: 1 },
                    },
                },
                { $match: searchQuery },
                {
                    $facet: {
                        count: [{ $count: "total" }],
                        list: [{ $skip: skip }, { $limit: pageSize }],
                    },
                },
            ]);

            const count = result[0].count.length > 0 ? result[0].count[0].total : 0;
            return { list: result[0].list, count };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async updateCourseStatus(updateCourseDto: UpdateCourseStatusDto, user: any): Promise<any> {
        try {
            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            const course = await this.PricingModel.findOne({
                _id: updateCourseDto.courseId,
                organizationId,
            });

            if (!course) throw new NotFoundException(`Course not found`);

            if (updateCourseDto.isActive !== undefined && updateCourseDto.isActive !== course.isActive) course.isActive = updateCourseDto.isActive;

            if (updateCourseDto.isFeatured !== undefined && updateCourseDto.isFeatured !== course.isFeatured) course.isFeatured = updateCourseDto.isFeatured;

            if (updateCourseDto.description !== undefined && updateCourseDto.description !== course.description) course.description = updateCourseDto.description;

            if (updateCourseDto.image !== undefined && updateCourseDto.image !== course.image) course.image = updateCourseDto.image;

            await course.save();

            return {
                message: "Course details updated successfully",
                course,
            };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    private async validatePackageOwnership(organizationId: Types.ObjectId, packageId: Types.ObjectId) {
        const pricingData: any = await this.PricingModel.findOne({ _id: packageId, organizationId }).select("_id expiredInDays durationUnit createdAt services");
        if (!pricingData) throw new BadRequestException("Access Denied: Course does not belong to this organization.");

        const startDate = new Date(pricingData.createdAt);
        const endDate = new Date(startDate);

        switch (pricingData.durationUnit) {
            case DurationUnit.DAYS:
                endDate.setDate(endDate.getDate() + pricingData.expiredInDays);
                break;
            case DurationUnit.MONTHS:
                endDate.setMonth(endDate.getMonth() + pricingData.expiredInDays);
                break;
            case DurationUnit.YEARS:
                endDate.setFullYear(endDate.getFullYear() + pricingData.expiredInDays);
                break;
        }
        if (new Date() > endDate) throw new BadRequestException("Access Denied: Course has expired.");

        return pricingData;
    }

    private isTimeOverlapping(slot1: { from: string; to: string }, slot2: { from: String; to: String }): boolean {
        return slot1.from < slot2.to && slot1.to > slot2.from;
    }

    private isTimeWithinAvailableSlot(requestedSlot: { from: string; to: string }, availableSlot: { from: string; to: string }): boolean {
        return (
            new Date(`1970-01-01T${requestedSlot.from}:00`) >= new Date(`1970-01-01T${availableSlot.from}:00`) &&
            new Date(`1970-01-01T${requestedSlot.to}:00`) <= new Date(`1970-01-01T${availableSlot.to}:00`)
        );
    }

    private async validateFacilityValidation(createSchedulingDto: CreateSchedulingDataDto, organizationId: Types.ObjectId) {
        const { facilityId, from, to, date } = createSchedulingDto;
        const facility = await this.FacilityModel.findOne({ _id: facilityId, organizationId }).select("_id");
        if (!facility) throw new BadRequestException("Access Denied: Facility does not belong to this organization.");
        const dayOfWeek = new Date(date).toLocaleString("en-US", { weekday: "short" }).toLowerCase();
        const parsedDate = new Date(date);
        const startOfDay = new Date(parsedDate.setHours(0, 0, 0, 0));
        const endOfDay = new Date(parsedDate.setHours(23, 59, 59, 999));
        const facilityRecords = await this.FacilityAvailabilityModel.find(
            {
                facilityId: new Types.ObjectId(facilityId),
                organizationId,
                $or: [{ type: "unavailable", fromDate: { $lte: endOfDay }, endDate: { $gte: startOfDay } }, { type: "available" }],
            },
            { type: 1, time: 1, workingHours: 1 },
        );

        const unavailableRecord = facilityRecords.find((record) => record.type === "unavailable");
        if (unavailableRecord) {
            for (const slot of unavailableRecord.time) {
                if (this.isTimeOverlapping({ from, to }, slot)) {
                    throw new BadRequestException(`Facility is unavailable from ${slot.from} to ${slot.to}`);
                }
            }
        }

        const facilityAvailability = facilityRecords.find((record) => record.type === "available");
        if (!facilityAvailability) throw new BadRequestException("No available working hours found for the facility.");

        const availableSlots = facilityAvailability.workingHours?.[dayOfWeek];
        if (!availableSlots) throw new BadRequestException(`Facility is not available on ${dayOfWeek}.`);

        if (!availableSlots.some((slot) => this.isTimeWithinAvailableSlot({ from, to }, slot))) {
            const availableSlotsText = availableSlots.map((slot) => `${slot.from} to ${slot.to}`).join(", ");
            throw new BadRequestException(`Requested slot from ${from} to ${to} is outside facility's available hours. Working hours: ${availableSlotsText}.`);
        }
    }

    private async validateStaffValidation(createSchedulingDto: any, organizationId: Types.ObjectId, courseData: any) {
        const { facilityId, from, to, date, trainerId, schedulingId } = createSchedulingDto;
        const staff = await this.StaffProfileModel.findOne({ userId: trainerId, facilityId }).select("_id");
        if (!staff) throw new BadRequestException("Staff is not part of this facility.");
        // const parsedDate = new Date(date);
        // const startOfDay = new Date(parsedDate.setHours(0, 0, 0, 0));
        // const endOfDay = new Date(parsedDate.setHours(23, 59, 59, 999));
        // const staffRecord = await this.StaffAvailabilityModel.findOne(
        //     { userId: trainerId, facilityId: new Types.ObjectId(facilityId), organizationId, date: { $gte: startOfDay, $lte: endOfDay } },
        //     { timeSlots: 1 },
        // );

        // if (!staffRecord) throw new BadRequestException("No available working hours found for the staff.");

        // const availableSlots = staffRecord.timeSlots;
        // if (!availableSlots.length) throw new BadRequestException(`Staff is not available on this date.`);

        // if (!availableSlots.some((slot) => this.isTimeWithinAvailableSlot({ from, to }, slot))) {
        //     const availableSlotsText = availableSlots.map((slot) => `${slot.from} to ${slot.to}`).join(", ");
        //     throw new BadRequestException(`Requested slot from ${from} to ${to} is outside staff's available hours. Working hours: ${availableSlotsText}.`);
        // }

        const isTrainerBusy = await this.SchedulingModel.countDocuments({
            _id: { $ne: schedulingId },
            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
            trainerId,
            date,
            $and: [{ from: { $lt: to } }, { to: { $gt: from } }],
        });
        const user = await this.UserModel.findOne({ _id: trainerId }).exec();
        if (isTrainerBusy) throw new BadRequestException(`${user.name} is unavailable due to an existing appointment`);

        // const reqFromDate = new Date(`1970-01-01T${from}:00`);
        // const reqToDate = new Date(`1970-01-01T${to}:00`);
        // const payRateIds = new Set<string>();
        // for (let slot of availableSlots) {
        //     const fromDate = new Date(`1970-01-01T${slot.from}:00`);
        //     const toDate = new Date(`1970-01-01T${slot.to}:00`);
        //     if (fromDate <= reqFromDate && toDate >= reqToDate) {
        //         if (Array.isArray(slot.payRateIds)) {
        //             slot.payRateIds.forEach((id: string) => payRateIds.add(id));
        //         }
        //     }
        // }

        // if (payRateIds.size === 0) throw new BadRequestException("Staff not available at the given time");
        // const payRates = await this.PayRateModel.find({
        //     _id: { $in: Array.from(payRateIds) },
        //     serviceCategory: courseData?.services?.serviceCategory,
        //     appointmentType: { $in: courseData?.services?.appointmentType },
        // });
        const payRateData = await this.PayRateModel.findOne({
            userId: trainerId,
            serviceCategory: courseData?.services?.serviceCategory,
            appointmentType: { $in: courseData?.services?.appointmentType },
        });
        if (!payRateData) throw new BadRequestException("Staff not available for the given sub type");
    }

    private async validateRoomAvailability(createSchedulingDto: any) {
        const { roomId, from, to, date } = createSchedulingDto;

        const selectedRoom = await this.RoomModel.findOne({ _id: roomId, status: true }).select("capacity");
        if (!selectedRoom) throw new BadRequestException("Invalid room.");
        const obj: any = {
            roomId,
            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
            date,
            from: { $lt: to },
            to: { $gt: from },
        };

        if (createSchedulingDto.schedulingId) obj._id = { $ne: createSchedulingDto.schedulingId };
        const currentRoomStrength = await this.SchedulingModel.countDocuments(obj);

        if (currentRoomStrength >= selectedRoom.capacity) {
            throw new BadRequestException(`Selected room is at full capacity from ${from} to ${to}.`);
        }
    }

    private isFromLessThanTo({ from, to }: { from: string; to: string }): boolean {
        return new Date(`1970-01-01T${from}:00`) < new Date(`1970-01-01T${to}:00`);
    }

    async createCourseScheduling(createSchedulingDto: CreateSchedulingDataDto, user: any): Promise<any> {
        const session = await this.transactionService.startTransaction();
        const { role } = user;
        if (role.type === ENUM_ROLE_TYPE.TRAINER) throw new BadRequestException("Access denied.");
        try {
            if (!this.isFromLessThanTo({ from: createSchedulingDto.from, to: createSchedulingDto.to })) throw new BadRequestException("Invalid time range.");
            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            const courseId = new Types.ObjectId(createSchedulingDto.courseId);
            const roomId = createSchedulingDto.roomId ? new Types.ObjectId(createSchedulingDto.roomId) : null;

            await this.validateFacilityValidation(createSchedulingDto, organizationId);
            const courseData = await this.validatePackageOwnership(organizationId, courseId);
            await this.validateStaffValidation(createSchedulingDto, organizationId, courseData);
            if (roomId) await this.validateRoomAvailability(createSchedulingDto);

            const newSchedule = new this.SchedulingModel({
                organizationId,
                scheduledBy: user._id,
                facilityId: createSchedulingDto.facilityId,
                packageId: courseId,
                classType: createSchedulingDto.classType,
                subTypeId: createSchedulingDto.subType,
                trainerId: createSchedulingDto.trainerId,
                serviceCategoryId: createSchedulingDto.serviceCategory,
                roomId,
                dateRange: createSchedulingDto.dateRange,
                date: new Date(createSchedulingDto.date),
                from: createSchedulingDto.from,
                to: createSchedulingDto.to,
                duration: createSchedulingDto.duration,
                sessions: 1,
                notes: createSchedulingDto.notes,
                classCapacity: createSchedulingDto.classCapacity,
            });

            console.log(123456, createSchedulingDto, newSchedule);
            await newSchedule.save();

            const distinctUsers = await this.PurchaseModel.aggregate([
                { $match: { packageId: courseId } },
                { $group: { _id: "$userId", purchaseId: { $first: "$_id" } } },
                { $limit: createSchedulingDto.classCapacity },
            ]);

            const newEnrollments = distinctUsers.map((doc) => ({
                schedulingId: newSchedule._id,
                userId: doc._id,
                packageId: courseId,
                purchaseId: doc.purchaseId,
            }));

            if (newEnrollments.length) await this.EnrollmentModel.insertMany(newEnrollments);

            await this.transactionService.commitTransaction(session);
            return { message: "Schedule created successfully", data: newSchedule };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async updateCourseScheduling(updateSchedulingDto: UpdateSchedulingDataDto, user: any): Promise<any> {
        const session = await this.transactionService.startTransaction();
        const { role } = user;
        if (role.type === ENUM_ROLE_TYPE.TRAINER) throw new BadRequestException("Access denied.");
        try {
            if (!this.isFromLessThanTo({ from: updateSchedulingDto.from, to: updateSchedulingDto.to })) throw new BadRequestException("Invalid time range.");

            if (updateSchedulingDto.classCapacity <= 0) throw new BadRequestException("Class capacity must be greater than 0.");

            const scheduleDate = new Date(updateSchedulingDto.date);
            if (isNaN(scheduleDate.getTime())) {
                throw new BadRequestException("Invalid date format.");
            }

            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            const courseId = new Types.ObjectId(updateSchedulingDto.courseId);
            const roomId = updateSchedulingDto.roomId ? new Types.ObjectId(updateSchedulingDto.roomId) : null;

            const existingEnrollments = await this.EnrollmentModel.find({ schedulingId: updateSchedulingDto.schedulingId });
            if (existingEnrollments.length > updateSchedulingDto.classCapacity)
                throw new BadRequestException(`Cannot update scheduling: existing enrollments exceed new class capacity.`);

            await this.validateFacilityValidation(updateSchedulingDto, organizationId);
            const courseData = await this.validatePackageOwnership(organizationId, courseId);
            await this.validateStaffValidation(updateSchedulingDto, organizationId, courseData);
            if (roomId) await this.validateRoomAvailability(updateSchedulingDto);

            const schedulingDetails = await this.SchedulingModel.findOneAndUpdate(
                { _id: updateSchedulingDto.schedulingId },
                {
                    $set: {
                        subTypeId: updateSchedulingDto.subType,
                        serviceCategoryId: updateSchedulingDto.serviceCategory,
                        roomId,
                        dateRange: updateSchedulingDto.dateRange,
                        date: scheduleDate,
                        from: updateSchedulingDto.from,
                        trainerId: updateSchedulingDto.trainerId,
                        to: updateSchedulingDto.to,
                        duration: updateSchedulingDto.duration,
                        notes: updateSchedulingDto.notes,
                        classCapacity: updateSchedulingDto.classCapacity,
                    },
                },
                { new: true, session },
            );

            if (!schedulingDetails) throw new BadRequestException("Scheduling does not exist.");

            let newEnrollments = [];

            const availableSlots = updateSchedulingDto.classCapacity - existingEnrollments.length;
            if (availableSlots > 0) {
                const existingUserIds = existingEnrollments.map((enrollment) => enrollment.userId);

                const distinctUsers = await this.PurchaseModel.aggregate([
                    { $match: { packageId: courseId, userId: { $nin: existingUserIds } } },
                    { $group: { _id: "$userId", purchaseId: { $first: "$_id" } } },
                    { $limit: availableSlots },
                ]).session(session);

                newEnrollments = distinctUsers.map((doc) => ({
                    schedulingId: updateSchedulingDto.schedulingId,
                    userId: doc._id,
                    packageId: courseId,
                    purchaseId: doc.purchaseId,
                }));

                if (newEnrollments.length) {
                    await this.EnrollmentModel.insertMany(newEnrollments, { session });
                }
            }
            await this.transactionService.commitTransaction(session);
            return {
                message: "Schedule updated successfully",
                data: schedulingDetails,
                newlyEnrolledCount: newEnrollments.length,
            };
        } catch (error) {
            console.error("Error updating scheduling:", error);
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async getSchedulingList(user: IUserDocument, schedulingListDto: SchedulingListDto): Promise<any> {
        try {
            const pageSize = schedulingListDto.pageSize ?? 10;
            const page = schedulingListDto.page ?? 1;
            const skip = pageSize * (page - 1);
            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            const filter: any = { classType: ClassType.COURSES };
            if (schedulingListDto.courseId) filter.packageId = new Types.ObjectId(schedulingListDto.courseId);
            if (schedulingListDto.roomId) filter.roomId = new Types.ObjectId(schedulingListDto.roomId);
            if (schedulingListDto.facilityId) filter.facilityId = new Types.ObjectId(schedulingListDto.facilityId);
            if (schedulingListDto.startDate && schedulingListDto.endDate) {
                filter["date"] = {
                    $gte: schedulingListDto.startDate,
                    $lte: schedulingListDto.endDate,
                };
            }

            const result = await this.SchedulingModel.aggregate([
                { $match: filter },
                {
                    $lookup: {
                        from: "enrollments",
                        let: {
                            schedulingId: "$_id",
                            userId: new Types.ObjectId(user._id),
                        },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [...(user.role.type === ENUM_ROLE_TYPE.USER ? [{ $eq: ["$userId", "$$userId"] }] : []), { $eq: ["$schedulingId", "$$schedulingId"] }],
                                    },
                                },
                            },
                        ],
                        as: "enrollmentData",
                    },
                },
                ...(user.role.type === ENUM_ROLE_TYPE.USER
                    ? [
                        {
                            $match: {
                                $expr: {
                                    $gt: [
                                        {
                                            $size: "$enrollmentData",
                                        },
                                        0,
                                    ],
                                },
                            },
                        },
                    ]
                    : []),
                { $sort: { date: 1 } },
                {
                    $lookup: {
                        from: "users",
                        localField: "trainerId",
                        foreignField: "_id",
                        as: "trainerData",
                    },
                },
                {
                    $unwind: {
                        path: "$trainerData",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "facilities",
                        localField: "facilityId",
                        foreignField: "_id",
                        as: "facilityData",
                    },
                },
                {
                    $unwind: {
                        path: "$facilityData",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "rooms",
                        localField: "roomId",
                        foreignField: "_id",
                        as: "roomData",
                    },
                },
                {
                    $unwind: {
                        path: "$roomData",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "services",
                        localField: "serviceCategoryId",
                        foreignField: "_id",
                        as: "serviceCategoryData",
                    },
                },
                {
                    $unwind: {
                        path: "$serviceCategoryData",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "pricings",
                        localField: "packageId",
                        foreignField: "_id",
                        as: "pricingDetails",
                    },
                },
                {
                    $unwind: {
                        path: "$pricingDetails",
                    },
                },
                {
                    $addFields: {
                        totalEnrollments: { $size: "$enrollmentData" },
                        totalCheckedIns: {
                            $size: {
                                $filter: {
                                    input: "$enrollmentData",
                                    as: "enrollment",
                                    cond: { $eq: ["$$enrollment.isCheckedIn", true] },
                                },
                            },
                        },
                    },
                },
                {
                    $project: {
                        from: "$from",
                        to: "$to",
                        date: "$date",
                        scheduleStatus: "$scheduleStatus",
                        trainerName: "$trainerData.name",
                        trainerId: "$trainerData._id",
                        facilityName: "$facilityData.facilityName",
                        facilityId: "$facilityData._id",
                        roomName: "$roomData.roomName",
                        roomId: "$roomData._id",
                        totalCheckedIns: "$totalCheckedIns",
                        totalEnrollments: "$totalEnrollments",
                        serviceCategoryId: "$serviceCategoryId",
                        subTypeId: "$subTypeId",
                        serviceCategoryName: "$serviceCategoryData.name",
                        subTypeData: {
                            $filter: {
                                input: "$serviceCategoryData.appointmentType",
                                as: "appointment",
                                cond: {
                                    $eq: ["$$appointment._id", "$subTypeId"],
                                },
                            },
                        },
                        courseId: "$packageId",
                        courseName: "$pricingDetails.name",
                    },
                },
                {
                    $facet: {
                        count: [{ $match: { scheduleStatus: { $ne: ScheduleStatusType.CANCELED } } }, { $count: "total" }],
                        list: [{ $skip: skip }, { $limit: pageSize }],
                    },
                },
            ]);

            const count = result[0].count.length > 0 ? result[0].count[0].total : 0;

            return { list: result[0].list, count };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async getSchedulingDetails(user: Record<string, any>, schedulingId: string): Promise<Record<string, any>> {
        try {
            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            let schedule: any = await this.SchedulingModel.findOne({ organizationId, _id: schedulingId }).lean();
            if (!schedule) throw new NotFoundException("Schedule not found");
            return {
                message: "Schedule fetched successfully",
                data: schedule,
            };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async deleteSchedule(user: Record<string, any>, schedulingId: string): Promise<any> {
        try {
            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            const schedule = await this.SchedulingModel.findOne({ _id: schedulingId });
            if (!schedule) throw new NotFoundException(`Schedule not found`);
            if (schedule.organizationId?.toString() !== organizationId.toString()) throw new NotFoundException(`Schedule does not belong to the current organization`);

            const sessionStartDateTime = moment(schedule.date).format("YYYY-MM-DD") + " " + schedule.from;
            const currentDateTime = moment();

            if (currentDateTime.isAfter(moment(sessionStartDateTime, "YYYY-MM-DD HH:mm"))) throw new BadRequestException("You can only delete the session before it starts");

            const isEnrolled = await this.EnrollmentModel.exists({ schedulingId });
            if (isEnrolled) throw new BadRequestException("Schedule cannot be deleted because clients are already enrolled.");

            await this.SchedulingModel.deleteOne({ _id: schedulingId, organizationId });

            return {
                message: "Schedule deleted successfully",
            };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async cancelSchedule(user: Record<string, any>, schedulingId: string): Promise<any> {
        try {
            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            const schedule = await this.SchedulingModel.findOne({ _id: schedulingId });
            if (!schedule) throw new NotFoundException(`Schedule not found`);
            if (schedule.scheduleStatus === ScheduleStatusType.CANCELED) throw new BadRequestException(`Schedule already canceled.`);
            if (schedule.organizationId?.toString() !== organizationId.toString()) throw new BadRequestException(`Schedule does not belong to the current organization`);

            const sessionStartDateTime = moment(schedule.date).format("YYYY-MM-DD") + " " + schedule.from;
            const currentDateTime = moment();

            if (currentDateTime.isAfter(moment(sessionStartDateTime, "YYYY-MM-DD HH:mm"))) throw new BadRequestException("You can only cancel the session before it starts");
            schedule.scheduleStatus = ScheduleStatusType.CANCELED;
            await schedule.save();
            return {
                message: "Schedule cancelled successfully",
            };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async enrollScheduling(enrollSchedulingDto: EnrollSchedulingDto, user): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            const { scheduleIds, customerIds, courseId } = enrollSchedulingDto;

            const [schedules, clients, existingEnrollments]: any = await Promise.all([
                this.SchedulingModel.find({ _id: { $in: scheduleIds } }).lean(),
                this.ClientsModel.find({ userId: { $in: customerIds } })
                    .populate("userId")
                    .lean(),
                this.EnrollmentModel.find({ schedulingId: { $in: scheduleIds }, userId: { $in: customerIds } }).lean(),
            ]);

            const errorMessages: string[] = [];
            const scheduleMap = new Map(schedules.map((s) => [s._id.toString(), s]));
            const clientMap = new Map(clients.map((c) => [c.userId?._id?.toString(), c]));
            const existingEnrollmentSet = new Set(existingEnrollments.map((e) => `${e.schedulingId}-${e.userId}`));

            const missingSchedules = scheduleIds.filter((id) => !scheduleMap.has(id.toString()));
            const missingClients = customerIds.filter((id) => !clientMap.has(id.toString()));

            if (missingSchedules.length) errorMessages.push(`Schedules not found: ${missingSchedules.join(", ")}`);
            if (missingClients.length) errorMessages.push(`Clients not found: ${missingClients.join(", ")}`);

            const now = moment();
            const newEnrollments: any[] = [];

            const purchaseDataMap = new Map();
            const purchaseResults = await Promise.all(clients.map((client) => this.PurchaseModel.findOne({ userId: client.userId?._id, packageId: courseId }).lean()));
            clients.forEach((client, index) => purchaseDataMap.set(client.userId?._id?.toString(), purchaseResults[index]));

            for (const schedule of schedules) {
                if (!schedule) continue;
                const { date, from, to, organizationId: scheduleOrgId, packageId, scheduleStatus, classCapacity } = schedule;
                const scheduleDate = date ? moment(date).format("DD/MM/YYYY") : "Unknown Date";
                const scheduleTime = from && to ? `Time: ${from} to ${to}` : "Unknown Time";
                const sessionEnd = date && to ? moment(`${moment(date).format("YYYY-MM-DD")} ${to}`, "YYYY-MM-DD HH:mm") : null;

                if (!scheduleOrgId || scheduleOrgId.toString() !== organizationId.toString()) {
                    errorMessages.push(`${scheduleDate} ${scheduleTime} does not belong to the current organization.`);
                    continue;
                }
                if (!packageId || packageId.toString() !== courseId?.toString()) {
                    errorMessages.push(`${scheduleDate} ${scheduleTime} does not belong to the current course.`);
                    continue;
                }
                if (scheduleStatus === ScheduleStatusType.CANCELED) {
                    errorMessages.push(`${scheduleDate} ${scheduleTime} is canceled.`);
                    continue;
                }
                if (sessionEnd && now.isAfter(sessionEnd)) {
                    errorMessages.push(`${scheduleDate} ${scheduleTime} has already ended.`);
                    continue;
                }

                const totalEnrollments = await this.EnrollmentModel.countDocuments({ schedulingId: schedule._id });
                if (classCapacity - totalEnrollments < clients.length) {
                    errorMessages.push(`${scheduleDate} ${scheduleTime} does not have enough spots.`);
                    continue;
                }
            }

            for (const client of clients) {
                if (!client) continue;
                const clientName = client.userId?.name || "Unknown Client";
                const purchaseData = purchaseDataMap.get(client.userId?._id.toString());

                if (!client.organizationId || client.organizationId.toString() !== organizationId.toString()) {
                    errorMessages.push(`${clientName} is not in the current organization.`);
                    continue;
                }
                if (!purchaseData) {
                    errorMessages.push(`${clientName} has no access to this course.`);
                    continue;
                }
                if (purchaseData.totalSessions === purchaseData.sessionConsumed) {
                    errorMessages.push(`${clientName} has no remaining sessions.`);
                    continue;
                }

                for (const schedule of schedules) {
                    if (existingEnrollmentSet.has(`${schedule._id}-${client.userId?._id}`)) {
                        errorMessages.push(`${clientName} is already enrolled in ${moment(schedule.date).format("DD/MM/YYYY")} (${schedule.from} - ${schedule.to}).`);
                        continue;
                    }
                    newEnrollments.push({
                        schedulingId: schedule._id,
                        userId: client.userId?._id,
                        packageId: courseId,
                        purchaseId: purchaseData._id,
                    });
                    await this.PurchaseModel.updateOne({ _id: purchaseData._id }, { $inc: { sessionConsumed: schedule.sessions } });
                }
            }

            if (errorMessages.length) throw new BadRequestException(errorMessages.join("\n"));
            if (newEnrollments.length) await this.EnrollmentModel.insertMany(newEnrollments);

            await this.transactionService.commitTransaction(session);
            return { message: "All schedules enrolled successfully." };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw new BadRequestException(error.message);
        } finally {
            session.endSession();
        }
    }

    async checkIn(user, checkInDto: CheckedInDto): Promise<any> {
        try {
            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            const { enrollmentId, isCheckedIn } = checkInDto;
            const enrollment = await this.EnrollmentModel.findOne({ _id: enrollmentId });

            if (!enrollment) throw new BadRequestException("Enrollment not found.");

            if (isCheckedIn && enrollment.isCheckedIn) throw new BadRequestException("User has already checked in.");
            else if (!isCheckedIn && !enrollment.isCheckedIn) throw new BadRequestException("User has already not arrived.");

            enrollment.isCheckedIn = isCheckedIn;
            enrollment.checkedInDate = isCheckedIn ? new Date() : null;
            await enrollment.save();

            return { message: isCheckedIn ? "Checked in successfully." : "Mark un-arrived successfully." };
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async deleteEnrollment(user, enrollmentId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const organizationId = new Types.ObjectId(await this.getOrganizationId(user));
            const enrollmentData = await this.EnrollmentModel.findOne({ _id: enrollmentId }).lean();
            if (!enrollmentData) throw new NotFoundException(`Enrollment not found.`);
            if (enrollmentData.isCheckedIn) throw new BadRequestException("Enrollment cannot be remove because client has already checked in.");
            const schedule = await this.SchedulingModel.findOne({ _id: enrollmentData.schedulingId }).lean();
            if (!schedule) throw new NotFoundException(`Schedule not found`);
            await this.EnrollmentModel.deleteOne({ _id: enrollmentId });
            await this.PurchaseModel.updateOne({ _id: schedule.purchaseId }, { $inc: { sessionConsumed: -1 * schedule.sessions } });

            await this.transactionService.commitTransaction(session);
            return {
                message: "Enrollment deleted successfully",
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw new BadRequestException(error.message);
        } finally {
            session.endSession();
        }
    }
}

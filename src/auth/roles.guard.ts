import { CanActivate, ExecutionContext, ForbiddenException, Injectable } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { AuthService } from "./services/auth.service";

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector, private authService: AuthService) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const roles = this.reflector.get<string[]>("roles", context.getHandler()) ?? [];
    const request = context.switchToHttp().getRequest();

    if (request?.user) {
      const { user, sub, userId } = request.user;
      let id = user ?? sub ?? userId
      const userD = await this.authService.getById(id);
      request.__user = userD;
      request.__organizationId = user.organizationId;
      if (userD.role.type === "superAdmin" || userD.role.type === "organization") return true;
      return roles.includes(userD.role.type);
    }
    throw new ForbiddenException("policy.error.abilityForbidden");
  }
}

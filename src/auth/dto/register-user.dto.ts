import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsEmail, Length, ValidateIf, IsStrongPassword, IsMongoId, IsString, IsOptional, IsNotEmpty, IsBoolean } from "class-validator";
import { AuthTypes } from "../../utils/enums/auth.enum";
import { Transform } from "class-transformer";
import { ApiProperty } from "@nestjs/swagger";

export class RegisterUserDto {
    @ApiProperty({
        description: "The authentication type for registration. Can be either EMAIL or MOBILE.",
        enum: AuthTypes,
        example: AuthTypes.EMAIL,
    })
    @IsEnum([AuthTypes.EMAIL, AuthTypes.MOBILE], { message: "Authentication type must be either EMAIL or MO<PERSON>LE." })
    @IsNotEmpty({ message: "Authentication type is required." })
    type: string;

    @ApiProperty({
        description: "Name of the Organization.",
        example: "Knox",
        minLength: 2,
        maxLength: 50,
    })
    @Length(2, 50, { message: "Name must be between 2 and 50 characters long." })
    @IsOptional()
    name: string;

    @ApiProperty({
        description: "First Name of the user.",
        example: "<PERSON>",
        minLength: 2,
        maxLength: 50,
    })
    @Length(2, 50, { message: "FirstName must be between 2 and 50 characters long." })
    @IsOptional()
    firstName: string;

    @ApiProperty({
        description: "Last Name of the user.",
        example: "Wick",
        minLength: 2,
        maxLength: 50,
    })
   
    @IsOptional()
    lastName?: string;

    @ApiProperty({
        description: "The user email. Required if the authentication type is EMAIL.",
        example: "<EMAIL>",
        maxLength: 255,
        required: false,
    })
    @Transform((param) => param.value.toLowerCase())
    @IsEmail({}, { message: "Email must be a valid email address." })
    // @IsNotEmpty({ message: "Email is required." })
    @MaxLength(255, { message: "Email cannot exceed 255 characters." })
    @IsOptional()
    email?: string;

    @ApiProperty({
        description: "The user mobile number. Required if the authentication type is MOBILE.",
        example: "**********",
        //minLength: 10,
        //maxLength: 10,
        required: false,
    })
    @IsNotEmpty({ message: "Mobile number is required." })
    //@Length(10, 10, { message: "Mobile number must be exactly 10 digits." })
    mobile: string;

    @ApiProperty({
        description: "A strong password for the user account. Must meet the strong password criteria.",
        example: "Str0ngP@ss!",
    })
    @IsOptional()
    // @IsNotEmpty({ message: "Password is required." })
    // @IsStrongPassword({}, { message: "Password must meet the strong password criteria." })
    password: string;

    @ApiProperty({
        description: "A strong password for the user account. Must meet the strong password criteria.",
        example: "Str0ngP@ss!",
    })
    @IsOptional()
    // @IsNotEmpty({ message: "Confirm Password is required." })
    // @IsStrongPassword({}, { message: "Confirm Password must meet the strong password criteria." })
    confirmPassword: string;

    @ApiProperty({
        description: "The OTP verification code associated with the user. It must be a valid MongoDB ObjectId.",
        example: "507f191e810c19729de860ea",
    })
    @IsNotEmpty({ message: "OTP Verification code is required." })
    @IsMongoId({ message: "OTP Verification code must be a valid MongoDB ObjectId." })
    otpVerificationCode: string;

    @ApiProperty({
        description: "Organization id with which client is getting associated.",
        example: "507f191e810c19729de860ea",
    })
    @IsMongoId({ message: "Invalid organization details" })
    organizationId: string;

    @ApiProperty({
        description: "Facility id with which client is getting associated.",
        example: "507f191e810c19729de860ea",
    })
    @IsMongoId({ message: "Invalid facility details" })
    @IsOptional()
    facilityId?: string;

    @ApiProperty({
        description: "User term and condition acceptance status.",
        example:true,
    })
    @IsOptional()
    @IsBoolean()
    isUserAcceptTerms:boolean
    
     @ApiProperty({
        description: "country code for the Mobile",
        example: "+91",
        required: false
    })
    @ValidateIf((o) => o.mobile)
    @IsNotEmpty({ message: "Country Code is Required" })
    countryCode: string

}

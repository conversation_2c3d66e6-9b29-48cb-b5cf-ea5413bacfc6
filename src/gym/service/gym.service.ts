import { BadRequestException, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { ClientProfileDetails } from "src/users/schemas/user-profile-details.schema";
import { User } from "src/users/schemas/user.schema";
import { UploadService } from "src/utils/services/upload.service";
import { CreateGymDto } from "../dto/create-gym-profile.dto";
import * as bcrypt from "bcrypt";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { GymProfileDetails } from "../schemas/gym-profile-details.schema";
import { MailService } from "src/mail/services/mail.service";
import { PaginationDTO } from "src/users/dto/pagination.dto";
import { ConfigService } from "@nestjs/config";
import * as jwt from "jsonwebtoken";
import { GeneralService } from "src/utils/services/general.service";
import { TransactionService } from "src/utils/services/transaction.service";
import { UpdateGymDto } from "../dto/update-gym.dto";
import { RoleService } from "src/role/services/role.service";

@Injectable()
export class GymService {
    readonly adminFrontEndHost = this.configService.getOrThrow<string>("ADMIN_FRONTEND_APP_URL");
    constructor(
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(ClientProfileDetails.name) private ClientProfileModel: Model<ClientProfileDetails>,
        @InjectModel(GymProfileDetails.name) private GymProfileModel: Model<GymProfileDetails>,
        private readonly roleService: RoleService,
        private readonly mailService: MailService,
        private UploadService: UploadService,
        private readonly configService: ConfigService,
        private readonly transactionService: TransactionService,
        private readonly generalService: GeneralService,
    ) { }

    async adminRegisterGym(createGymReq: CreateGymDto): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const existingUser = await this.UserModel.findOne({
                $or: [{ email: createGymReq.email }, { mobile: createGymReq.mobile }],
            });

            if (existingUser) {
                throw new BadRequestException("Email or Mobile already exists");
            }

            const role = await this.roleService.findOneByType(ENUM_ROLE_TYPE.WEB_MASTER);

            const randomPassword = this.generalService.generateRandomPassword();
            const salt = await bcrypt.genSalt();
            const hashedPassword = await this.generalService.hashPassword(randomPassword, salt);

            const gymDetails = {
                name: createGymReq.gymName,
                mobile: createGymReq.mobile,
                email: createGymReq.email,
                role: role._id,
                salt,
                password: hashedPassword,
            };

            const createdGym = new this.UserModel(gymDetails);
            await createdGym.save({ session });

            const userId = new Types.ObjectId(createdGym._id);
            const gymId = this.generalService.generateGymId();
            createGymReq.address.cityId = new Types.ObjectId(createGymReq.address.cityId);
            createGymReq.address.stateId = new Types.ObjectId(createGymReq.address.stateId);

            const gymProfileDetails = {
                address: createGymReq.address,
                amenities: createGymReq?.amenities ? createGymReq?.amenities : [],
                description: createGymReq?.description || "",
                profilePicture: createGymReq?.profilePicture || "",
                gallery: createGymReq?.gallery || [],
                contactName: createGymReq?.contactName,
            };

            const createdGymProfile = new this.GymProfileModel({
                userId,
                gymId,
                ...gymProfileDetails,
            });
            await createdGymProfile.save({ session });
            const token = jwt.sign({ email: createdGym.email.toString() }, process.env.JWT_SECRET);
            const passwordResetUrl = this.adminFrontEndHost + `set-password?token=${token}`;
            await this.mailService.sendMail({
                to: createGymReq?.email.toString(),
                subject: `Welcome to ${createGymReq.gymName} - Set Your New Password`,
                template: "password-reset",
                context: {
                    name: createGymReq?.gymName,
                    email: createGymReq?.email,
                    dashboardUrl: passwordResetUrl,
                },
            });
            createdGym.password = createdGym.salt = undefined;
            await this.transactionService.commitTransaction(session);
            return {
                message: "Gym profile created successfully",
                data: createdGym,
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async adminGetGymDetailsById(userId: string): Promise<any> {
        try {
            const id = new Types.ObjectId(userId);
            const user = await this.UserModel.aggregate([
                {
                    $match: {
                        _id: new Types.ObjectId(id),
                    },
                },
                {
                    $lookup: {
                        from: "gymprofiledetails",
                        localField: "_id",
                        foreignField: "userId",
                        as: "gymDetails",
                    },
                },
                {
                    $unwind: "$gymDetails",
                },
                {
                    $project: {
                        password: 0,
                        salt: 0,
                        "gymDetails._id": 0,
                        "gymDetails.createdAt": 0,
                        "gymDetails.updatedAt": 0,
                    },
                },
            ]);

            if (user.length === 0) {
                return {
                    message: "Gym details not found",
                    data: [],
                };
            }
            return {
                message: "Gym details fetched successfully",
                data: user[0],
            };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    async adminGetAllGymList(paginationDTO: PaginationDTO): Promise<any> {
        try {
            let { page, pageSize, search } = paginationDTO;
            let query = { role: ENUM_ROLE_TYPE.ORGANIZATION };
            let searchQuery: any = [];
            if (isNaN(page) || page < 1) {
                page = 1;
            }
            if (isNaN(pageSize) || pageSize < 1) {
                pageSize = 10;
            }
            const skip = (page - 1) * pageSize;
            if (search?.length > 0) {
                searchQuery.push({ name: { $regex: search, $options: "i" } }, { mobile: { $regex: search, $options: "i" } }, { email: { $regex: search, $options: "i" } });
            }
            if (searchQuery.length > 0) {
                query["$or"] = searchQuery;
            }
            const result = await this.UserModel.aggregate([
                {
                    $match: query,
                },
                {
                    $sort: {
                        updatedAt: -1,
                    },
                },
                {
                    $lookup: {
                        from: "gymprofiledetails",
                        localField: "_id",
                        foreignField: "userId",
                        as: "gymDetails",
                    },
                },
                {
                    $unwind: {
                        path: "$gymDetails",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $addFields: {
                        "gymDetails.address.cityId": {
                            $toObjectId: "$gymDetails.address.cityId",
                        },
                    },
                },
                {
                    $lookup: {
                        from: "cities",
                        localField: "gymDetails.address.cityId",
                        foreignField: "_id",
                        as: "cityDetails",
                    },
                },
                {
                    $unwind: {
                        path: "$cityDetails",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $facet: {
                        metadata: [{ $count: "total" }],
                        data: [
                            {
                                $project: {
                                    _id: 1,
                                    userId: "$gymDetails.userId",
                                    gymId: "$gymDetails.gymId",
                                    name: 1,
                                    mobile: 1,
                                    email: 1,
                                    isActive: 1,
                                    city: "$cityDetails.name",
                                },
                            },
                            { $skip: skip },
                            { $limit: pageSize },
                        ],
                    },
                },
            ]);
            const totalUsers = result[0].metadata[0] ? result[0].metadata[0].total : 0;
            const totalPages = Math.ceil(totalUsers / pageSize);

            return {
                message: result[0].data.length > 0 ? "Gym list fetched successfully" : "Gym list not found",
                data: result[0].data,
                pagination: {
                    currentPage: page,
                    currentPageSize: pageSize,
                    totalPages: totalPages,
                    totalCount: totalUsers,
                },
            };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    async updateGym(updateGymDto: UpdateGymDto, userId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let userData = {
                name: updateGymDto.gymName,
                mobile: updateGymDto.mobile,
                email: updateGymDto.email,
            };

            let gymDetails = {
                address: updateGymDto.address,
                amenities: updateGymDto?.amenities ? updateGymDto?.amenities : [],
                description: updateGymDto?.description || "",
                profilePicture: updateGymDto?.profilePicture || "",
                gallery: updateGymDto?.gallery || [],
                contactName: updateGymDto?.contactName,
            };

            let updateGymDetailsProm = this.GymProfileModel.findOneAndUpdate(
                {
                    userId: userId,
                },
                {
                    $set: gymDetails,
                },
                {
                    new: true,
                    session,
                },
            );

            let updateUserProm = this.UserModel.findOneAndUpdate(
                {
                    _id: userId,
                },
                {
                    $set: userData,
                },
                {
                    new: true,
                    session,
                },
            );

            let [updateGymDetails, updateUser] = await Promise.all([updateGymDetailsProm, updateUserProm]);
            if (!updateGymDetails) throw new BadRequestException("Invalid gym details");
            if (!updateUser) throw new BadRequestException("Invalid gym details");
            await this.transactionService.commitTransaction(session);
            return {
                message: "Gym profile updated successfully",
                data: updateGymDetails,
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async updateGymByAdmin(updateGymDto: UpdateGymDto): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let userData = {
                name: updateGymDto.gymName,
                mobile: updateGymDto.mobile,
                email: updateGymDto.email,
            };

            let gymDetails = {
                address: updateGymDto.address,
                amenities: updateGymDto?.amenities ? updateGymDto?.amenities : [],
                description: updateGymDto?.description || "",
                profilePicture: updateGymDto?.profilePicture || "",
                gallery: updateGymDto?.gallery || [],
                contactName: updateGymDto?.contactName,
            };

            let updateGymDetailsProm = this.GymProfileModel.findOneAndUpdate(
                {
                    userId: updateGymDto.userId,
                },
                {
                    $set: gymDetails,
                },
                {
                    new: true,
                    session,
                },
            );

            let updateUserProm = this.UserModel.findOneAndUpdate(
                {
                    _id: updateGymDto.userId,
                },
                {
                    $set: userData,
                },
                {
                    new: true,
                    session,
                },
            );

            let [updateGymDetails, updateUser] = await Promise.all([updateGymDetailsProm, updateUserProm]);
            if (!updateGymDetails) throw new BadRequestException("Invalid gym details");
            if (!updateUser) throw new BadRequestException("Invalid gym details");
            await this.transactionService.commitTransaction(session);
            return {
                message: "Gym profile updated successfully",
                data: updateGymDetails,
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }
}

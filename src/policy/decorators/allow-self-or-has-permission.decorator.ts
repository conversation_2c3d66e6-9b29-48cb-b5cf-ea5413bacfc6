import { createParamDecorator, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Types } from 'mongoose';
import { ENUM_PERMISSION_TYPE } from '../enums/policy.permissions.enum';
import { IRequestApp } from 'src/common/request/interfaces/request.interface';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';

export const PolicyAllowIfSelfOrHasPermission = (
    field: string, permission: ENUM_PERMISSION_TYPE[],
    data?: 'self' | 'hasPermissions' | undefined
) =>
    createParamDecorator((_, ctx: ExecutionContext) => {
        const request: IRequestApp = ctx.switchToHttp().getRequest();

        const user = request.__user;

        if (!user) {
            throw new ForbiddenException('User not authenticated.');
        }

        if (user.role.type === ENUM_ROLE_TYPE.SUPER_ADMIN) {
            throw new ForbiddenException("Super admin not allowed to change organization data")
        }

        if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            return true;
        }

        const fromBody = request.body?.[field];
        const fromQuery = request.query?.[field];
        const fromParams = request.params?.[field];

        const value = fromBody || fromQuery || fromParams;

        let isSelf = false
        if (value && Array.isArray(value)) {
            isSelf = value.length == 1 && Types.ObjectId.isValid(value[0]) && new Types.ObjectId(user._id).equals(value[0])
        } else if (value && Types.ObjectId.isValid(value)) {
            isSelf = new Types.ObjectId(user._id).equals(new Types.ObjectId(value));
        }

        const hasPermission = permission.every((permission) => {
            return request.__permissionSet.has(permission)
        });

        const hasRolePermissions = permission.includes(user.role.type)

        if (!isSelf && !hasPermission && !hasRolePermissions) {
            throw new ForbiddenException(
                `You are not authorized to perform this action for another user.`
            );
        }
        if (data == 'self') {
            return isSelf;
        }
        if (data === 'hasPermissions') {
            return hasPermission;
        }
        return true;
    })();


export const PolicyHasPermission = (
    ...permission: ENUM_PERMISSION_TYPE[]
) =>
    createParamDecorator((_, ctx: ExecutionContext) => {
        const request: IRequestApp = ctx.switchToHttp().getRequest();

        const user = request.__user;

        if (!user) {
            throw new ForbiddenException('User not authenticated.');
        }

        if (user.role.type === ENUM_ROLE_TYPE.SUPER_ADMIN || user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            return true;
        }

        const hasPermission = permission.every((permission) => {
            return request.__permissionSet.has(permission)
        });

        return hasPermission;
    })();

import {
    Injectable,
    CanActivate,
    ExecutionContext,
    ForbiddenException,
} from '@nestjs/common';
import { ENUM_SESSION_STATUS_CODE_ERROR } from '../enums/session.status-code.enum';
import { IRequestApp } from 'src/common/request/interfaces/request.interface';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { OrganizationSettingService } from 'src/organizationSettings/services/organization-settings.service';

@Injectable()
export class SessionActiveGuard implements CanActivate {

    constructor(
        private readonly organizationSettingService: OrganizationSettingService,
    ) { }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const req = context.switchToHttp().getRequest<IRequestApp>();
        const { user, __isDelegateSessionActive } = context.switchToHttp().getRequest<IRequestApp>();
        const { type } = user;


        if (type == ENUM_ROLE_TYPE.ORGANIZATION || type == ENUM_ROLE_TYPE.SUPER_ADMIN) {
            req.__sessionId = "";
            req.__isSessionActive = true;
            req.__delegateSessionId = "";
            req.__isDelegateSessionActive = true;
            req.__delegateUser = req.__user;

            return true;
        }
        const isOrganizationSettingEnabled = await this.organizationSettingService.isOrganizationSettingEnabled(user.organization, "settings_pin");

        if (!isOrganizationSettingEnabled) {
            req.__sessionId = "";
            req.__isSessionActive = true;
            req.__delegateSessionId = "";
            req.__isDelegateSessionActive = true;
            req.__delegateUser = req.__user;

            return true;
        }

        if (!__isDelegateSessionActive) {
            throw new ForbiddenException({
                statusCode: ENUM_SESSION_STATUS_CODE_ERROR.SESSION_EXPIRED,
                message: 'session.error.notFound',
            });
        }

        return true
    }
}




// import {
//     Injectable,
//     CanActivate,
//     ExecutionContext,
//     ForbiddenException,
//     Logger,
// } from '@nestjs/common';
// import { SessionService } from '../services/session.service';
// import { ENUM_SESSION_STATUS_CODE_ERROR } from '../enums/session.status-code.enum';
// import { Types } from 'mongoose';
// import { UserService } from 'src/users/services/user.service';
// import { ENUM_USER_STATUS_CODE_ERROR } from 'src/users/enums/user.status-code.enum';
// import { ENUM_ROLE_STATUS_CODE_ERROR } from 'src/role/enums/role.status-code.enum';
// import { IRequestApp } from 'src/common/request/interfaces/request.interface';

// @Injectable()
// export class SessionActiveGuard implements CanActivate {
//     private readonly logger = new Logger(SessionActiveGuard.name);

//     constructor(
//         private readonly sessionService: SessionService,
//         private readonly userService: UserService,
//     ) {}

//     /**
//      * Validates the session and user status for the incoming request.
//      * @param context - The execution context of the request.
//      * @returns A boolean indicating whether the session and user are active.
//      * @throws ForbiddenException if the session or user is inactive.
//      */
//     async canActivate(context: ExecutionContext): Promise<boolean> {
//         const request = context.switchToHttp().getRequest<IRequestApp>();
//         const { organization : organizationId} = request.user;

//         // Initialize session-related properties
//         request.__sessionId = "";
//         request.__isSessionActive = false;
//         request.__delegateSessionId = "";
//         request.__isDelegateSessionActive = false;
//         request.__organizationId = false;

//         try {
//             const userId = request.session?.userId;
//             const userSessionId = request.session?.userSessionId;
//             const delegatedUserId = request.session?.userDelegatedId;
//             const delegatedSessionId = request.session?.userDelegatedSessionId;

//             // Check main user session
//             if (userId && userSessionId) {
//                 const isActive = await this.sessionService.checkSessionActivity(
//                     new Types.ObjectId(userSessionId)
//                 );
//                 request.__isSessionActive = isActive;

//                 if (!isActive) {
//                     throw new ForbiddenException({
//                         statusCode: ENUM_SESSION_STATUS_CODE_ERROR.SESSION_EXPIRED,
//                         message: 'Session has expired',
//                     });
//                 }

//                 const user = await this.sessionService.getLoginSessionUser(
//                     new Types.ObjectId(userSessionId)
//                 );

//                 // Validate user status
//                 if (!user.isActive) {
//                     throw new ForbiddenException({
//                         statusCode: ENUM_USER_STATUS_CODE_ERROR.INACTIVE_FORBIDDEN,
//                         message: 'User inactive',
//                     });
//                 }

//                 // Validate user role
//                 const userWithRole = await this.userService.findOneWithRoleAndPermissions({
//                     _id: user._id,
//                 });

//                 if (!userWithRole?.role?.isActive) {
//                     throw new ForbiddenException({
//                         statusCode: ENUM_ROLE_STATUS_CODE_ERROR.INACTIVE_FORBIDDEN,
//                         message: 'Role inactive',
//                     });
//                 }

//                 request.__sessionId = userSessionId;
//                 request.__user = userWithRole;
//             } else {
//                 throw new ForbiddenException({
//                     statusCode: ENUM_SESSION_STATUS_CODE_ERROR.NOT_FOUND,
//                     message: 'Session not found',
//                 });
//             }

//             // Check delegated user session if exists
//             if (delegatedUserId && delegatedSessionId) {
//                 const isActive = await this.sessionService.checkSessionActivity(
//                     new Types.ObjectId(delegatedSessionId)
//                 );
//                 request.__isDelegateSessionActive = isActive;

//                 if (isActive) {
//                     const delegateUser = await this.sessionService.getLoginSessionUser(
//                         new Types.ObjectId(delegatedSessionId)
//                     );

//                     // Validate delegate user status
//                     if (!delegateUser.isActive) {
//                         throw new ForbiddenException({
//                             statusCode: ENUM_USER_STATUS_CODE_ERROR.INACTIVE_FORBIDDEN,
//                             message: 'Inactive delegate user',
//                         });
//                     }

//                     // Validate delegate user role
//                     const delegateUserWithRole = await this.userService.findOneWithRoleAndPermissions({
//                         _id: delegateUser._id,
//                     });

//                     if (!delegateUserWithRole.role.isActive) {
//                         throw new ForbiddenException({
//                             statusCode: ENUM_ROLE_STATUS_CODE_ERROR.INACTIVE_FORBIDDEN,
//                             message: 'Inactive delegate user role',
//                         });
//                     }

//                     request.__delegateSessionId = delegatedSessionId;
//                     request.__delegateUser = delegateUserWithRole;
//                 }
//             }



//             return true;
//         } catch (error) {
//             this.logger.error(`Session validation failed: ${error.message}`);
//             throw error;
//         }
//     }
// }

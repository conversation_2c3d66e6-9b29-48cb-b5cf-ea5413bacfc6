import { InjectQueue } from '@nestjs/bullmq';
import { Types } from 'mongoose';
import { Logger } from 'nestjs-pino'
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { ForbiddenException, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Queue } from 'bullmq';
import { Cache } from 'cache-manager';
import { instanceToPlain, plainToClass, plainToInstance } from 'class-transformer';
import { Request } from 'express';
import { Duration } from 'luxon';
import { Document } from 'mongoose';
import { CachingService } from 'src/common/caching/services/caching.service';
import {
    IDatabaseCreateOptions,
    IDatabaseDeleteManyOptions,
    IDatabaseFindAllOptions,
    IDatabaseFindOneOptions,
    IDatabaseGetTotalOptions,
    IDatabaseOptions,
    IDatabaseUpdateManyOptions,
} from 'src/common/database/interfaces/database.interface';
import { HelperDateService } from 'src/common/helper/services/helper.date.service';
import { SessionActivePrefix, SessionLoginPrefix } from 'src/session/constants/session.constant';
import { SessionCreateRequestDto } from 'src/session/dtos/request/session.create.request.dto';
import { SessionListResponseDto } from 'src/session/dtos/response/session.list.response.dto';
import {
    ENUM_SESSION_PROCESS,
    ENUM_SESSION_STATUS,
} from 'src/session/enums/session.enum';
import {
    SessionDoc,
    SessionEntity,
} from 'src/session/repository/entities/session.entity';
import { SessionRepository } from 'src/session/repository/repositories/session.repository';
import { IUserDocument } from 'src/users/interfaces/user.interface';
import { ENUM_WORKER_QUEUES } from 'src/worker/enums/worker.enum';
import { IPolicyDocument } from 'src/policy/interfaces/policy.interface';
import { IRoleDocument } from 'src/role/interfaces/role.interface';
import { UserService } from 'src/users/services/user.service';
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';

@Injectable()
export class SessionService {
    private readonly sessionExpirationTime: number;
    private readonly appNameSlug: string;
    private readonly sessionInactivityTime: number | null; // null means no inactivity timeout

    constructor(
        private readonly logger: Logger,
        @InjectQueue(ENUM_WORKER_QUEUES.SESSION_QUEUE) private readonly sessionQueue: Queue,
        @Inject(CACHE_MANAGER) private cacheManager: Cache,
        private readonly configService: ConfigService,
        private readonly helperDateService: HelperDateService,
        private readonly sessionRepository: SessionRepository,
        private readonly cachingService: CachingService,
        private readonly userService: UserService
    ) {
        this.sessionExpirationTime =  30 * 1000; //this.configService.get<number>('auth.session.maxAge');

        // Set to null to disable inactivity timeout for browser-session based sessions
        this.sessionInactivityTime = null;

        this.appNameSlug = this.configService.get<string>('app.nameSlug');
    }

    async findAll(
        find?: Record<string, any>,
        options?: IDatabaseFindAllOptions
    ): Promise<SessionDoc[]> {
        return this.sessionRepository.findAll<SessionDoc>(find, options);
    }

    async findAllByUser(
        user: Types.ObjectId,
        find?: Record<string, any>,
        options?: IDatabaseFindAllOptions
    ): Promise<SessionDoc[]> {
        return this.sessionRepository.findAll<SessionDoc>(
            { user, ...find },
            options
        );
    }

    async findOneById(
        _id: Types.ObjectId,
        options?: IDatabaseFindOneOptions
    ): Promise<SessionDoc> {
        return this.sessionRepository.findOneById<SessionDoc>(_id, options);
    }

    async findOne(
        find: Record<string, any>,
        options?: IDatabaseFindOneOptions
    ): Promise<SessionDoc> {
        return this.sessionRepository.findOne<SessionDoc>(find, options);
    }

    async findOneActiveById(
        _id: Types.ObjectId,
        options?: IDatabaseFindOneOptions
    ): Promise<SessionDoc> {
        const today = this.helperDateService.create();

        return this.sessionRepository.findOne<SessionDoc>(
            {
                _id,
                expiredAt: {
                    $gte: today,
                },
            },
            options
        );
    }

    async findOneActiveByIdAndUser(
        _id: Types.ObjectId,
        user: string,
        options?: IDatabaseFindOneOptions
    ): Promise<SessionDoc> {
        const today = this.helperDateService.create();

        return this.sessionRepository.findOne<SessionDoc>(
            {
                _id,
                user,
                expiredAt: {
                    $gte: today,
                },
            },
            options
        );
    }

    async getTotal(
        find?: Record<string, any>,
        options?: IDatabaseGetTotalOptions
    ): Promise<number> {
        return this.sessionRepository.getTotal(find, options);
    }

    async getTotalByUser(
        user: Types.ObjectId,
        options?: IDatabaseGetTotalOptions
    ): Promise<number> {
        return this.sessionRepository.getTotal({ user }, options);
    }

    async create(
        request: Request,
        { user }: SessionCreateRequestDto,
        options?: IDatabaseCreateOptions
    ): Promise<SessionDoc> {
        const today = this.helperDateService.create();
        const expiredAt: Date = this.helperDateService.forward(
            today,
            Duration.fromObject({
                seconds: this.sessionExpirationTime,
            })
        );

        const create = new SessionEntity();
        create.user = user;
        create.hostname = request.hostname;
        create.ip = request.ip;
        create.protocol = request.protocol;
        create.originalUrl = request.originalUrl;
        create.method = request.method;

        create.userAgent = request.headers['user-agent'] as string;
        create.xForwardedFor = request.headers['x-forwarded-for'] as string;
        create.xForwardedHost = request.headers['x-forwarded-host'] as string;
        create.xForwardedPorto = request.headers['x-forwarded-porto'] as string;

        create.status = ENUM_SESSION_STATUS.ACTIVE;
        create.expiredAt = expiredAt;
        create.lastActivityAt = today;

        return this.sessionRepository.create<SessionEntity>(create, options);
    }

    mapList(
        userLogins: SessionDoc[] | SessionEntity[]
    ): SessionListResponseDto[] {
        return plainToInstance(
            SessionListResponseDto,
            userLogins.map((e: SessionDoc | SessionEntity) =>
                e instanceof Document ? e.toObject() : e
            )
        );
    }

    async findLoginSession(_id: Types.ObjectId): Promise<string> {
        return this.cacheManager.get<string>(
            `${this.appNameSlug}:${SessionLoginPrefix}:${_id}`
        );
    }

    private serializeUser(user: IUserDocument): Record<string, any> {
        return JSON.parse(JSON.stringify(user));
    }

    private deserializeUser(userData: Record<string, any>): IUserDocument | null {
            return {
                ...userData,
                _id: new Types.ObjectId(userData._id),
                email: userData.email,
                name: userData.name,
                firstName: userData.firstName,
                lastName: userData.lastName,
                mobile: userData.mobile,
                countryCode: userData.countryCode,
                isActive: userData.isActive,
                newUser: userData.newUser,
                role: {
                    ...userData.role,
                    _id: new Types.ObjectId(userData.role._id),
                    type: userData.role.type,
                    isActive: userData.role.isActive,
                    policies: userData.role.policies.map((policy: IPolicyDocument) => (
                        {
                            ...policy,
                            _id: new Types.ObjectId(policy._id),
                            permissions: policy.permissions.map((permission) => ({
                                ...permission,
                                _id: new Types.ObjectId(permission._id)

                            }))
                        }
                    )),
                    createdAt: new Date(userData.role.createdAt),
                    updatedAt: new Date(userData.role.updatedAt),
                } as IRoleDocument,
                assignedPolicies: userData.assignedPolicies?.map((policy: IPolicyDocument) =>(
                    {
                        ...policy,
                        _id: new Types.ObjectId(policy._id),
                        permissions: policy.permissions.map((permission) => ({
                            ...permission,
                            _id: new Types.ObjectId(permission._id)

                        })),
                        createdAt: new Date(userData.role.createdAt),
                        updatedAt: new Date(userData.role.updatedAt),
                    }
                )),
                restrictedPolicies: userData.restrictedPolicies?.map((policy: IPolicyDocument) =>
                    ({
                        _id: new Types.ObjectId(policy._id),
                        permissions:  policy.permissions.map((permission) => ({
                            ...permission,
                            _id: new Types.ObjectId(permission._id)

                        })),
                        createdAt: new Date(userData.role.createdAt),
                        updatedAt: new Date(userData.role.updatedAt),
                    })
                ),
                permissions: userData.permissions,
                createdAt: new Date(userData.createdAt),
                updatedAt: new Date(userData.updatedAt),
            } as IUserDocument;
    }

    async cacheLoginSession(user: IUserDocument, session: SessionDoc): Promise<void> {
        const key = `${this.appNameSlug}:${SessionLoginPrefix}:${session._id}`;
        const keyUser = `${this.appNameSlug}:${SessionLoginPrefix}:${user._id}`;

        try {
            const serializedUser = this.serializeUser(user);

            await Promise.all([await this.cachingService.set(
                key,
                JSON.stringify(serializedUser),
                this.sessionExpirationTime
            ),
            await this.cachingService.set(
                keyUser,
                JSON.stringify(serializedUser),
                this.sessionExpirationTime
            )]);

        } catch (error) {
            this.logger.error(`Failed to serialize session data: ${error.message}`);
        }
    }

    async getLoginSessionUser(_id: Types.ObjectId): Promise<IUserDocument | null> {
        const key = `${this.appNameSlug}:${SessionLoginPrefix}:${_id.toString()}`;
        const userData = await this.cachingService.get<string>(key);
        if (userData){
            return this.deserializeUser(JSON.parse(userData)) as IUserDocument;
        };
        const session = await this.findOneById(_id);
        if (!session){
            throw new ForbiddenException(`Unable to restore session`);;
        };
        const dbUser = await this.userService.findOneWithRoleAndPermissions(session.user);
        await this.cacheLoginSession(dbUser, session);
        return dbUser

    }

    async deleteLoginSession(_id: Types.ObjectId): Promise<void> {
        const key = `${this.appNameSlug}:${SessionLoginPrefix}:${_id}`;
        await this.cacheManager.del(key);

        await this.sessionQueue.remove(key);

        return;
    }

    async resetLoginSession(): Promise<void> {
        await this.cacheManager.clear();

        return;
    }

    async updateRevoke(
        repository: SessionDoc,
        options?: IDatabaseOptions
    ): Promise<SessionDoc> {
        await this.deleteLoginSession(repository._id);

        repository.status = ENUM_SESSION_STATUS.REVOKED;
        repository.revokeAt = this.helperDateService.create();

        return this.sessionRepository.save(repository, options);
    }

    async updateManyRevokeByUser(
        user: Types.ObjectId,
        options?: IDatabaseUpdateManyOptions
    ): Promise<boolean> {
        const today = this.helperDateService.create();
        const sessions = await this.findAllByUser(user, undefined, options);
        const promises = sessions.map(e => this.deleteLoginSession(e._id));

        await Promise.all(promises);

        await this.sessionRepository.updateMany(
            {
                user,
            },
            {
                status: ENUM_SESSION_STATUS.REVOKED,
                revokeAt: today,
            },
            options
        );

        return true;
    }

    async deleteMany(
        find: Record<string, any>,
        options?: IDatabaseDeleteManyOptions
    ): Promise<boolean> {
        await this.sessionRepository.deleteMany(find, options);

        return true;
    }

    async checkSessionActivity(sessionId: Types.ObjectId): Promise<boolean> {
        const activeKey = `${this.appNameSlug}:${SessionActivePrefix}:${sessionId}`;

        // First check cache for active status
        let isActive = await this.cachingService.get<boolean>(activeKey);

        // If active in cache, return true immediately
        if (isActive === true) {
            return true;
        }

        // If not in cache or not active, check DB
        const session = await this.findOneById(sessionId);
        if (!session) return false;

        // Check if session is explicitly revoked
        if (session.status === ENUM_SESSION_STATUS.REVOKED) {
            return false;
        }

        // For browser-session based sessions, only check if session exists and is not revoked
        // No inactivity timeout check since sessions should last until browser closes
        if (this.sessionInactivityTime === null) {
            isActive = true;
        } else {
            // Legacy behavior: check inactivity timeout
            const now = this.helperDateService.create();
            const lastActivity = session.lastActivityAt || session.createdAt;
            const inactiveTime = Math.floor((now.getTime() - lastActivity.getTime()));
            isActive = inactiveTime <= this.sessionInactivityTime;
        }

        if (isActive) {
            // Cache the active status (use a reasonable TTL for cache efficiency)
            const cacheTTL = this.sessionInactivityTime || 300000; // 5 minutes default for cache
            await this.cachingService.set(
                activeKey,
                true,
                cacheTTL
            );

            // Queue session refresh in the background
            this.queueSessionRefresh(sessionId).catch(err => {
                this.logger.error(`Failed to queue session refresh: ${err.message}`);
            });
        } else {
            // Revoke the session in the background (only for legacy timeout-based sessions)
            this.updateRevoke(session).catch(err => {
                this.logger.error(`Failed to revoke session: ${err.message}`);
            });
        }

        return isActive;
    }

    async checkDelegateSessionActivity(sessionId: Types.ObjectId): Promise<boolean> {
        // Use the same implementation as checkSessionActivity for consistency
        return this.checkSessionActivity(sessionId);
    }

    private async extendSessionExpiry(session: SessionDoc): Promise<void> {
        const now = this.helperDateService.create();
        const newExpiryDate = this.helperDateService.forward(
            now,
            this.sessionExpirationTime
        );

        // Get the last expiry update time from cache
        const lastExpiryUpdateKey = `${this.appNameSlug}:${SessionActivePrefix}:${session._id}:lastExpiryUpdate`;
        const lastExpiryUpdate = await this.cachingService.get<number>(lastExpiryUpdateKey);
        const currentTime = now.getTime();

        // Only update the database if:
        // 1. The new expiry would be later than current expiry AND
        // 2. It's been at least 10 seconds since the last expiry update OR there's no record of a previous update
        if (newExpiryDate > session.expiredAt &&
            (!lastExpiryUpdate || (currentTime - lastExpiryUpdate) > 10000)) {

            // Update session document
            await this.sessionRepository.update(
                { _id: session._id },
                {
                    $set: {
                        expiredAt: newExpiryDate,
                        lastActivityAt: now
                    }
                }
            );

            // Record the time of this expiry update
            await this.cachingService.set(
                lastExpiryUpdateKey,
                currentTime,
                this.sessionInactivityTime
            );
        }

        // Always update cache expiry regardless of database update
        const loginKey = `${this.appNameSlug}:${SessionLoginPrefix}:${session._id}`;
        const userData = await this.cachingService.get<string>(loginKey);
        if (userData) {
            await this.cachingService.set(
                loginKey,
                userData,
                Math.floor(this.sessionExpirationTime / 1000) // Convert to seconds
            );
        }

        // Always update active status cache
        const activeKey = `${this.appNameSlug}:${SessionActivePrefix}:${session._id}`;
        await this.cachingService.set(
            activeKey,
            true,
            this.sessionInactivityTime
        );
    }

    private async queueSessionRefresh(sessionId: IDatabaseObjectId): Promise<void> {
        const refreshQueuedKey = `${this.appNameSlug}:${SessionActivePrefix}:${sessionId}:refreshQueued`;
        const isRefreshQueued = await this.cachingService.get<boolean>(refreshQueuedKey);

        // Only queue a refresh job if one isn't already queued
        if (!isRefreshQueued) {
            // Mark that a refresh job has been queued
            await this.cachingService.set(
                refreshQueuedKey,
                true,
                10000 // 10 seconds TTL for this flag
            );

            await this.sessionQueue.add(
                ENUM_SESSION_PROCESS.REFRESH,
                {
                    sessionId
                },
                {
                    removeOnComplete: true,
                    removeOnFail: true,
                    delay: 5000, // 5 seconds delay to batch potential updates
                    jobId: `session-refresh-${sessionId}` // Ensure only one refresh job per session
                }
            );
        }
    }

    async refreshSession(sessionId: Types.ObjectId): Promise<void> {
        const session = await this.findOneById(sessionId);
        if (!session) return;

        await this.extendSessionExpiry(session);
    }

    async updateSessionActivity(sessionId: Types.ObjectId): Promise<void> {
        const now = this.helperDateService.create();
        const activeKey = `${this.appNameSlug}:${SessionActivePrefix}:${sessionId}`;
        const lastUpdateKey = `${this.appNameSlug}:${SessionActivePrefix}:${sessionId}:lastDbUpdate`;

        // Always update the active status in cache
        const cacheTTL = this.sessionInactivityTime || 300000; // 5 minutes default for cache
        await this.cachingService.set(
            activeKey,
            true,
            cacheTTL
        );

        // Check when the last database update occurred
        const lastDbUpdate = await this.cachingService.get<number>(lastUpdateKey);
        const currentTime = now.getTime();

        // Only update the database every 10 seconds
        if (!lastDbUpdate || (currentTime - lastDbUpdate) > 10000) {
            // Get the session from the database
            const session = await this.findOneById(sessionId);
            if (!session) return;

            // Update lastActivityAt in the database
            await this.sessionRepository.update(
                { _id: session._id },
                { $set: { lastActivityAt: now } }
            );

            // Update the last database update timestamp in cache
            await this.cachingService.set(
                lastUpdateKey,
                currentTime,
                cacheTTL
            );

            // Queue session refresh to extend expiry if needed (only for timeout-based sessions)
            if (this.sessionInactivityTime !== null) {
                await this.queueSessionRefresh(sessionId);
            }
        }
    }
}

import { Type } from "class-transformer";
import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { IsInt, IsOptional, <PERSON>, <PERSON> } from "class-validator";

export class PaginationDto {

    @ApiProperty({
        description: "Search query to filter results.",
        example: "Search",
        required: false,
    })
    @IsOptional()
    @Type(() => String)
    search?: string;

    @ApiProperty({
        description: "Number of items per page.",
        example: 10,
        minimum: 1,
        maximum: 50,
        required: false,
    })
    @IsOptional()
    @IsInt()
    @Min(1)
    @Max(1000)
    @Type(() => Number)
    pageSize?: number = 20;

    @ApiProperty({
        description: "Page number to jump to.",
        example: 1,
        minimum: 1,
        required: false,
    })
    @IsOptional()
    @IsInt()
    @Min(1)
    @Type(() => Number)
    page?: number = 1;

    @ApiProperty({
        description: "Order by field.",
        example: "createdAt",
        required: false,
    })
    @IsOptional()
    orderBy?: string = 'name';

    @ApiProperty({
        description: "Order direction.",
        example: "asc",
        required: false,
    })
    @IsOptional()
    orderDirection?: "asc" | "desc" = "desc";

    @ApiHideProperty()
    @IsOptional()
    _search?: any;

    @ApiHideProperty()
    @IsOptional()
    _limit?: number = 20;

    @ApiHideProperty()
    @IsOptional()
    _offset?: number = 0;

    @ApiHideProperty()
    @IsOptional()
    _order?: { [key: string]: 1 | -1 };

}
